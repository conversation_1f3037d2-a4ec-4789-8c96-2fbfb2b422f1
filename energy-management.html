<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>能源管理系统 - 广州农行智慧楼宇</title>
  
  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- 引入Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  
  <!-- 引入主样式 -->
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div id="sidebarContainer"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部导航 -->
      <div id="headerContainer"></div>

      <!-- 页面内容 -->
      <div class="page-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">能源管理系统</h1>
          <p class="page-description">抄表数据采集、能耗趋势图表、用能超标告警、用能分摊设置</p>
        </div>

        <!-- 能耗统计 -->
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">1,245</div>
                <div class="stat-label">今日用电(kWh)</div>
                <div class="stat-change negative">
                  <i class="fas fa-arrow-down"></i>
                  <span>较昨日 -5%</span>
                </div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-bolt"></i>
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">今日用水(m³)</div>
                <div class="stat-change positive">
                  <i class="fas fa-arrow-up"></i>
                  <span>较昨日 +2%</span>
                </div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-tint"></i>
              </div>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">89</div>
                <div class="stat-label">今日用气(m³)</div>
                <div class="stat-change positive">
                  <i class="fas fa-arrow-up"></i>
                  <span>较昨日 +8%</span>
                </div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-fire"></i>
              </div>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">¥2,456</div>
                <div class="stat-label">今日费用</div>
                <div class="stat-change negative">
                  <i class="fas fa-arrow-down"></i>
                  <span>较昨日 -3%</span>
                </div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-yen-sign"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 能耗趋势图表 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">用电趋势</h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-sm btn-secondary">日</button>
                <button class="btn btn-sm btn-primary">周</button>
                <button class="btn btn-sm btn-secondary">月</button>
              </div>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="electricChart"></canvas>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">用水趋势</h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-sm btn-secondary">日</button>
                <button class="btn btn-sm btn-primary">周</button>
                <button class="btn btn-sm btn-secondary">月</button>
              </div>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="waterChart"></canvas>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">能耗分布</h3>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="distributionChart"></canvas>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">费用统计</h3>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="costChart"></canvas>
              </div>
            </div>
          </div>
        </div>

        <!-- 实时监控和告警 -->
        <div class="dashboard-grid">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">实时监控</h3>
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
            </div>
            <div class="card-body">
              <div class="device-grid">
                <div class="device-card">
                  <div class="device-header">
                    <div class="device-info">
                      <div class="device-icon" style="background: var(--primary-color);">
                        <i class="fas fa-bolt"></i>
                      </div>
                      <div>
                        <div class="device-name">1号楼总电表</div>
                        <div class="device-location">1号楼配电室</div>
                      </div>
                    </div>
                    <div class="status-badge success">正常</div>
                  </div>
                  <div class="device-body">
                    <div class="device-params">
                      <div class="param-item">
                        <span class="param-label">当前功率</span>
                        <span class="param-value">156 kW</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">今日用电</span>
                        <span class="param-value">1,245 kWh</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">电压</span>
                        <span class="param-value">380 V</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">电流</span>
                        <span class="param-value">245 A</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="device-card">
                  <div class="device-header">
                    <div class="device-info">
                      <div class="device-icon" style="background: var(--info-color);">
                        <i class="fas fa-tint"></i>
                      </div>
                      <div>
                        <div class="device-name">主水表</div>
                        <div class="device-location">地下室水表间</div>
                      </div>
                    </div>
                    <div class="status-badge success">正常</div>
                  </div>
                  <div class="device-body">
                    <div class="device-params">
                      <div class="param-item">
                        <span class="param-label">瞬时流量</span>
                        <span class="param-value">12.5 m³/h</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">今日用水</span>
                        <span class="param-value">156 m³</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">水压</span>
                        <span class="param-value">0.35 MPa</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">温度</span>
                        <span class="param-value">18°C</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="device-card">
                  <div class="device-header">
                    <div class="device-info">
                      <div class="device-icon" style="background: var(--warning-color);">
                        <i class="fas fa-fire"></i>
                      </div>
                      <div>
                        <div class="device-name">燃气表</div>
                        <div class="device-location">锅炉房</div>
                      </div>
                    </div>
                    <div class="status-badge warning">告警</div>
                  </div>
                  <div class="device-body">
                    <div class="device-params">
                      <div class="param-item">
                        <span class="param-label">瞬时流量</span>
                        <span class="param-value">8.9 m³/h</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">今日用气</span>
                        <span class="param-value">89 m³</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">压力</span>
                        <span class="param-value">0.25 MPa</span>
                      </div>
                      <div class="param-item">
                        <span class="param-label">温度</span>
                        <span class="param-value">22°C</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">能耗告警</h3>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-cog"></i>
                告警设置
              </button>
            </div>
            <div class="card-body p-0">
              <div class="recent-list">
                <div class="recent-item">
                  <div class="recent-icon" style="background: #f59e0b">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">用电量超标</div>
                    <div class="recent-desc">1号楼今日用电量超过预设阈值20%</div>
                  </div>
                  <div class="recent-time">5分钟前</div>
                </div>

                <div class="recent-item">
                  <div class="recent-icon" style="background: #ef4444">
                    <i class="fas fa-fire"></i>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">燃气压力异常</div>
                    <div class="recent-desc">锅炉房燃气表压力低于安全值</div>
                  </div>
                  <div class="recent-time">15分钟前</div>
                </div>

                <div class="recent-item">
                  <div class="recent-icon" style="background: #06b6d4">
                    <i class="fas fa-tint"></i>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">用水量异常</div>
                    <div class="recent-desc">2号楼用水量突然增加，疑似漏水</div>
                  </div>
                  <div class="recent-time">1小时前</div>
                </div>

                <div class="recent-item">
                  <div class="recent-icon" style="background: #10b981">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">告警恢复</div>
                    <div class="recent-desc">3号楼电压异常已恢复正常</div>
                  </div>
                  <div class="recent-time">2小时前</div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">分摊设置</h3>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i>
                新增规则
              </button>
            </div>
            <div class="card-body">
              <div class="table-container">
                <table class="table">
                  <thead>
                    <tr>
                      <th>区域</th>
                      <th>分摊比例</th>
                      <th>本月费用</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>1号楼</td>
                      <td>35%</td>
                      <td>¥8,560</td>
                      <td>
                        <button class="btn btn-sm btn-secondary">编辑</button>
                      </td>
                    </tr>
                    <tr>
                      <td>2号楼</td>
                      <td>30%</td>
                      <td>¥7,340</td>
                      <td>
                        <button class="btn btn-sm btn-secondary">编辑</button>
                      </td>
                    </tr>
                    <tr>
                      <td>3号楼</td>
                      <td>25%</td>
                      <td>¥6,120</td>
                      <td>
                        <button class="btn btn-sm btn-secondary">编辑</button>
                      </td>
                    </tr>
                    <tr>
                      <td>公共区域</td>
                      <td>10%</td>
                      <td>¥2,450</td>
                      <td>
                        <button class="btn btn-sm btn-secondary">编辑</button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">抄表计划</h3>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-calendar-plus"></i>
                新增计划
              </button>
            </div>
            <div class="card-body">
              <div class="table-container">
                <table class="table">
                  <thead>
                    <tr>
                      <th>表计名称</th>
                      <th>抄表周期</th>
                      <th>下次抄表</th>
                      <th>状态</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>1号楼总电表</td>
                      <td>每日</td>
                      <td>2024-01-16 08:00</td>
                      <td><span class="status-badge success">正常</span></td>
                    </tr>
                    <tr>
                      <td>主水表</td>
                      <td>每日</td>
                      <td>2024-01-16 08:00</td>
                      <td><span class="status-badge success">正常</span></td>
                    </tr>
                    <tr>
                      <td>燃气表</td>
                      <td>每周</td>
                      <td>2024-01-20 09:00</td>
                      <td><span class="status-badge warning">待抄表</span></td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入主JavaScript -->
  <script src="assets/js/main.js"></script>
  
  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载组件
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      
      // 初始化图表
      initEnergyCharts();
      
      // 更新面包屑
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('能源管理系统');
        }
      }, 100);
    });

    // 加载组件
    function loadComponent(containerId, componentPath) {
      fetch(componentPath)
        .then(response => response.text())
        .then(html => {
          document.getElementById(containerId).innerHTML = html;
        })
        .catch(error => {
          console.error('Error loading component:', error);
        });
    }

    // 初始化能源图表
    function initEnergyCharts() {
      // 用电趋势图
      const electricCtx = document.getElementById('electricChart').getContext('2d');
      new Chart(electricCtx, {
        type: 'line',
        data: {
          labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          datasets: [{
            label: '用电量 (kWh)',
            data: [1200, 1350, 1180, 1420, 1245, 980, 1100],
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: { legend: { display: false } },
          scales: {
            y: { beginAtZero: true, grid: { color: '#f3f4f6' } },
            x: { grid: { display: false } }
          }
        }
      });

      // 用水趋势图
      const waterCtx = document.getElementById('waterChart').getContext('2d');
      new Chart(waterCtx, {
        type: 'bar',
        data: {
          labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          datasets: [{
            label: '用水量 (m³)',
            data: [120, 135, 118, 142, 156, 98, 110],
            backgroundColor: '#06b6d4',
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: { legend: { display: false } },
          scales: {
            y: { beginAtZero: true, grid: { color: '#f3f4f6' } },
            x: { grid: { display: false } }
          }
        }
      });

      // 能耗分布图
      const distributionCtx = document.getElementById('distributionChart').getContext('2d');
      new Chart(distributionCtx, {
        type: 'doughnut',
        data: {
          labels: ['照明', '空调', '电梯', '其他'],
          datasets: [{
            data: [30, 45, 15, 10],
            backgroundColor: ['#3b82f6', '#10b981', '#f59e0b', '#6b7280'],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { position: 'bottom', labels: { padding: 20, usePointStyle: true } }
          }
        }
      });

      // 费用统计图
      const costCtx = document.getElementById('costChart').getContext('2d');
      new Chart(costCtx, {
        type: 'bar',
        data: {
          labels: ['电费', '水费', '燃气费'],
          datasets: [{
            label: '费用 (元)',
            data: [1800, 450, 320],
            backgroundColor: ['#3b82f6', '#06b6d4', '#f59e0b'],
            borderRadius: 4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: { legend: { display: false } },
          scales: {
            y: { beginAtZero: true, grid: { color: '#f3f4f6' } },
            x: { grid: { display: false } }
          }
        }
      });
    }
  </script>
</body>
</html>
