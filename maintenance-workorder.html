<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>维保与工单系统 - 广州农行智慧楼宇</title>
  
  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- 引入主样式 -->
  <link rel="stylesheet" href="assets/css/main.css">
  
  <style>
    .workorder-filters {
      background: white;
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      border: 1px solid var(--gray-200);
    }
    
    .workorder-table {
      background: white;
      border-radius: var(--radius-lg);
      border: 1px solid var(--gray-200);
      overflow: hidden;
    }
    
    .workorder-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-lg);
      border-bottom: 1px solid var(--gray-100);
      transition: background 0.3s ease;
    }
    
    .workorder-item:hover {
      background: var(--gray-50);
    }
    
    .workorder-item:last-child {
      border-bottom: none;
    }
    
    .workorder-priority {
      width: 4px;
      height: 60px;
      border-radius: 2px;
      margin-right: var(--spacing-lg);
    }
    
    .workorder-priority.high { background: var(--danger-color); }
    .workorder-priority.medium { background: var(--warning-color); }
    .workorder-priority.low { background: var(--success-color); }
    
    .workorder-content {
      flex: 1;
      min-width: 0;
    }
    
    .workorder-header {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-sm);
    }
    
    .workorder-id {
      font-size: 14px;
      font-weight: 600;
      color: var(--primary-color);
    }
    
    .workorder-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
      flex: 1;
    }
    
    .workorder-status {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .workorder-status.pending {
      background: rgba(245, 158, 11, 0.1);
      color: var(--warning-color);
    }
    
    .workorder-status.processing {
      background: rgba(59, 130, 246, 0.1);
      color: var(--primary-color);
    }
    
    .workorder-status.completed {
      background: rgba(16, 185, 129, 0.1);
      color: var(--success-color);
    }
    
    .workorder-desc {
      font-size: 14px;
      color: var(--gray-600);
      margin-bottom: var(--spacing-sm);
      line-height: 1.4;
    }
    
    .workorder-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-lg);
      font-size: 12px;
      color: var(--gray-500);
    }
    
    .workorder-meta-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
    }
    
    .workorder-actions {
      display: flex;
      gap: var(--spacing-sm);
      margin-left: var(--spacing-lg);
    }
    
    .create-workorder-form {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-md);
    }
    
    .form-group-full {
      grid-column: 1 / -1;
    }
    
    .maintenance-schedule {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-md);
    }
    
    .schedule-card {
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      transition: all 0.3s ease;
    }
    
    .schedule-card:hover {
      box-shadow: var(--shadow-md);
    }
    
    .schedule-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-md);
    }
    
    .schedule-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
    }
    
    .schedule-frequency {
      padding: 4px 8px;
      background: var(--primary-color);
      color: white;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .schedule-details {
      font-size: 14px;
      color: var(--gray-600);
      margin-bottom: var(--spacing-md);
    }
    
    .schedule-next {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      font-size: 12px;
      color: var(--gray-500);
      margin-bottom: var(--spacing-md);
    }
    
    .schedule-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
    
    @media (max-width: 768px) {
      .create-workorder-form {
        grid-template-columns: 1fr;
      }
      
      .workorder-item {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-md);
      }
      
      .workorder-actions {
        margin-left: 0;
        width: 100%;
        justify-content: flex-end;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div id="sidebarContainer"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部导航 -->
      <div id="headerContainer"></div>

      <!-- 页面内容 -->
      <div class="page-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">维保与工单系统</h1>
          <p class="page-description">报修工单处理流程、维修计划设置与执行、工单状态跟踪</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid mb-lg">
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">23</div>
                <div class="stat-label">待处理工单</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">15</div>
                <div class="stat-label">处理中工单</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-tools"></i>
              </div>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">已完成工单</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
          </div>

          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">8</div>
                <div class="stat-label">维修计划</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-calendar-alt"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 工单筛选 -->
        <div class="workorder-filters-compact">
          <div class="filter-row-compact">
            <div class="filter-item">
              <label>事件类型</label>
              <select class="form-select-compact">
                <option>全部类型</option>
                <option>设备维修</option>
                <option>清洁保养</option>
                <option>安全检查</option>
                <option>系统升级</option>
              </select>
            </div>
            <div class="filter-item">
              <label>优先级</label>
              <select class="form-select-compact">
                <option>全部优先级</option>
                <option>高</option>
                <option>中</option>
                <option>低</option>
              </select>
            </div>
            <div class="filter-item">
              <label>状态</label>
              <select class="form-select-compact">
                <option>全部状态</option>
                <option>待处理</option>
                <option>处理中</option>
                <option>已完成</option>
                <option>已关闭</option>
              </select>
            </div>
            <div class="filter-item">
              <label>时间范围</label>
              <select class="form-select-compact">
                <option>今天</option>
                <option>本周</option>
                <option>本月</option>
                <option>全部</option>
              </select>
            </div>
            <div class="filter-actions">
              <button class="btn-search">
                <i class="fas fa-search"></i>
                查询
              </button>
              <button class="btn-reset">
                <i class="fas fa-redo"></i>
                重置
              </button>
            </div>
          </div>
        </div>

        <!-- 工单列表 -->
        <div class="card mb-lg">
          <div class="card-header">
            <h3 class="card-title">工单列表</h3>
            <div class="d-flex gap-sm">
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-download"></i>
                导出
              </button>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="workorder-table">
              <div class="workorder-item">
                <div class="workorder-priority high"></div>
                <div class="workorder-content">
                  <div class="workorder-header">
                    <div class="workorder-id">WO-2024-001</div>
                    <div class="workorder-title">1号楼电梯故障维修</div>
                    <div class="workorder-status pending">待处理</div>
                  </div>
                  <div class="workorder-desc">
                    1号楼客梯出现异响，按钮反应迟缓，需要立即检修
                  </div>
                  <div class="workorder-meta">
                    <div class="workorder-meta-item">
                      <i class="fas fa-user"></i>
                      <span>申请人：张三</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-map-marker-alt"></i>
                      <span>位置：1号楼电梯</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-clock"></i>
                      <span>创建时间：2024-01-15 14:30</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-exclamation-triangle"></i>
                      <span>优先级：高</span>
                    </div>
                  </div>
                </div>
                <div class="workorder-actions">
                  <button class="btn btn-sm btn-primary">接单</button>
                  <button class="btn btn-sm btn-secondary">详情</button>
                </div>
              </div>

              <div class="workorder-item">
                <div class="workorder-priority medium"></div>
                <div class="workorder-content">
                  <div class="workorder-header">
                    <div class="workorder-id">WO-2024-002</div>
                    <div class="workorder-title">会议室空调维护</div>
                    <div class="workorder-status processing">处理中</div>
                  </div>
                  <div class="workorder-desc">
                    3号楼会议室A空调制冷效果差，需要清洗滤网和检查制冷剂
                  </div>
                  <div class="workorder-meta">
                    <div class="workorder-meta-item">
                      <i class="fas fa-user"></i>
                      <span>负责人：李四</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-map-marker-alt"></i>
                      <span>位置：3号楼会议室A</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-clock"></i>
                      <span>开始时间：2024-01-15 10:00</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-flag"></i>
                      <span>优先级：中</span>
                    </div>
                  </div>
                </div>
                <div class="workorder-actions">
                  <button class="btn btn-sm btn-success">完成</button>
                  <button class="btn btn-sm btn-secondary">详情</button>
                </div>
              </div>

              <div class="workorder-item">
                <div class="workorder-priority low"></div>
                <div class="workorder-content">
                  <div class="workorder-header">
                    <div class="workorder-id">WO-2024-003</div>
                    <div class="workorder-title">地下车库照明检查</div>
                    <div class="workorder-status completed">已完成</div>
                  </div>
                  <div class="workorder-desc">
                    定期检查地下车库照明设备，更换损坏的LED灯具
                  </div>
                  <div class="workorder-meta">
                    <div class="workorder-meta-item">
                      <i class="fas fa-user"></i>
                      <span>完成人：王五</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-map-marker-alt"></i>
                      <span>位置：地下车库</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-clock"></i>
                      <span>完成时间：2024-01-15 16:30</span>
                    </div>
                    <div class="workorder-meta-item">
                      <i class="fas fa-flag"></i>
                      <span>优先级：低</span>
                    </div>
                  </div>
                </div>
                <div class="workorder-actions">
                  <button class="btn btn-sm btn-success">已完成</button>
                  <button class="btn btn-sm btn-secondary">详情</button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 维修计划 -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">维修计划</h3>
            <button class="btn btn-primary">
              <i class="fas fa-plus"></i>
              新增计划
            </button>
          </div>
          <div class="card-body">
            <div class="maintenance-schedule">
              <div class="schedule-card">
                <div class="schedule-header">
                  <div class="schedule-title">电梯定期保养</div>
                  <div class="schedule-frequency">月度</div>
                </div>
                <div class="schedule-details">
                  对所有电梯进行定期保养检查，包括机械部件润滑、安全装置测试等
                </div>
                <div class="schedule-next">
                  <i class="fas fa-calendar"></i>
                  <span>下次执行：2024-02-01</span>
                </div>
                <div class="schedule-actions">
                  <button class="btn btn-sm btn-primary">立即执行</button>
                  <button class="btn btn-sm btn-secondary">编辑</button>
                </div>
              </div>

              <div class="schedule-card">
                <div class="schedule-header">
                  <div class="schedule-title">空调系统清洁</div>
                  <div class="schedule-frequency">季度</div>
                </div>
                <div class="schedule-details">
                  清洁空调系统滤网，检查制冷剂，测试温控系统
                </div>
                <div class="schedule-next">
                  <i class="fas fa-calendar"></i>
                  <span>下次执行：2024-04-01</span>
                </div>
                <div class="schedule-actions">
                  <button class="btn btn-sm btn-primary">立即执行</button>
                  <button class="btn btn-sm btn-secondary">编辑</button>
                </div>
              </div>

              <div class="schedule-card">
                <div class="schedule-header">
                  <div class="schedule-title">消防设备检查</div>
                  <div class="schedule-frequency">月度</div>
                </div>
                <div class="schedule-details">
                  检查消防栓、灭火器、烟感器等消防设备的工作状态
                </div>
                <div class="schedule-next">
                  <i class="fas fa-calendar"></i>
                  <span>下次执行：2024-01-20</span>
                </div>
                <div class="schedule-actions">
                  <button class="btn btn-sm btn-warning">即将到期</button>
                  <button class="btn btn-sm btn-secondary">编辑</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入主JavaScript -->
  <script src="assets/js/main.js"></script>
  
  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载组件
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      
      // 更新面包屑
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('维保与工单系统');
        }
      }, 100);
    });

    // 加载组件
    function loadComponent(containerId, componentPath) {
      fetch(componentPath)
        .then(response => response.text())
        .then(html => {
          document.getElementById(containerId).innerHTML = html;
        })
        .catch(error => {
          console.error('Error loading component:', error);
        });
    }
  </script>
</body>
</html>
