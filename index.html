<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>广州农行智慧楼宇 - 首页</title>
  
  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- 引入Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  
  <!-- 引入主样式 -->
  <link rel="stylesheet" href="assets/css/main.css">
  
  <style>
    /* 首页特定样式 */
    .dashboard-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
      gap: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
    }
    
    .quick-actions {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-lg);
    }
    
    .quick-action {
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      text-decoration: none;
      color: var(--gray-700);
      transition: all 0.3s ease;
      display: flex;
      flex-direction: column;
      align-items: center;
      text-align: center;
      gap: var(--spacing-md);
    }
    
    .quick-action:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
      border-color: var(--primary-color);
    }
    
    .quick-action-icon {
      width: 56px;
      height: 56px;
      border-radius: var(--radius-lg);
      background: var(--primary-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 24px;
    }
    
    .quick-action-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
    }
    
    .quick-action-desc {
      font-size: 12px;
      color: var(--gray-500);
    }
    
    .chart-container {
      position: relative;
      height: 300px;
      margin-top: var(--spacing-md);
    }
    
    .recent-list {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .recent-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      border-bottom: 1px solid var(--gray-100);
      transition: background 0.3s ease;
    }
    
    .recent-item:hover {
      background: var(--gray-50);
    }
    
    .recent-item:last-child {
      border-bottom: none;
    }
    
    .recent-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: white;
      flex-shrink: 0;
    }
    
    .recent-content {
      flex: 1;
    }
    
    .recent-title {
      font-size: 14px;
      font-weight: 500;
      color: var(--gray-900);
      margin-bottom: 2px;
    }
    
    .recent-desc {
      font-size: 12px;
      color: var(--gray-500);
    }
    
    .recent-time {
      font-size: 11px;
      color: var(--gray-400);
      white-space: nowrap;
    }
    
    @media (max-width: 768px) {
      .stats-grid {
        grid-template-columns: 1fr;
      }
      
      .dashboard-grid {
        grid-template-columns: 1fr;
      }
      
      .quick-actions {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div id="sidebarContainer"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部导航 -->
      <div id="headerContainer"></div>

      <!-- 页面内容 -->
      <div class="page-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">广州农行智慧楼宇</h1>
          <p class="page-description">实时监控楼宇运行状态，智能化管理各类设备和系统</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value" id="totalAlerts">24</div>
                <div class="stat-label">告警总数</div>
                <div class="stat-change positive">
                  <i class="fas fa-arrow-up"></i>
                  <span>较昨日 +12%</span>
                </div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value" id="totalWorkOrders">156</div>
                <div class="stat-label">工单数量</div>
                <div class="stat-change positive">
                  <i class="fas fa-arrow-up"></i>
                  <span>较昨日 +8%</span>
                </div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-clipboard-list"></i>
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value" id="onlineDevices">892</div>
                <div class="stat-label">在线设备</div>
                <div class="stat-change neutral">
                  <i class="fas fa-circle"></i>
                  <span>在线率 89.2%</span>
                </div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-wifi"></i>
              </div>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value" id="energyConsumption">1,245</div>
                <div class="stat-label">今日能耗 (kWh)</div>
                <div class="stat-change negative">
                  <i class="fas fa-arrow-down"></i>
                  <span>较昨日 -5%</span>
                </div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-bolt"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="card mb-lg">
          <div class="card-header">
            <h3 class="card-title">快捷操作</h3>
          </div>
          <div class="card-body">
            <div class="quick-actions">
              <a href="video-monitoring.html" class="quick-action">
                <div class="quick-action-icon">
                  <i class="fas fa-video"></i>
                </div>
                <div class="quick-action-title">视频监控</div>
                <div class="quick-action-desc">实时查看监控画面</div>
              </a>

              <a href="access-control.html" class="quick-action">
                <div class="quick-action-icon">
                  <i class="fas fa-door-open"></i>
                </div>
                <div class="quick-action-title">门禁控制</div>
                <div class="quick-action-desc">管理门禁权限</div>
              </a>

              <a href="event-linkage.html" class="quick-action">
                <div class="quick-action-icon">
                  <i class="fas fa-exclamation-triangle"></i>
                </div>
                <div class="quick-action-title">事件处理</div>
                <div class="quick-action-desc">查看处理告警事件</div>
              </a>

              <a href="maintenance-workorder.html" class="quick-action">
                <div class="quick-action-icon">
                  <i class="fas fa-tools"></i>
                </div>
                <div class="quick-action-title">工单管理</div>
                <div class="quick-action-desc">创建处理工单</div>
              </a>

              <a href="energy-management.html" class="quick-action">
                <div class="quick-action-icon">
                  <i class="fas fa-chart-line"></i>
                </div>
                <div class="quick-action-title">能耗分析</div>
                <div class="quick-action-desc">查看能耗趋势</div>
              </a>

              <a href="map-monitoring.html" class="quick-action">
                <div class="quick-action-icon">
                  <i class="fas fa-map-marked-alt"></i>
                </div>
                <div class="quick-action-title">地图监控</div>
                <div class="quick-action-desc">地图总览监控</div>
              </a>
            </div>
          </div>
        </div>

        <!-- 图表和最新动态 -->
        <div class="dashboard-grid">
          <!-- 能耗趋势图表 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">能耗趋势</h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-sm btn-secondary">日</button>
                <button class="btn btn-sm btn-primary">周</button>
                <button class="btn btn-sm btn-secondary">月</button>
              </div>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="energyChart"></canvas>
              </div>
            </div>
          </div>

          <!-- 设备状态分布 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">设备状态分布</h3>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="deviceChart"></canvas>
              </div>
            </div>
          </div>

          <!-- 最新告警 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">最新告警</h3>
              <a href="event-linkage.html" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body p-0">
              <div class="recent-list" id="recentAlerts">
                <!-- 告警列表将通过JavaScript动态生成 -->
              </div>
            </div>
          </div>

          <!-- 最新工单 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">最新工单</h3>
              <a href="maintenance-workorder.html" class="btn btn-sm btn-primary">查看全部</a>
            </div>
            <div class="card-body p-0">
              <div class="recent-list" id="recentWorkOrders">
                <!-- 工单列表将通过JavaScript动态生成 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入主JavaScript -->
  <script src="assets/js/main.js"></script>
  
  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载组件
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      
      // 初始化图表
      initCharts();
      
      // 加载最新数据
      loadRecentData();
      
      // 设置当前页面导航激活状态
      setTimeout(() => {
        setActiveNavigation();

        // 更新面包屑
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('首页');
        }
      }, 100);
    });

    // 加载组件
    function loadComponent(containerId, componentPath) {
      fetch(componentPath)
        .then(response => response.text())
        .then(html => {
          document.getElementById(containerId).innerHTML = html;
        })
        .catch(error => {
          console.error('Error loading component:', error);
        });
    }

    // 设置当前页面导航激活状态
    function setActiveNavigation() {
      // 移除所有导航项的激活状态
      document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
      });

      // 设置首页为激活状态
      const dashboardNavItem = document.querySelector('.nav-item[data-page="dashboard"]');
      if (dashboardNavItem) {
        dashboardNavItem.classList.add('active');
      }
    }

    // 初始化图表
    function initCharts() {
      // 能耗趋势图表
      const energyCtx = document.getElementById('energyChart').getContext('2d');
      new Chart(energyCtx, {
        type: 'line',
        data: {
          labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
          datasets: [{
            label: '能耗 (kWh)',
            data: [1200, 1350, 1180, 1420, 1245, 980, 1100],
            borderColor: '#3b82f6',
            backgroundColor: 'rgba(59, 130, 246, 0.1)',
            borderWidth: 2,
            fill: true,
            tension: 0.4
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              display: false
            }
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: '#f3f4f6'
              }
            },
            x: {
              grid: {
                display: false
              }
            }
          }
        }
      });

      // 设备状态分布图表
      const deviceCtx = document.getElementById('deviceChart').getContext('2d');
      new Chart(deviceCtx, {
        type: 'doughnut',
        data: {
          labels: ['正常', '告警', '离线', '维护'],
          datasets: [{
            data: [750, 80, 120, 50],
            backgroundColor: [
              '#10b981',
              '#f59e0b',
              '#ef4444',
              '#6b7280'
            ],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: {
                padding: 20,
                usePointStyle: true
              }
            }
          }
        }
      });
    }

    // 加载最新数据
    function loadRecentData() {
      // 模拟最新告警数据
      const recentAlerts = [
        {
          icon: 'fas fa-exclamation-triangle',
          iconColor: '#ef4444',
          title: '空调系统故障',
          desc: '1号楼3层空调系统温度异常',
          time: '2分钟前'
        },
        {
          icon: 'fas fa-thermometer-half',
          iconColor: '#f59e0b',
          title: '机房温度过高',
          desc: '服务器机房温度超过预设阈值',
          time: '15分钟前'
        },
        {
          icon: 'fas fa-door-open',
          iconColor: '#3b82f6',
          title: '门禁异常',
          desc: '2号楼入口门禁读卡器故障',
          time: '1小时前'
        },
        {
          icon: 'fas fa-video',
          iconColor: '#6b7280',
          title: '摄像头离线',
          desc: '停车场监控摄像头连接中断',
          time: '2小时前'
        }
      ];

      // 模拟最新工单数据
      const recentWorkOrders = [
        {
          icon: 'fas fa-tools',
          iconColor: '#10b981',
          title: '电梯维护完成',
          desc: 'WO001 - 1号楼客梯定期保养',
          time: '30分钟前'
        },
        {
          icon: 'fas fa-wrench',
          iconColor: '#f59e0b',
          title: '空调维修中',
          desc: 'WO002 - 会议室空调制冷异常',
          time: '1小时前'
        },
        {
          icon: 'fas fa-clipboard-check',
          iconColor: '#3b82f6',
          title: '安全检查',
          desc: 'WO003 - 消防设备月度检查',
          time: '3小时前'
        },
        {
          icon: 'fas fa-lightbulb',
          iconColor: '#6b7280',
          title: '照明维修',
          desc: 'WO004 - 地下车库照明灯更换',
          time: '5小时前'
        }
      ];

      // 渲染告警列表
      renderRecentList('recentAlerts', recentAlerts);
      
      // 渲染工单列表
      renderRecentList('recentWorkOrders', recentWorkOrders);
    }

    // 渲染最新列表
    function renderRecentList(containerId, items) {
      const container = document.getElementById(containerId);
      if (!container) return;

      const html = items.map(item => `
        <div class="recent-item">
          <div class="recent-icon" style="background: ${item.iconColor}">
            <i class="${item.icon}"></i>
          </div>
          <div class="recent-content">
            <div class="recent-title">${item.title}</div>
            <div class="recent-desc">${item.desc}</div>
          </div>
          <div class="recent-time">${item.time}</div>
        </div>
      `).join('');

      container.innerHTML = html;
    }
  </script>
</body>
</html>
