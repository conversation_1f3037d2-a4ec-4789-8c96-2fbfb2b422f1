<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>资产与设备管理 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">资产与设备管理</h1>
          <p class="page-description">设备台账管理、设备维修保养记录、报废流程管理</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">1,245</div>
                <div class="stat-label">设备总数</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-boxes"></i>
              </div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">1,156</div>
                <div class="stat-label">正常设备</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">67</div>
                <div class="stat-label">维修中</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-tools"></i>
              </div>
            </div>
          </div>
          <div class="stat-card danger">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">22</div>
                <div class="stat-label">待报废</div>
              </div>
              <div class="stat-icon danger">
                <i class="fas fa-trash"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 设备筛选和搜索 -->
        <div class="card mb-lg">
          <div class="card-header">
            <h3 class="card-title">
              <i class="fas fa-search text-primary"></i>
              设备筛选
            </h3>
            <button class="btn btn-outline-primary btn-sm" onclick="resetFilters()">
              <i class="fas fa-redo"></i>
              重置筛选
            </button>
          </div>
          <div class="card-body">
            <div class="equipment-filters-compact">
              <div class="filter-row-compact">
                <div class="filter-item">
                  <label>设备分类</label>
                  <select class="form-select-compact" id="categoryFilter">
                    <option value="">全部分类</option>
                    <option value="hvac">暖通空调</option>
                    <option value="electrical">电气设备</option>
                    <option value="security">安防设备</option>
                    <option value="network">网络设备</option>
                    <option value="elevator">电梯设备</option>
                    <option value="fire">消防设备</option>
                  </select>
                </div>
                <div class="filter-item">
                  <label>设备状态</label>
                  <select class="form-select-compact" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="normal">正常运行</option>
                    <option value="maintenance">维修中</option>
                    <option value="standby">备用</option>
                    <option value="scrap">待报废</option>
                  </select>
                </div>
                <div class="filter-item">
                  <label>所在位置</label>
                  <select class="form-select-compact" id="locationFilter">
                    <option value="">全部位置</option>
                    <option value="building1">1号楼</option>
                    <option value="building2">2号楼</option>
                    <option value="building3">3号楼</option>
                  </select>
                </div>
                <div class="filter-item">
                  <label>关键词搜索</label>
                  <input type="text" class="form-control-compact" id="searchKeyword" placeholder="设备名称/编号/品牌">
                </div>
                <div class="filter-actions">
                  <button class="btn-search" onclick="searchEquipment()">
                    <i class="fas fa-search"></i>
                    搜索
                  </button>
                  <button class="btn-export" onclick="exportEquipment()">
                    <i class="fas fa-download"></i>
                    导出
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备台账管理 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-list text-success"></i>
                设备台账列表
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshEquipmentList()">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
                <button class="btn btn-success btn-sm" onclick="showAddEquipmentModal()">
                  <i class="fas fa-plus"></i>
                  新增设备
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="equipment-list">
                <!-- 设备列表项 -->
                <div class="equipment-item normal">
                  <div class="equipment-info">
                    <div class="equipment-header">
                      <div class="equipment-name">中央空调主机-01</div>
                      <div class="equipment-code">HVAC-001</div>
                      <span class="status-badge success">正常运行</span>
                    </div>
                    <div class="equipment-details">
                      <div class="detail-item">
                        <i class="fas fa-tag"></i>
                        <span>品牌：大金</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <span>安装日期：2022-03-15</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>位置：1号楼-地下1层-机房</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-user"></i>
                        <span>负责人：张工</span>
                      </div>
                    </div>
                  </div>
                  <div class="equipment-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewEquipmentDetail('HVAC-001')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="showMaintenanceRecord('HVAC-001')">
                      <i class="fas fa-wrench"></i>
                      维保
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="generateQRCode('HVAC-001')">
                      <i class="fas fa-qrcode"></i>
                      二维码
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="editEquipment('HVAC-001')">
                      <i class="fas fa-edit"></i>
                      编辑
                    </button>
                  </div>
                </div>

                <div class="equipment-item maintenance">
                  <div class="equipment-info">
                    <div class="equipment-header">
                      <div class="equipment-name">电梯控制系统-A</div>
                      <div class="equipment-code">ELEV-A01</div>
                      <span class="status-badge warning">维修中</span>
                    </div>
                    <div class="equipment-details">
                      <div class="detail-item">
                        <i class="fas fa-tag"></i>
                        <span>品牌：奥的斯</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <span>安装日期：2021-08-20</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>位置：1号楼-1层-电梯井</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>故障：控制面板异常</span>
                      </div>
                    </div>
                  </div>
                  <div class="equipment-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewEquipmentDetail('ELEV-A01')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="showRepairProgress('ELEV-A01')">
                      <i class="fas fa-tools"></i>
                      维修进度
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="generateQRCode('ELEV-A01')">
                      <i class="fas fa-qrcode"></i>
                      二维码
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="editEquipment('ELEV-A01')">
                      <i class="fas fa-edit"></i>
                      编辑
                    </button>
                  </div>
                </div>

                <div class="equipment-item normal">
                  <div class="equipment-info">
                    <div class="equipment-header">
                      <div class="equipment-name">监控摄像头-大厅01</div>
                      <div class="equipment-code">CAM-H01</div>
                      <span class="status-badge success">正常运行</span>
                    </div>
                    <div class="equipment-details">
                      <div class="detail-item">
                        <i class="fas fa-tag"></i>
                        <span>品牌：海康威视</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <span>安装日期：2023-01-10</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>位置：1号楼-1层-大厅</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-shield-alt"></i>
                        <span>保修期：2025-01-10</span>
                      </div>
                    </div>
                  </div>
                  <div class="equipment-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewEquipmentDetail('CAM-H01')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="showMaintenanceRecord('CAM-H01')">
                      <i class="fas fa-wrench"></i>
                      维保
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="generateQRCode('CAM-H01')">
                      <i class="fas fa-qrcode"></i>
                      二维码
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="editEquipment('CAM-H01')">
                      <i class="fas fa-edit"></i>
                      编辑
                    </button>
                  </div>
                </div>

                <div class="equipment-item standby">
                  <div class="equipment-info">
                    <div class="equipment-header">
                      <div class="equipment-name">UPS不间断电源-02</div>
                      <div class="equipment-code">UPS-002</div>
                      <span class="status-badge info">备用</span>
                    </div>
                    <div class="equipment-details">
                      <div class="detail-item">
                        <i class="fas fa-tag"></i>
                        <span>品牌：APC</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <span>安装日期：2022-11-05</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>位置：2号楼-地下1层-配电房</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-battery-half"></i>
                        <span>电池状态：良好</span>
                      </div>
                    </div>
                  </div>
                  <div class="equipment-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewEquipmentDetail('UPS-002')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="showMaintenanceRecord('UPS-002')">
                      <i class="fas fa-wrench"></i>
                      维保
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="activateEquipment('UPS-002')">
                      <i class="fas fa-play"></i>
                      启用
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" onclick="editEquipment('UPS-002')">
                      <i class="fas fa-edit"></i>
                      编辑
                    </button>
                  </div>
                </div>

                <div class="equipment-item scrap">
                  <div class="equipment-info">
                    <div class="equipment-header">
                      <div class="equipment-name">老式打印机-03</div>
                      <div class="equipment-code">PRINT-003</div>
                      <span class="status-badge danger">待报废</span>
                    </div>
                    <div class="equipment-details">
                      <div class="detail-item">
                        <i class="fas fa-tag"></i>
                        <span>品牌：惠普</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-calendar"></i>
                        <span>安装日期：2018-05-12</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>位置：3号楼-2层-办公室</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-exclamation-circle"></i>
                        <span>报废原因：设备老化，维修成本过高</span>
                      </div>
                    </div>
                  </div>
                  <div class="equipment-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewEquipmentDetail('PRINT-003')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="showScrapProcess('PRINT-003')">
                      <i class="fas fa-trash"></i>
                      报废流程
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="postponeScrap('PRINT-003')">
                      <i class="fas fa-clock"></i>
                      延期报废
                    </button>
                  </div>
                </div>
              </div>

              <!-- 分页 -->
              <div class="pagination-container">
                <div class="pagination-info">
                  显示 1-5 条，共 1,245 条记录
                </div>
                <div class="pagination">
                  <button class="btn btn-sm btn-outline-primary" disabled>
                    <i class="fas fa-chevron-left"></i>
                    上一页
                  </button>
                  <button class="btn btn-sm btn-primary">1</button>
                  <button class="btn btn-sm btn-outline-primary">2</button>
                  <button class="btn btn-sm btn-outline-primary">3</button>
                  <span>...</span>
                  <button class="btn btn-sm btn-outline-primary">249</button>
                  <button class="btn btn-sm btn-outline-primary">
                    下一页
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-chart-pie text-info"></i>
                设备统计分析
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="refreshStatistics()">
                <i class="fas fa-sync-alt"></i>
                刷新统计
              </button>
            </div>
            <div class="card-body">
              <!-- 设备分类统计 -->
              <div class="statistics-section">
                <h5>设备分类统计</h5>
                <div class="category-stats">
                  <div class="category-item">
                    <div class="category-icon hvac">
                      <i class="fas fa-wind"></i>
                    </div>
                    <div class="category-info">
                      <div class="category-name">暖通空调</div>
                      <div class="category-count">245台</div>
                      <div class="category-percent">19.7%</div>
                    </div>
                  </div>
                  <div class="category-item">
                    <div class="category-icon electrical">
                      <i class="fas fa-bolt"></i>
                    </div>
                    <div class="category-info">
                      <div class="category-name">电气设备</div>
                      <div class="category-count">312台</div>
                      <div class="category-percent">25.1%</div>
                    </div>
                  </div>
                  <div class="category-item">
                    <div class="category-icon security">
                      <i class="fas fa-shield-alt"></i>
                    </div>
                    <div class="category-info">
                      <div class="category-name">安防设备</div>
                      <div class="category-count">189台</div>
                      <div class="category-percent">15.2%</div>
                    </div>
                  </div>
                  <div class="category-item">
                    <div class="category-icon network">
                      <i class="fas fa-network-wired"></i>
                    </div>
                    <div class="category-info">
                      <div class="category-name">网络设备</div>
                      <div class="category-count">156台</div>
                      <div class="category-percent">12.5%</div>
                    </div>
                  </div>
                  <div class="category-item">
                    <div class="category-icon elevator">
                      <i class="fas fa-elevator"></i>
                    </div>
                    <div class="category-info">
                      <div class="category-name">电梯设备</div>
                      <div class="category-count">12台</div>
                      <div class="category-percent">1.0%</div>
                    </div>
                  </div>
                  <div class="category-item">
                    <div class="category-icon fire">
                      <i class="fas fa-fire-extinguisher"></i>
                    </div>
                    <div class="category-info">
                      <div class="category-name">消防设备</div>
                      <div class="category-count">331台</div>
                      <div class="category-percent">26.6%</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 设备状态分布 -->
              <div class="statistics-section">
                <h5>设备状态分布</h5>
                <div class="status-chart">
                  <canvas id="statusChart" width="300" height="200"></canvas>
                </div>
                <div class="status-legend">
                  <div class="legend-item">
                    <span class="legend-color normal"></span>
                    <span>正常运行 (92.8%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color maintenance"></span>
                    <span>维修中 (5.4%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color standby"></span>
                    <span>备用 (1.0%)</span>
                  </div>
                  <div class="legend-item">
                    <span class="legend-color scrap"></span>
                    <span>待报废 (1.8%)</span>
                  </div>
                </div>
              </div>

              <!-- 维保提醒 -->
              <div class="statistics-section">
                <h5>维保提醒</h5>
                <div class="maintenance-alerts">
                  <div class="alert-item urgent">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="alert-content">
                      <div class="alert-title">紧急维保</div>
                      <div class="alert-desc">3台设备需要立即维保</div>
                    </div>
                    <button class="btn btn-sm btn-danger" onclick="viewUrgentMaintenance()">查看</button>
                  </div>
                  <div class="alert-item warning">
                    <i class="fas fa-clock"></i>
                    <div class="alert-content">
                      <div class="alert-title">即将到期</div>
                      <div class="alert-desc">15台设备维保即将到期</div>
                    </div>
                    <button class="btn btn-sm btn-warning" onclick="viewUpcomingMaintenance()">查看</button>
                  </div>
                  <div class="alert-item info">
                    <i class="fas fa-calendar-check"></i>
                    <div class="alert-content">
                      <div class="alert-title">本月计划</div>
                      <div class="alert-desc">45台设备计划维保</div>
                    </div>
                    <button class="btn btn-sm btn-info" onclick="viewMaintenancePlan()">查看</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 维修保养记录和报废流程 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-wrench text-warning"></i>
                维修保养记录
              </h3>
              <button class="btn btn-primary btn-sm" onclick="showAddMaintenanceModal()">
                <i class="fas fa-plus"></i>
                新增记录
              </button>
            </div>
            <div class="card-body">
              <div class="maintenance-records">
                <div class="record-item completed">
                  <div class="record-header">
                    <div class="record-type maintenance">
                      <i class="fas fa-wrench"></i>
                      <span>定期保养</span>
                    </div>
                    <div class="record-status completed">已完成</div>
                    <div class="record-date">2024-01-10</div>
                  </div>
                  <div class="record-content">
                    <div class="record-equipment">设备：中央空调主机-01 (HVAC-001)</div>
                    <div class="record-description">更换空调滤网，清洗冷凝器，检查制冷剂压力</div>
                    <div class="record-technician">维修人员：张工 | 费用：￥1,200</div>
                  </div>
                  <div class="record-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewMaintenanceDetail('MAINT-001')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="downloadMaintenanceReport('MAINT-001')">
                      <i class="fas fa-download"></i>
                      报告
                    </button>
                  </div>
                </div>

                <div class="record-item in-progress">
                  <div class="record-header">
                    <div class="record-type repair">
                      <i class="fas fa-tools"></i>
                      <span>故障维修</span>
                    </div>
                    <div class="record-status in-progress">进行中</div>
                    <div class="record-date">2024-01-15</div>
                  </div>
                  <div class="record-content">
                    <div class="record-equipment">设备：电梯控制系统-A (ELEV-A01)</div>
                    <div class="record-description">控制面板显示异常，需要更换主控板</div>
                    <div class="record-technician">维修人员：李工 | 预计费用：￥3,500</div>
                  </div>
                  <div class="record-actions">
                    <button class="btn btn-sm btn-outline-warning" onclick="updateMaintenanceProgress('MAINT-002')">
                      <i class="fas fa-edit"></i>
                      更新进度
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="completeMaintenanceRecord('MAINT-002')">
                      <i class="fas fa-check"></i>
                      完成
                    </button>
                  </div>
                </div>

                <div class="record-item scheduled">
                  <div class="record-header">
                    <div class="record-type inspection">
                      <i class="fas fa-search"></i>
                      <span>安全检查</span>
                    </div>
                    <div class="record-status scheduled">已安排</div>
                    <div class="record-date">2024-01-20</div>
                  </div>
                  <div class="record-content">
                    <div class="record-equipment">设备：消防报警系统 (FIRE-SYS-01)</div>
                    <div class="record-description">年度消防设备安全检查和功能测试</div>
                    <div class="record-technician">检查人员：王工 | 预计费用：￥800</div>
                  </div>
                  <div class="record-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="editMaintenanceSchedule('MAINT-003')">
                      <i class="fas fa-calendar"></i>
                      调整时间
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="cancelMaintenanceSchedule('MAINT-003')">
                      <i class="fas fa-times"></i>
                      取消
                    </button>
                  </div>
                </div>

                <div class="record-item overdue">
                  <div class="record-header">
                    <div class="record-type maintenance">
                      <i class="fas fa-exclamation-triangle"></i>
                      <span>逾期保养</span>
                    </div>
                    <div class="record-status overdue">已逾期</div>
                    <div class="record-date">2024-01-05</div>
                  </div>
                  <div class="record-content">
                    <div class="record-equipment">设备：UPS不间断电源-01 (UPS-001)</div>
                    <div class="record-description">电池组定期检查和容量测试</div>
                    <div class="record-technician">负责人：赵工 | 逾期：10天</div>
                  </div>
                  <div class="record-actions">
                    <button class="btn btn-sm btn-danger" onclick="urgentScheduleMaintenance('MAINT-004')">
                      <i class="fas fa-exclamation"></i>
                      紧急安排
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="postponeMaintenance('MAINT-004')">
                      <i class="fas fa-clock"></i>
                      延期
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-trash text-danger"></i>
                设备报废流程
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="showScrapStatistics()">
                <i class="fas fa-chart-bar"></i>
                报废统计
              </button>
            </div>
            <div class="card-body">
              <div class="scrap-process">
                <div class="process-item pending">
                  <div class="process-header">
                    <div class="process-step">1</div>
                    <div class="process-title">报废申请</div>
                    <div class="process-status pending">待审批</div>
                  </div>
                  <div class="process-content">
                    <div class="process-equipment">设备：老式打印机-03 (PRINT-003)</div>
                    <div class="process-reason">报废原因：设备老化严重，维修成本过高，已超过使用年限</div>
                    <div class="process-info">申请人：办公室主任 | 申请时间：2024-01-12</div>
                  </div>
                  <div class="process-actions">
                    <button class="btn btn-sm btn-success" onclick="approveScrapApplication('SCRAP-001')">
                      <i class="fas fa-check"></i>
                      批准
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="rejectScrapApplication('SCRAP-001')">
                      <i class="fas fa-times"></i>
                      拒绝
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewScrapDetail('SCRAP-001')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                  </div>
                </div>

                <div class="process-item approved">
                  <div class="process-header">
                    <div class="process-step">2</div>
                    <div class="process-title">资产评估</div>
                    <div class="process-status approved">已批准</div>
                  </div>
                  <div class="process-content">
                    <div class="process-equipment">设备：旧服务器-05 (SERVER-005)</div>
                    <div class="process-reason">评估结果：残值￥2,000，建议回收处理</div>
                    <div class="process-info">评估人：财务部 | 评估时间：2024-01-08</div>
                  </div>
                  <div class="process-actions">
                    <button class="btn btn-sm btn-primary" onclick="proceedToDisposal('SCRAP-002')">
                      <i class="fas fa-arrow-right"></i>
                      进入处置
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="revaluateAsset('SCRAP-002')">
                      <i class="fas fa-redo"></i>
                      重新评估
                    </button>
                  </div>
                </div>

                <div class="process-item in-disposal">
                  <div class="process-header">
                    <div class="process-step">3</div>
                    <div class="process-title">设备处置</div>
                    <div class="process-status in-disposal">处置中</div>
                  </div>
                  <div class="process-content">
                    <div class="process-equipment">设备：废旧空调-02 (AC-OLD-02)</div>
                    <div class="process-reason">处置方式：委托专业回收公司处理</div>
                    <div class="process-info">处置公司：绿色回收有限公司 | 预约时间：2024-01-18</div>
                  </div>
                  <div class="process-actions">
                    <button class="btn btn-sm btn-warning" onclick="updateDisposalProgress('SCRAP-003')">
                      <i class="fas fa-edit"></i>
                      更新进度
                    </button>
                    <button class="btn btn-sm btn-success" onclick="completeDisposal('SCRAP-003')">
                      <i class="fas fa-check"></i>
                      完成处置
                    </button>
                  </div>
                </div>

                <div class="process-item completed">
                  <div class="process-header">
                    <div class="process-step">4</div>
                    <div class="process-title">流程完成</div>
                    <div class="process-status completed">已完成</div>
                  </div>
                  <div class="process-content">
                    <div class="process-equipment">设备：旧复印机-01 (COPY-OLD-01)</div>
                    <div class="process-reason">已完成报废处置，资产已从台账中移除</div>
                    <div class="process-info">完成时间：2024-01-05 | 回收价值：￥500</div>
                  </div>
                  <div class="process-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewScrapReport('SCRAP-004')">
                      <i class="fas fa-file-alt"></i>
                      查看报告
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="downloadScrapCertificate('SCRAP-004')">
                      <i class="fas fa-certificate"></i>
                      处置证明
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备分类管理和二维码管理 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-tags text-info"></i>
                设备分类管理
              </h3>
              <button class="btn btn-primary btn-sm" onclick="showAddCategoryModal()">
                <i class="fas fa-plus"></i>
                新增分类
              </button>
            </div>
            <div class="card-body">
              <div class="category-management">
                <div class="category-tree">
                  <div class="category-node expanded">
                    <div class="category-header">
                      <i class="fas fa-chevron-down category-toggle"></i>
                      <i class="fas fa-wind category-icon"></i>
                      <span class="category-name">暖通空调系统</span>
                      <span class="category-count">(245台)</span>
                      <div class="category-actions">
                        <button class="btn btn-xs btn-outline-primary" onclick="editCategory('hvac')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-xs btn-outline-danger" onclick="deleteCategory('hvac')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                    <div class="category-children">
                      <div class="subcategory-item">
                        <i class="fas fa-snowflake"></i>
                        <span>中央空调 (89台)</span>
                      </div>
                      <div class="subcategory-item">
                        <i class="fas fa-fan"></i>
                        <span>新风系统 (45台)</span>
                      </div>
                      <div class="subcategory-item">
                        <i class="fas fa-thermometer-half"></i>
                        <span>温控设备 (111台)</span>
                      </div>
                    </div>
                  </div>

                  <div class="category-node">
                    <div class="category-header">
                      <i class="fas fa-chevron-right category-toggle"></i>
                      <i class="fas fa-bolt category-icon"></i>
                      <span class="category-name">电气设备</span>
                      <span class="category-count">(312台)</span>
                      <div class="category-actions">
                        <button class="btn btn-xs btn-outline-primary" onclick="editCategory('electrical')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-xs btn-outline-danger" onclick="deleteCategory('electrical')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="category-node">
                    <div class="category-header">
                      <i class="fas fa-chevron-right category-toggle"></i>
                      <i class="fas fa-shield-alt category-icon"></i>
                      <span class="category-name">安防设备</span>
                      <span class="category-count">(189台)</span>
                      <div class="category-actions">
                        <button class="btn btn-xs btn-outline-primary" onclick="editCategory('security')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-xs btn-outline-danger" onclick="deleteCategory('security')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="category-node">
                    <div class="category-header">
                      <i class="fas fa-chevron-right category-toggle"></i>
                      <i class="fas fa-network-wired category-icon"></i>
                      <span class="category-name">网络设备</span>
                      <span class="category-count">(156台)</span>
                      <div class="category-actions">
                        <button class="btn btn-xs btn-outline-primary" onclick="editCategory('network')">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-xs btn-outline-danger" onclick="deleteCategory('network')">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-qrcode text-success"></i>
                设备二维码管理
              </h3>
              <button class="btn btn-primary btn-sm" onclick="batchGenerateQRCodes()">
                <i class="fas fa-qrcode"></i>
                批量生成
              </button>
            </div>
            <div class="card-body">
              <div class="qr-management">
                <div class="qr-stats mb-md">
                  <div class="qr-stat-item">
                    <div class="qr-stat-value">1,156</div>
                    <div class="qr-stat-label">已生成</div>
                  </div>
                  <div class="qr-stat-item">
                    <div class="qr-stat-value">89</div>
                    <div class="qr-stat-label">待生成</div>
                  </div>
                  <div class="qr-stat-item">
                    <div class="qr-stat-value">2,340</div>
                    <div class="qr-stat-label">扫描次数</div>
                  </div>
                </div>

                <div class="qr-list">
                  <div class="qr-item">
                    <div class="qr-code">
                      <img src="https://via.placeholder.com/60x60/4F46E5/FFFFFF?text=QR" alt="二维码">
                    </div>
                    <div class="qr-info">
                      <div class="qr-equipment">中央空调主机-01</div>
                      <div class="qr-code-id">HVAC-001</div>
                      <div class="qr-generate-time">生成时间：2024-01-10</div>
                    </div>
                    <div class="qr-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="previewQRCode('HVAC-001')">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadQRCode('HVAC-001')">
                        <i class="fas fa-download"></i>
                        下载
                      </button>
                      <button class="btn btn-sm btn-outline-warning" onclick="regenerateQRCode('HVAC-001')">
                        <i class="fas fa-redo"></i>
                        重新生成
                      </button>
                    </div>
                  </div>

                  <div class="qr-item">
                    <div class="qr-code">
                      <img src="https://via.placeholder.com/60x60/10B981/FFFFFF?text=QR" alt="二维码">
                    </div>
                    <div class="qr-info">
                      <div class="qr-equipment">监控摄像头-大厅01</div>
                      <div class="qr-code-id">CAM-H01</div>
                      <div class="qr-generate-time">生成时间：2024-01-08</div>
                    </div>
                    <div class="qr-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="previewQRCode('CAM-H01')">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="downloadQRCode('CAM-H01')">
                        <i class="fas fa-download"></i>
                        下载
                      </button>
                      <button class="btn btn-sm btn-outline-info" onclick="printQRCode('CAM-H01')">
                        <i class="fas fa-print"></i>
                        打印
                      </button>
                    </div>
                  </div>

                  <div class="qr-item pending">
                    <div class="qr-code">
                      <div class="qr-placeholder">
                        <i class="fas fa-qrcode"></i>
                        <span>待生成</span>
                      </div>
                    </div>
                    <div class="qr-info">
                      <div class="qr-equipment">UPS不间断电源-02</div>
                      <div class="qr-code-id">UPS-002</div>
                      <div class="qr-generate-time">等待生成</div>
                    </div>
                    <div class="qr-actions">
                      <button class="btn btn-sm btn-primary" onclick="generateSingleQRCode('UPS-002')">
                        <i class="fas fa-plus"></i>
                        生成二维码
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('资产与设备管理');
        }
      }, 100);

      // 初始化资产设备管理功能
      initAssetEquipmentManagement();
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 初始化资产设备管理系统
    function initAssetEquipmentManagement() {
      // 初始化统计图表
      initStatusChart();
      // 初始化分类树
      initCategoryTree();
    }

    // 初始化设备状态图表
    function initStatusChart() {
      const canvas = document.getElementById('statusChart');
      if (canvas) {
        const ctx = canvas.getContext('2d');
        drawStatusPieChart(ctx);
      }
    }

    // 绘制设备状态饼图
    function drawStatusPieChart(ctx) {
      const width = ctx.canvas.width;
      const height = ctx.canvas.height;
      const centerX = width / 2;
      const centerY = height / 2;
      const radius = Math.min(width, height) / 2 - 20;

      const data = [
        { label: '正常运行', value: 1156, color: '#10b981' },
        { label: '维修中', value: 67, color: '#f59e0b' },
        { label: '备用', value: 12, color: '#3b82f6' },
        { label: '待报废', value: 22, color: '#ef4444' }
      ];

      const total = data.reduce((sum, item) => sum + item.value, 0);
      let currentAngle = -Math.PI / 2;

      ctx.clearRect(0, 0, width, height);

      data.forEach(item => {
        const sliceAngle = (item.value / total) * 2 * Math.PI;

        ctx.beginPath();
        ctx.moveTo(centerX, centerY);
        ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + sliceAngle);
        ctx.closePath();
        ctx.fillStyle = item.color;
        ctx.fill();

        currentAngle += sliceAngle;
      });
    }

    // 初始化分类树
    function initCategoryTree() {
      const toggles = document.querySelectorAll('.category-toggle');
      toggles.forEach(toggle => {
        toggle.addEventListener('click', function() {
          const node = this.closest('.category-node');
          const children = node.querySelector('.category-children');

          if (children) {
            const isExpanded = children.style.display !== 'none';
            children.style.display = isExpanded ? 'none' : 'block';
            this.className = isExpanded ? 'fas fa-chevron-right category-toggle' : 'fas fa-chevron-down category-toggle';

            if (isExpanded) {
              node.classList.remove('expanded');
            } else {
              node.classList.add('expanded');
            }
          }
        });
      });
    }

    // 重置筛选条件
    function resetFilters() {
      document.getElementById('categoryFilter').value = '';
      document.getElementById('statusFilter').value = '';
      document.getElementById('locationFilter').value = '';
      document.getElementById('searchKeyword').value = '';
      showNotification('筛选条件已重置', 'info');
    }

    // 搜索设备
    function searchEquipment() {
      const category = document.getElementById('categoryFilter').value;
      const status = document.getElementById('statusFilter').value;
      const location = document.getElementById('locationFilter').value;
      const keyword = document.getElementById('searchKeyword').value;

      console.log('搜索条件:', { category, status, location, keyword });
      showNotification('搜索完成，找到相关设备', 'success');
    }

    // 导出设备数据
    function exportEquipment() {
      console.log('导出设备数据');
      showNotification('设备数据导出已开始', 'info');
    }

    // 刷新设备列表
    function refreshEquipmentList() {
      console.log('刷新设备列表');
      showNotification('设备列表已刷新', 'success');
    }

    // 显示新增设备模态框
    function showAddEquipmentModal() {
      console.log('显示新增设备模态框');
      showNotification('新增设备功能开发中', 'info');
    }

    // 查看设备详情
    function viewEquipmentDetail(equipmentId) {
      console.log('查看设备详情:', equipmentId);
      showNotification('设备详情功能开发中', 'info');
    }

    // 显示维保记录
    function showMaintenanceRecord(equipmentId) {
      console.log('显示维保记录:', equipmentId);
      showNotification('维保记录功能开发中', 'info');
    }

    // 生成二维码
    function generateQRCode(equipmentId) {
      console.log('生成二维码:', equipmentId);
      showNotification('二维码生成成功', 'success');
    }

    // 编辑设备
    function editEquipment(equipmentId) {
      console.log('编辑设备:', equipmentId);
      showNotification('设备编辑功能开发中', 'info');
    }

    // 显示维修进度
    function showRepairProgress(equipmentId) {
      console.log('显示维修进度:', equipmentId);
      showNotification('维修进度功能开发中', 'info');
    }

    // 启用设备
    function activateEquipment(equipmentId) {
      console.log('启用设备:', equipmentId);
      showNotification('设备已启用', 'success');
    }

    // 显示报废流程
    function showScrapProcess(equipmentId) {
      console.log('显示报废流程:', equipmentId);
      showNotification('报废流程功能开发中', 'info');
    }

    // 延期报废
    function postponeScrap(equipmentId) {
      console.log('延期报废:', equipmentId);
      showNotification('报废已延期', 'success');
    }

    // 刷新统计
    function refreshStatistics() {
      console.log('刷新统计');
      initStatusChart();
      showNotification('统计数据已刷新', 'success');
    }

    // 查看紧急维保
    function viewUrgentMaintenance() {
      console.log('查看紧急维保');
      showNotification('紧急维保功能开发中', 'info');
    }

    // 查看即将到期维保
    function viewUpcomingMaintenance() {
      console.log('查看即将到期维保');
      showNotification('即将到期维保功能开发中', 'info');
    }

    // 查看维保计划
    function viewMaintenancePlan() {
      console.log('查看维保计划');
      showNotification('维保计划功能开发中', 'info');
    }

    // 显示新增维保记录模态框
    function showAddMaintenanceModal() {
      console.log('显示新增维保记录模态框');
      showNotification('新增维保记录功能开发中', 'info');
    }

    // 查看维保详情
    function viewMaintenanceDetail(maintenanceId) {
      console.log('查看维保详情:', maintenanceId);
      showNotification('维保详情功能开发中', 'info');
    }

    // 下载维保报告
    function downloadMaintenanceReport(maintenanceId) {
      console.log('下载维保报告:', maintenanceId);
      showNotification('维保报告下载已开始', 'info');
    }

    // 更新维保进度
    function updateMaintenanceProgress(maintenanceId) {
      console.log('更新维保进度:', maintenanceId);
      showNotification('维保进度更新功能开发中', 'info');
    }

    // 完成维保记录
    function completeMaintenanceRecord(maintenanceId) {
      console.log('完成维保记录:', maintenanceId);
      showNotification('维保记录已完成', 'success');
    }

    // 编辑维保计划
    function editMaintenanceSchedule(maintenanceId) {
      console.log('编辑维保计划:', maintenanceId);
      showNotification('维保计划编辑功能开发中', 'info');
    }

    // 取消维保计划
    function cancelMaintenanceSchedule(maintenanceId) {
      console.log('取消维保计划:', maintenanceId);
      showNotification('维保计划已取消', 'warning');
    }

    // 紧急安排维保
    function urgentScheduleMaintenance(maintenanceId) {
      console.log('紧急安排维保:', maintenanceId);
      showNotification('已安排紧急维保', 'success');
    }

    // 延期维保
    function postponeMaintenance(maintenanceId) {
      console.log('延期维保:', maintenanceId);
      showNotification('维保已延期', 'warning');
    }

    // 显示报废统计
    function showScrapStatistics() {
      console.log('显示报废统计');
      showNotification('报废统计功能开发中', 'info');
    }

    // 批准报废申请
    function approveScrapApplication(scrapId) {
      console.log('批准报废申请:', scrapId);
      showNotification('报废申请已批准', 'success');
    }

    // 拒绝报废申请
    function rejectScrapApplication(scrapId) {
      console.log('拒绝报废申请:', scrapId);
      showNotification('报废申请已拒绝', 'warning');
    }

    // 查看报废详情
    function viewScrapDetail(scrapId) {
      console.log('查看报废详情:', scrapId);
      showNotification('报废详情功能开发中', 'info');
    }

    // 进入处置流程
    function proceedToDisposal(scrapId) {
      console.log('进入处置流程:', scrapId);
      showNotification('已进入处置流程', 'success');
    }

    // 重新评估资产
    function revaluateAsset(scrapId) {
      console.log('重新评估资产:', scrapId);
      showNotification('资产重新评估功能开发中', 'info');
    }

    // 更新处置进度
    function updateDisposalProgress(scrapId) {
      console.log('更新处置进度:', scrapId);
      showNotification('处置进度更新功能开发中', 'info');
    }

    // 完成处置
    function completeDisposal(scrapId) {
      console.log('完成处置:', scrapId);
      showNotification('设备处置已完成', 'success');
    }

    // 查看报废报告
    function viewScrapReport(scrapId) {
      console.log('查看报废报告:', scrapId);
      showNotification('报废报告功能开发中', 'info');
    }

    // 下载处置证明
    function downloadScrapCertificate(scrapId) {
      console.log('下载处置证明:', scrapId);
      showNotification('处置证明下载已开始', 'info');
    }

    // 显示新增分类模态框
    function showAddCategoryModal() {
      console.log('显示新增分类模态框');
      showNotification('新增分类功能开发中', 'info');
    }

    // 编辑分类
    function editCategory(categoryId) {
      console.log('编辑分类:', categoryId);
      showNotification('分类编辑功能开发中', 'info');
    }

    // 删除分类
    function deleteCategory(categoryId) {
      if (confirm('确定要删除这个分类吗？')) {
        console.log('删除分类:', categoryId);
        showNotification('分类删除成功', 'success');
      }
    }

    // 批量生成二维码
    function batchGenerateQRCodes() {
      console.log('批量生成二维码');
      showNotification('批量生成二维码功能开发中', 'info');
    }

    // 预览二维码
    function previewQRCode(equipmentId) {
      console.log('预览二维码:', equipmentId);
      showNotification('二维码预览功能开发中', 'info');
    }

    // 下载二维码
    function downloadQRCode(equipmentId) {
      console.log('下载二维码:', equipmentId);
      showNotification('二维码下载已开始', 'info');
    }

    // 重新生成二维码
    function regenerateQRCode(equipmentId) {
      console.log('重新生成二维码:', equipmentId);
      showNotification('二维码重新生成成功', 'success');
    }

    // 打印二维码
    function printQRCode(equipmentId) {
      console.log('打印二维码:', equipmentId);
      showNotification('二维码打印功能开发中', 'info');
    }

    // 生成单个二维码
    function generateSingleQRCode(equipmentId) {
      console.log('生成单个二维码:', equipmentId);
      showNotification('二维码生成成功', 'success');
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);
      setTimeout(() => notification.classList.add('show'), 100);

      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
