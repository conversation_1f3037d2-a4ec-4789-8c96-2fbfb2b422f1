<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>车辆与停车管理 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">车辆与停车管理</h1>
          <p class="page-description">车辆档案管理、停车场管理、车牌识别记录、违停超速告警管理</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">总车位</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-car"></i>
              </div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">89</div>
                <div class="stat-label">已停车</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-parking"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">67</div>
                <div class="stat-label">空余车位</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-square"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">3</div>
                <div class="stat-label">违停告警</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 停车场实时监控和车辆管理 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-map text-primary"></i>
                停车场实时监控
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshParkingStatus()">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
                <button class="btn btn-outline-info btn-sm" onclick="showParkingMap()">
                  <i class="fas fa-expand"></i>
                  全屏地图
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 停车场区域选择 -->
              <div class="parking-area-tabs mb-md">
                <button class="area-tab active" onclick="switchParkingArea('A')">
                  <i class="fas fa-building"></i>
                  A区地下停车场
                  <span class="area-status">45/60</span>
                </button>
                <button class="area-tab" onclick="switchParkingArea('B')">
                  <i class="fas fa-building"></i>
                  B区地面停车场
                  <span class="area-status">32/56</span>
                </button>
                <button class="area-tab" onclick="switchParkingArea('C')">
                  <i class="fas fa-building"></i>
                  C区访客停车场
                  <span class="area-status">12/40</span>
                </button>
              </div>

              <!-- 停车场地图 -->
              <div class="parking-map">
                <div class="parking-grid" id="parkingGridA">
                  <!-- 第一排车位 -->
                  <div class="parking-row">
                    <div class="row-label">第1排</div>
                    <div class="parking-spaces">
                      <div class="parking-space occupied" data-space="A01" onclick="showSpaceDetail('A01')">
                        <div class="space-number">A01</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京A12345</span>
                        </div>
                      </div>
                      <div class="parking-space occupied" data-space="A02" onclick="showSpaceDetail('A02')">
                        <div class="space-number">A02</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京B67890</span>
                        </div>
                      </div>
                      <div class="parking-space empty" data-space="A03" onclick="showSpaceDetail('A03')">
                        <div class="space-number">A03</div>
                        <div class="space-status">
                          <i class="fas fa-square"></i>
                          <span>空闲</span>
                        </div>
                      </div>
                      <div class="parking-space occupied" data-space="A04" onclick="showSpaceDetail('A04')">
                        <div class="space-number">A04</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京C11111</span>
                        </div>
                      </div>
                      <div class="parking-space empty" data-space="A05" onclick="showSpaceDetail('A05')">
                        <div class="space-number">A05</div>
                        <div class="space-status">
                          <i class="fas fa-square"></i>
                          <span>空闲</span>
                        </div>
                      </div>
                      <div class="parking-space reserved" data-space="A06" onclick="showSpaceDetail('A06')">
                        <div class="space-number">A06</div>
                        <div class="space-status">
                          <i class="fas fa-bookmark"></i>
                          <span>预约</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 第二排车位 -->
                  <div class="parking-row">
                    <div class="row-label">第2排</div>
                    <div class="parking-spaces">
                      <div class="parking-space occupied" data-space="A07" onclick="showSpaceDetail('A07')">
                        <div class="space-number">A07</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京D22222</span>
                        </div>
                      </div>
                      <div class="parking-space empty" data-space="A08" onclick="showSpaceDetail('A08')">
                        <div class="space-number">A08</div>
                        <div class="space-status">
                          <i class="fas fa-square"></i>
                          <span>空闲</span>
                        </div>
                      </div>
                      <div class="parking-space occupied" data-space="A09" onclick="showSpaceDetail('A09')">
                        <div class="space-number">A09</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京E33333</span>
                        </div>
                      </div>
                      <div class="parking-space violation" data-space="A10" onclick="showSpaceDetail('A10')">
                        <div class="space-number">A10</div>
                        <div class="space-status">
                          <i class="fas fa-exclamation-triangle"></i>
                          <span>违停</span>
                        </div>
                      </div>
                      <div class="parking-space empty" data-space="A11" onclick="showSpaceDetail('A11')">
                        <div class="space-number">A11</div>
                        <div class="space-status">
                          <i class="fas fa-square"></i>
                          <span>空闲</span>
                        </div>
                      </div>
                      <div class="parking-space occupied" data-space="A12" onclick="showSpaceDetail('A12')">
                        <div class="space-number">A12</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京F44444</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <!-- 第三排车位 -->
                  <div class="parking-row">
                    <div class="row-label">第3排</div>
                    <div class="parking-spaces">
                      <div class="parking-space empty" data-space="A13" onclick="showSpaceDetail('A13')">
                        <div class="space-number">A13</div>
                        <div class="space-status">
                          <i class="fas fa-square"></i>
                          <span>空闲</span>
                        </div>
                      </div>
                      <div class="parking-space occupied" data-space="A14" onclick="showSpaceDetail('A14')">
                        <div class="space-number">A14</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京G55555</span>
                        </div>
                      </div>
                      <div class="parking-space empty" data-space="A15" onclick="showSpaceDetail('A15')">
                        <div class="space-number">A15</div>
                        <div class="space-status">
                          <i class="fas fa-square"></i>
                          <span>空闲</span>
                        </div>
                      </div>
                      <div class="parking-space occupied" data-space="A16" onclick="showSpaceDetail('A16')">
                        <div class="space-number">A16</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京H66666</span>
                        </div>
                      </div>
                      <div class="parking-space empty" data-space="A17" onclick="showSpaceDetail('A17')">
                        <div class="space-number">A17</div>
                        <div class="space-status">
                          <i class="fas fa-square"></i>
                          <span>空闲</span>
                        </div>
                      </div>
                      <div class="parking-space occupied" data-space="A18" onclick="showSpaceDetail('A18')">
                        <div class="space-number">A18</div>
                        <div class="space-status">
                          <i class="fas fa-car"></i>
                          <span>京J77777</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- 停车场图例 -->
                <div class="parking-legend">
                  <div class="legend-item">
                    <div class="legend-color occupied"></div>
                    <span>已停车</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color empty"></div>
                    <span>空闲</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color reserved"></div>
                    <span>预约</span>
                  </div>
                  <div class="legend-item">
                    <div class="legend-color violation"></div>
                    <span>违停</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-id-card text-success"></i>
                车辆档案管理
              </h3>
              <button class="btn btn-primary btn-sm" onclick="showAddVehicleModal()">
                <i class="fas fa-plus"></i>
                车辆登记
              </button>
            </div>
            <div class="card-body">
              <!-- 车辆搜索 -->
              <div class="vehicle-search mb-md">
                <div class="search-filters">
                  <div class="search-input">
                    <input type="text" class="form-control" placeholder="搜索车牌号、车主姓名..." id="vehicleSearch">
                    <button class="btn btn-outline-primary" onclick="searchVehicles()">
                      <i class="fas fa-search"></i>
                    </button>
                  </div>
                  <select class="form-control" id="vehicleTypeFilter" onchange="filterVehicles()">
                    <option value="">全部类型</option>
                    <option value="employee">员工车辆</option>
                    <option value="visitor">访客车辆</option>
                    <option value="service">服务车辆</option>
                  </select>
                  <select class="form-control" id="vehicleStatusFilter" onchange="filterVehicles()">
                    <option value="">全部状态</option>
                    <option value="in">在场</option>
                    <option value="out">离场</option>
                  </select>
                </div>
              </div>

              <!-- 车辆列表 -->
              <div class="vehicle-list">
                <div class="vehicle-item in-parking">
                  <div class="vehicle-info">
                    <div class="vehicle-header">
                      <div class="license-plate">京A12345</div>
                      <span class="vehicle-type employee">员工车辆</span>
                      <span class="status-badge success">在场</span>
                    </div>
                    <div class="vehicle-details">
                      <div class="detail-row">
                        <span class="detail-label">车主:</span>
                        <span class="detail-value">张三</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">部门:</span>
                        <span class="detail-value">技术部</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">车位:</span>
                        <span class="detail-value">A01</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">入场时间:</span>
                        <span class="detail-value">2024-01-15 08:30</span>
                      </div>
                    </div>
                  </div>
                  <div class="vehicle-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewVehicleDetail('京A12345')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="viewParkingHistory('京A12345')">
                      <i class="fas fa-history"></i>
                      记录
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="editVehicle('京A12345')">
                      <i class="fas fa-edit"></i>
                      编辑
                    </button>
                  </div>
                </div>

                <div class="vehicle-item out-parking">
                  <div class="vehicle-info">
                    <div class="vehicle-header">
                      <div class="license-plate">京B67890</div>
                      <span class="vehicle-type visitor">访客车辆</span>
                      <span class="status-badge secondary">离场</span>
                    </div>
                    <div class="vehicle-details">
                      <div class="detail-row">
                        <span class="detail-label">车主:</span>
                        <span class="detail-value">李四</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">访问部门:</span>
                        <span class="detail-value">市场部</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">停车时长:</span>
                        <span class="detail-value">2小时30分</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">离场时间:</span>
                        <span class="detail-value">2024-01-15 11:00</span>
                      </div>
                    </div>
                  </div>
                  <div class="vehicle-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewVehicleDetail('京B67890')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="viewParkingHistory('京B67890')">
                      <i class="fas fa-history"></i>
                      记录
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="generateInvoice('京B67890')">
                      <i class="fas fa-receipt"></i>
                      收费
                    </button>
                  </div>
                </div>

                <div class="vehicle-item in-parking violation">
                  <div class="vehicle-info">
                    <div class="vehicle-header">
                      <div class="license-plate">京C11111</div>
                      <span class="vehicle-type employee">员工车辆</span>
                      <span class="status-badge danger">违停</span>
                    </div>
                    <div class="vehicle-details">
                      <div class="detail-row">
                        <span class="detail-label">车主:</span>
                        <span class="detail-value">王五</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">部门:</span>
                        <span class="detail-value">财务部</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">违停位置:</span>
                        <span class="detail-value">消防通道</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">违停时长:</span>
                        <span class="detail-value">1小时15分</span>
                      </div>
                    </div>
                  </div>
                  <div class="vehicle-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewVehicleDetail('京C11111')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="sendViolationNotice('京C11111')">
                      <i class="fas fa-exclamation-triangle"></i>
                      违停通知
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="contactOwner('京C11111')">
                      <i class="fas fa-phone"></i>
                      联系车主
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 车牌识别记录和违停告警 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-camera text-info"></i>
                车牌识别记录
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshRecognitionLog()">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
                <button class="btn btn-outline-info btn-sm" onclick="exportRecognitionLog()">
                  <i class="fas fa-download"></i>
                  导出记录
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 识别记录筛选 -->
              <div class="recognition-filters mb-md">
                <div class="filter-row">
                  <select class="form-control" id="cameraFilter" onchange="filterRecognitionLog()">
                    <option value="">全部摄像头</option>
                    <option value="entrance">入口摄像头</option>
                    <option value="exit">出口摄像头</option>
                    <option value="internal">内部摄像头</option>
                  </select>
                  <select class="form-control" id="actionFilter" onchange="filterRecognitionLog()">
                    <option value="">全部动作</option>
                    <option value="enter">入场</option>
                    <option value="exit">出场</option>
                    <option value="patrol">巡检</option>
                  </select>
                  <input type="date" class="form-control" id="dateFilter" value="2024-01-15" onchange="filterRecognitionLog()">
                </div>
              </div>

              <!-- 识别记录列表 -->
              <div class="recognition-log">
                <div class="log-item enter">
                  <div class="log-time">
                    <div class="time-value">08:30:25</div>
                    <div class="date-value">2024-01-15</div>
                  </div>
                  <div class="log-image">
                    <img src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=120&h=80&fit=crop&crop=center" alt="车牌识别">
                    <div class="recognition-confidence">98%</div>
                  </div>
                  <div class="log-info">
                    <div class="license-plate-detected">京A12345</div>
                    <div class="log-details">
                      <div class="detail-item">
                        <i class="fas fa-camera"></i>
                        <span>入口摄像头-01</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>车辆入场</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-user"></i>
                        <span>张三 (技术部)</span>
                      </div>
                    </div>
                  </div>
                  <div class="log-status">
                    <span class="status-badge success">识别成功</span>
                    <div class="log-actions">
                      <button class="btn btn-xs btn-outline-primary" onclick="viewRecognitionDetail('LOG_001')">
                        <i class="fas fa-eye"></i>
                        详情
                      </button>
                    </div>
                  </div>
                </div>

                <div class="log-item exit">
                  <div class="log-time">
                    <div class="time-value">11:00:15</div>
                    <div class="date-value">2024-01-15</div>
                  </div>
                  <div class="log-image">
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=120&h=80&fit=crop&crop=center" alt="车牌识别">
                    <div class="recognition-confidence">95%</div>
                  </div>
                  <div class="log-info">
                    <div class="license-plate-detected">京B67890</div>
                    <div class="log-details">
                      <div class="detail-item">
                        <i class="fas fa-camera"></i>
                        <span>出口摄像头-01</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-sign-out-alt"></i>
                        <span>车辆出场</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-clock"></i>
                        <span>停车时长: 2小时30分</span>
                      </div>
                    </div>
                  </div>
                  <div class="log-status">
                    <span class="status-badge info">已出场</span>
                    <div class="log-actions">
                      <button class="btn btn-xs btn-outline-primary" onclick="viewRecognitionDetail('LOG_002')">
                        <i class="fas fa-eye"></i>
                        详情
                      </button>
                    </div>
                  </div>
                </div>

                <div class="log-item patrol">
                  <div class="log-time">
                    <div class="time-value">14:15:30</div>
                    <div class="date-value">2024-01-15</div>
                  </div>
                  <div class="log-image">
                    <img src="https://images.unsplash.com/photo-**********-fcd25c85cd64?w=120&h=80&fit=crop&crop=center" alt="车牌识别">
                    <div class="recognition-confidence">92%</div>
                  </div>
                  <div class="log-info">
                    <div class="license-plate-detected">京C11111</div>
                    <div class="log-details">
                      <div class="detail-item">
                        <i class="fas fa-camera"></i>
                        <span>内部摄像头-03</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>违停检测</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-map-marker-alt"></i>
                        <span>消防通道</span>
                      </div>
                    </div>
                  </div>
                  <div class="log-status">
                    <span class="status-badge warning">违停告警</span>
                    <div class="log-actions">
                      <button class="btn btn-xs btn-outline-danger" onclick="handleViolation('LOG_003')">
                        <i class="fas fa-exclamation-triangle"></i>
                        处理
                      </button>
                    </div>
                  </div>
                </div>

                <div class="log-item enter">
                  <div class="log-time">
                    <div class="time-value">15:45:10</div>
                    <div class="date-value">2024-01-15</div>
                  </div>
                  <div class="log-image">
                    <img src="https://images.unsplash.com/photo-1502877338535-766e1452684a?w=120&h=80&fit=crop&crop=center" alt="车牌识别">
                    <div class="recognition-confidence">89%</div>
                  </div>
                  <div class="log-info">
                    <div class="license-plate-detected">京D22222</div>
                    <div class="log-details">
                      <div class="detail-item">
                        <i class="fas fa-camera"></i>
                        <span>入口摄像头-02</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-sign-in-alt"></i>
                        <span>访客入场</span>
                      </div>
                      <div class="detail-item">
                        <i class="fas fa-user-friends"></i>
                        <span>访客车辆</span>
                      </div>
                    </div>
                  </div>
                  <div class="log-status">
                    <span class="status-badge primary">访客登记</span>
                    <div class="log-actions">
                      <button class="btn btn-xs btn-outline-success" onclick="registerVisitor('LOG_004')">
                        <i class="fas fa-user-plus"></i>
                        登记
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                违停告警管理
              </h3>
              <button class="btn btn-outline-danger btn-sm" onclick="showViolationSettings()">
                <i class="fas fa-cog"></i>
                告警设置
              </button>
            </div>
            <div class="card-body">
              <!-- 告警统计 -->
              <div class="violation-stats mb-md">
                <div class="stat-item urgent">
                  <div class="stat-icon">
                    <i class="fas fa-exclamation-circle"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">3</div>
                    <div class="stat-label">紧急告警</div>
                  </div>
                </div>
                <div class="stat-item warning">
                  <div class="stat-icon">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">7</div>
                    <div class="stat-label">一般告警</div>
                  </div>
                </div>
                <div class="stat-item resolved">
                  <div class="stat-icon">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">15</div>
                    <div class="stat-label">已处理</div>
                  </div>
                </div>
              </div>

              <!-- 违停告警列表 -->
              <div class="violation-list">
                <div class="violation-item urgent">
                  <div class="violation-header">
                    <div class="violation-type">
                      <i class="fas fa-fire"></i>
                      <span>消防通道违停</span>
                    </div>
                    <div class="violation-time">1小时15分前</div>
                    <span class="priority-badge urgent">紧急</span>
                  </div>
                  <div class="violation-content">
                    <div class="violation-info">
                      <div class="license-plate">京C11111</div>
                      <div class="violation-details">
                        <div class="detail-row">
                          <span class="detail-label">车主:</span>
                          <span class="detail-value">王五 (财务部)</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">位置:</span>
                          <span class="detail-value">A区地下停车场-消防通道</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">违停时长:</span>
                          <span class="detail-value">1小时15分</span>
                        </div>
                      </div>
                    </div>
                    <div class="violation-image">
                      <img src="https://images.unsplash.com/photo-**********-fcd25c85cd64?w=100&h=75&fit=crop&crop=center" alt="违停现场">
                    </div>
                  </div>
                  <div class="violation-actions">
                    <button class="btn btn-sm btn-danger" onclick="sendUrgentNotice('VIO_001')">
                      <i class="fas fa-bell"></i>
                      紧急通知
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="contactSecurity('VIO_001')">
                      <i class="fas fa-shield-alt"></i>
                      通知保安
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewViolationDetail('VIO_001')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                  </div>
                </div>

                <div class="violation-item warning">
                  <div class="violation-header">
                    <div class="violation-type">
                      <i class="fas fa-clock"></i>
                      <span>超时停车</span>
                    </div>
                    <div class="violation-time">30分钟前</div>
                    <span class="priority-badge warning">一般</span>
                  </div>
                  <div class="violation-content">
                    <div class="violation-info">
                      <div class="license-plate">京E33333</div>
                      <div class="violation-details">
                        <div class="detail-row">
                          <span class="detail-label">车主:</span>
                          <span class="detail-value">赵六 (访客)</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">位置:</span>
                          <span class="detail-value">C区访客停车场-C15</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">超时:</span>
                          <span class="detail-value">超出预约时间2小时</span>
                        </div>
                      </div>
                    </div>
                    <div class="violation-image">
                      <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=100&h=75&fit=crop&crop=center" alt="超时停车">
                    </div>
                  </div>
                  <div class="violation-actions">
                    <button class="btn btn-sm btn-warning" onclick="sendTimeoutNotice('VIO_002')">
                      <i class="fas fa-clock"></i>
                      超时提醒
                    </button>
                    <button class="btn btn-sm btn-info" onclick="extendParkingTime('VIO_002')">
                      <i class="fas fa-plus-circle"></i>
                      延时申请
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="viewViolationDetail('VIO_002')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                  </div>
                </div>

                <div class="violation-item resolved">
                  <div class="violation-header">
                    <div class="violation-type">
                      <i class="fas fa-ban"></i>
                      <span>占用残疾人车位</span>
                    </div>
                    <div class="violation-time">2小时前</div>
                    <span class="priority-badge resolved">已处理</span>
                  </div>
                  <div class="violation-content">
                    <div class="violation-info">
                      <div class="license-plate">京F44444</div>
                      <div class="violation-details">
                        <div class="detail-row">
                          <span class="detail-label">车主:</span>
                          <span class="detail-value">孙七 (行政部)</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">处理方式:</span>
                          <span class="detail-value">车主已移车</span>
                        </div>
                        <div class="detail-row">
                          <span class="detail-label">处理时间:</span>
                          <span class="detail-value">2024-01-15 13:30</span>
                        </div>
                      </div>
                    </div>
                    <div class="violation-status">
                      <i class="fas fa-check-circle"></i>
                      <span>已解决</span>
                    </div>
                  </div>
                  <div class="violation-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewViolationDetail('VIO_003')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="viewResolutionReport('VIO_003')">
                      <i class="fas fa-file-alt"></i>
                      处理报告
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 停车收费和统计分析 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-money-bill text-success"></i>
                停车收费管理
              </h3>
              <button class="btn btn-outline-success btn-sm" onclick="showFeeSettings()">
                <i class="fas fa-cog"></i>
                收费设置
              </button>
            </div>
            <div class="card-body">
              <!-- 收费统计 -->
              <div class="fee-stats mb-md">
                <div class="fee-summary">
                  <div class="summary-item">
                    <div class="summary-label">今日收费</div>
                    <div class="summary-value">¥1,280</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">本月收费</div>
                    <div class="summary-value">¥18,560</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">待缴费</div>
                    <div class="summary-value">¥320</div>
                  </div>
                </div>
              </div>

              <!-- 收费记录 -->
              <div class="fee-records">
                <div class="record-item paid">
                  <div class="record-info">
                    <div class="license-plate">京B67890</div>
                    <div class="record-details">
                      <div class="detail-item">停车时长: 2小时30分</div>
                      <div class="detail-item">收费标准: ¥5/小时</div>
                      <div class="detail-item">应收金额: ¥15</div>
                    </div>
                  </div>
                  <div class="record-status">
                    <span class="status-badge success">已缴费</span>
                    <div class="payment-time">2024-01-15 11:00</div>
                  </div>
                  <div class="record-actions">
                    <button class="btn btn-xs btn-outline-primary" onclick="viewFeeDetail('FEE_001')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-xs btn-outline-info" onclick="printReceipt('FEE_001')">
                      <i class="fas fa-print"></i>
                      打印
                    </button>
                  </div>
                </div>

                <div class="record-item pending">
                  <div class="record-info">
                    <div class="license-plate">京D22222</div>
                    <div class="record-details">
                      <div class="detail-item">停车时长: 4小时15分</div>
                      <div class="detail-item">收费标准: ¥8/小时</div>
                      <div class="detail-item">应收金额: ¥35</div>
                    </div>
                  </div>
                  <div class="record-status">
                    <span class="status-badge warning">待缴费</span>
                    <div class="payment-time">离场时缴费</div>
                  </div>
                  <div class="record-actions">
                    <button class="btn btn-xs btn-success" onclick="processPayment('FEE_002')">
                      <i class="fas fa-credit-card"></i>
                      收费
                    </button>
                    <button class="btn btn-xs btn-outline-primary" onclick="viewFeeDetail('FEE_002')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-chart-bar text-info"></i>
                统计分析
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="refreshStatistics()">
                <i class="fas fa-sync-alt"></i>
                刷新数据
              </button>
            </div>
            <div class="card-body">
              <!-- 停车统计图表 -->
              <div class="parking-chart mb-md">
                <canvas id="parkingChart" width="300" height="200"></canvas>
              </div>

              <!-- 统计数据 -->
              <div class="statistics-summary">
                <div class="stat-row">
                  <div class="stat-item">
                    <div class="stat-label">今日进出车辆</div>
                    <div class="stat-value">156次</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">平均停车时长</div>
                    <div class="stat-value">3.2小时</div>
                  </div>
                </div>
                <div class="stat-row">
                  <div class="stat-item">
                    <div class="stat-label">车位利用率</div>
                    <div class="stat-value">72.5%</div>
                  </div>
                  <div class="stat-item">
                    <div class="stat-label">违停处理率</div>
                    <div class="stat-value">95.2%</div>
                  </div>
                </div>
              </div>

              <!-- 热门时段分析 -->
              <div class="time-analysis">
                <h6>停车高峰时段</h6>
                <div class="time-slots">
                  <div class="time-slot peak">
                    <div class="time-range">08:00-10:00</div>
                    <div class="usage-rate">95%</div>
                  </div>
                  <div class="time-slot high">
                    <div class="time-range">14:00-16:00</div>
                    <div class="usage-rate">78%</div>
                  </div>
                  <div class="time-slot normal">
                    <div class="time-range">10:00-14:00</div>
                    <div class="usage-rate">65%</div>
                  </div>
                  <div class="time-slot low">
                    <div class="time-range">18:00-08:00</div>
                    <div class="usage-rate">25%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('车辆与停车管理');
        }
      }, 100);

      // 初始化停车场管理系统功能
      initParkingManagementSystem();
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 初始化停车场管理系统
    function initParkingManagementSystem() {
      // 初始化停车统计图表
      initParkingChart();
      // 初始化停车场区域切换
      initParkingAreaTabs();
    }

    // 初始化停车统计图表
    function initParkingChart() {
      const canvas = document.getElementById('parkingChart');
      if (canvas) {
        const ctx = canvas.getContext('2d');
        drawParkingChart(ctx);
      }
    }

    // 绘制停车统计图表
    function drawParkingChart(ctx) {
      const width = ctx.canvas.width;
      const height = ctx.canvas.height;
      const padding = 40;

      ctx.clearRect(0, 0, width, height);

      // 绘制网格
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 1;

      // 垂直网格线
      for (let i = 0; i <= 6; i++) {
        const x = padding + (i / 6) * (width - 2 * padding);
        ctx.beginPath();
        ctx.moveTo(x, padding);
        ctx.lineTo(x, height - padding);
        ctx.stroke();
      }

      // 水平网格线
      for (let i = 0; i <= 4; i++) {
        const y = padding + (i / 4) * (height - 2 * padding);
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();
      }

      // 绘制停车数据线
      const data = [30, 45, 65, 85, 75, 60, 40];
      ctx.strokeStyle = '#10b981';
      ctx.lineWidth = 3;
      ctx.beginPath();

      data.forEach((value, index) => {
        const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
        const y = height - padding - (value / 100) * (height - 2 * padding);

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // 绘制数据点
      ctx.fillStyle = '#10b981';
      data.forEach((value, index) => {
        const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
        const y = height - padding - (value / 100) * (height - 2 * padding);

        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();
      });
    }

    // 初始化停车场区域切换
    function initParkingAreaTabs() {
      const areaTabs = document.querySelectorAll('.area-tab');
      areaTabs.forEach(tab => {
        tab.addEventListener('click', function() {
          areaTabs.forEach(t => t.classList.remove('active'));
          this.classList.add('active');
        });
      });
    }

    // 停车场实时监控功能
    function refreshParkingStatus() {
      console.log('刷新停车场状态');
      showNotification('停车场状态已刷新', 'success');
    }

    function showParkingMap() {
      console.log('显示全屏地图');
      showNotification('全屏地图功能开发中', 'info');
    }

    function switchParkingArea(area) {
      console.log('切换停车区域:', area);
      showNotification(`已切换到${area}区停车场`, 'success');
    }

    function showSpaceDetail(spaceId) {
      console.log('显示车位详情:', spaceId);
      showNotification('车位详情功能开发中', 'info');
    }

    // 车辆档案管理功能
    function showAddVehicleModal() {
      console.log('显示车辆登记模态框');
      showNotification('车辆登记功能开发中', 'info');
    }

    function searchVehicles() {
      const keyword = document.getElementById('vehicleSearch').value;
      console.log('搜索车辆:', keyword);
      showNotification('车辆搜索完成', 'success');
    }

    function filterVehicles() {
      const type = document.getElementById('vehicleTypeFilter').value;
      const status = document.getElementById('vehicleStatusFilter').value;
      console.log('筛选车辆:', type, status);
      showNotification('车辆筛选完成', 'success');
    }

    function viewVehicleDetail(licensePlate) {
      console.log('查看车辆详情:', licensePlate);
      showNotification('车辆详情功能开发中', 'info');
    }

    function viewParkingHistory(licensePlate) {
      console.log('查看停车记录:', licensePlate);
      showNotification('停车记录功能开发中', 'info');
    }

    function editVehicle(licensePlate) {
      console.log('编辑车辆信息:', licensePlate);
      showNotification('车辆编辑功能开发中', 'info');
    }

    function generateInvoice(licensePlate) {
      console.log('生成收费单:', licensePlate);
      showNotification('收费单生成功能开发中', 'info');
    }

    function sendViolationNotice(licensePlate) {
      console.log('发送违停通知:', licensePlate);
      showNotification('违停通知已发送', 'warning');
    }

    function contactOwner(licensePlate) {
      console.log('联系车主:', licensePlate);
      showNotification('正在联系车主...', 'info');
    }

    // 车牌识别记录功能
    function refreshRecognitionLog() {
      console.log('刷新识别记录');
      showNotification('识别记录已刷新', 'success');
    }

    function exportRecognitionLog() {
      console.log('导出识别记录');
      showNotification('识别记录导出功能开发中', 'info');
    }

    function filterRecognitionLog() {
      const camera = document.getElementById('cameraFilter').value;
      const action = document.getElementById('actionFilter').value;
      const date = document.getElementById('dateFilter').value;
      console.log('筛选识别记录:', camera, action, date);
      showNotification('识别记录筛选完成', 'success');
    }

    function viewRecognitionDetail(logId) {
      console.log('查看识别详情:', logId);
      showNotification('识别详情功能开发中', 'info');
    }

    function handleViolation(logId) {
      console.log('处理违停:', logId);
      showNotification('违停处理功能开发中', 'info');
    }

    function registerVisitor(logId) {
      console.log('访客登记:', logId);
      showNotification('访客登记功能开发中', 'info');
    }

    // 违停告警管理功能
    function showViolationSettings() {
      console.log('显示告警设置');
      showNotification('告警设置功能开发中', 'info');
    }

    function sendUrgentNotice(violationId) {
      console.log('发送紧急通知:', violationId);
      showNotification('紧急通知已发送', 'danger');
    }

    function contactSecurity(violationId) {
      console.log('通知保安:', violationId);
      showNotification('保安已收到通知', 'warning');
    }

    function viewViolationDetail(violationId) {
      console.log('查看违停详情:', violationId);
      showNotification('违停详情功能开发中', 'info');
    }

    function sendTimeoutNotice(violationId) {
      console.log('发送超时提醒:', violationId);
      showNotification('超时提醒已发送', 'warning');
    }

    function extendParkingTime(violationId) {
      console.log('延时申请:', violationId);
      showNotification('延时申请功能开发中', 'info');
    }

    function viewResolutionReport(violationId) {
      console.log('查看处理报告:', violationId);
      showNotification('处理报告功能开发中', 'info');
    }

    // 停车收费管理功能
    function showFeeSettings() {
      console.log('显示收费设置');
      showNotification('收费设置功能开发中', 'info');
    }

    function viewFeeDetail(feeId) {
      console.log('查看收费详情:', feeId);
      showNotification('收费详情功能开发中', 'info');
    }

    function printReceipt(feeId) {
      console.log('打印收费单:', feeId);
      showNotification('收费单打印功能开发中', 'info');
    }

    function processPayment(feeId) {
      console.log('处理缴费:', feeId);
      showNotification('缴费处理功能开发中', 'info');
    }

    // 统计分析功能
    function refreshStatistics() {
      console.log('刷新统计数据');
      initParkingChart();
      showNotification('统计数据已刷新', 'success');
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'times-circle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);
      setTimeout(() => notification.classList.add('show'), 100);

      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
