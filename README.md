# 智慧楼宇管理平台原型

这是一个完整的智慧楼宇管理平台前端原型，包含了所有主要功能模块的高保真界面设计。

## 项目概述

智慧楼宇管理平台是一个综合性的楼宇管理系统，集成了视频监控、门禁控制、环境监测、能源管理、设备维护等多个子系统，为楼宇管理提供统一的操作界面和数据展示。

## 功能模块

### 1. 首页 (Dashboard)
- 数据总览（告警总数、工单数、设备状态、能耗趋势等）
- 快捷入口（功能模块跳转卡片）
- 最新动态（系统消息/报警/工单进展）
- 自定义图表（能耗、通行、事件等）
- 系统运行状态概览

### 2. 功能应用模块

#### 地图监控中心
- 实时地图展示（支持多图层控制）
- 视频/告警/门禁/人员等资源上图
- 告警图层联动、图上反控
- GPS轨迹回放（单兵/车辆）

#### 事件与联动中心
- 实时报警事件查看
- 历史事件查询与导出
- 联动规则配置（弹窗/抓拍/录像等）
- 视频联动操作

#### 视频监控系统
- 视频设备管理
- 实时预览与录像回放
- 视频上墙与轮巡控制
- 视频联动配置

#### 门禁与通行控制
- 门禁设备配置与实时控制
- 通行权限设置（组织/人员）
- 开门记录与门状态查询
- 区域互锁/反潜回设置

#### 人员布控系统
- 布控策略配置（陌生人/重点人员）
- 实时抓拍与布控告警
- 人脸轨迹回溯
- 人脸检索与身份比对

#### 楼宇与场所管理
- 建筑结构建模（园区/楼栋/楼层）
- 场所管理与二维码生成
- 场所资源绑定（视频/门禁等）

#### 维保与工单系统
- 报修工单处理流程（申请→派单→维修→评价）
- 维修计划设置与执行
- 工单状态跟踪
- 服务统计报表

#### 动环监控系统
- 设备数据监控（温湿度、UPS、空调等）
- 动环告警与联动配置
- 历史数据分析图表
- 报警记录查询

#### 能源管理系统
- 抄表数据采集（电、水、气）
- 能耗趋势图表（日报/月报）
- 用能超标告警
- 用能分摊设置（按楼层/部门）

#### 资产与设备管理
- 设备台账管理（添加/导入/状态）
- 设备维修保养记录
- 报废流程管理
- 设备二维码标识打印

#### 信息发布系统
- 素材管理（图片/视频/网页）
- 节目编辑器与排班
- 播放计划管理（定时/插播）
- 信息终端远程控制

#### 车辆与停车管理
- 车辆档案管理
- 停车场管理（车位、道闸）
- 车牌识别记录
- 违停/超速告警管理
- 访客预约与进出记录

### 3. 系统管理中心

#### 用户与权限管理
- 用户账号管理（账号、权限、组织、绑定人脸/卡片等）
- 角色与权限配置（功能菜单、操作权限）
- 组织架构管理

#### 登录与安全策略
- 密码策略配置
- 验证码设置
- IP限制
- 登录安全配置

#### 系统参数配置
- 地图服务配置
- 时间校准
- 系统基础参数设置

#### 日志与通知配置
- 操作日志管理
- 登录日志查询
- 设备日志分析
- 消息与通知配置

#### 资源接入与管理
- 视频、门禁等资源注册
- 移动端权限分配与入口配置
- 设备接入管理

## 技术特性

### 设计规范
- **现代化UI设计**：采用扁平化设计风格，简洁专业
- **响应式布局**：支持桌面端、平板和移动端适配
- **统一色彩系统**：深蓝主色调配合科技感辅助色
- **组件化架构**：统一的导航栏、头部、侧边栏等通用组件

### 技术栈
- **HTML5 + CSS3**：现代化前端技术
- **JavaScript ES6+**：原生JavaScript，无框架依赖
- **Font Awesome 6.0**：丰富的图标库
- **Chart.js**：数据可视化图表
- **Inter字体**：现代化字体系统

### 交互特性
- **侧边栏折叠**：支持侧边栏展开/折叠
- **面包屑导航**：清晰的页面层级导航
- **实时通知**：系统消息和告警通知
- **全屏模式**：支持地图和视频全屏显示
- **搜索筛选**：各模块支持数据筛选和搜索

## 项目结构

```
智慧楼宇管理平台/
├── index.html                    # 首页
├── assets/                       # 静态资源
│   ├── css/
│   │   └── main.css             # 主样式文件
│   └── js/
│       └── main.js              # 主JavaScript文件
├── components/                   # 通用组件
│   ├── sidebar.html             # 侧边栏组件
│   └── header.html              # 头部组件
├── 功能应用模块/
│   ├── map-monitoring.html      # 地图监控中心
│   ├── event-linkage.html       # 事件与联动中心
│   ├── video-monitoring.html    # 视频监控系统
│   ├── access-control.html      # 门禁与通行控制
│   ├── personnel-control.html   # 人员布控系统
│   ├── building-management.html # 楼宇与场所管理
│   ├── maintenance-workorder.html # 维保与工单系统
│   ├── environment-monitoring.html # 动环监控系统
│   ├── energy-management.html   # 能源管理系统
│   ├── asset-equipment.html     # 资产与设备管理
│   ├── information-release.html # 信息发布系统
│   └── vehicle-parking.html     # 车辆与停车管理
├── 系统管理中心/
│   ├── user-permission.html     # 用户与权限管理
│   ├── security-policy.html     # 登录与安全策略
│   ├── system-config.html       # 系统参数配置
│   ├── log-notification.html    # 日志与通知配置
│   └── resource-management.html # 资源接入与管理
└── README.md                    # 项目说明文档
```

## 快速开始

### 1. 环境要求
- 现代化浏览器（Chrome、Firefox、Safari、Edge）
- 本地Web服务器（可选，用于开发调试）

### 2. 运行方式

#### 方式一：直接打开
直接用浏览器打开 `index.html` 文件即可查看效果。

#### 方式二：本地服务器（推荐）
```bash
# 使用Python启动本地服务器
python3 -m http.server 8000

# 或使用Node.js
npx http-server

# 然后在浏览器中访问
http://localhost:8000
```

### 3. 功能演示
1. 打开首页查看整体布局和数据概览
2. 点击侧边栏导航体验各个功能模块
3. 测试响应式设计（调整浏览器窗口大小）
4. 体验交互功能（侧边栏折叠、通知下拉等）

## 开发说明

### 自定义配置
- 修改 `assets/css/main.css` 中的CSS变量来调整色彩主题
- 在 `assets/js/main.js` 中添加新的交互功能
- 通过修改 `components/` 中的组件来调整通用布局

### 扩展功能
- 添加新页面：复制现有页面模板，修改内容
- 集成后端API：在JavaScript中添加数据请求逻辑
- 添加新图表：使用Chart.js库创建数据可视化

### 部署建议
- 生产环境建议使用Nginx或Apache作为Web服务器
- 可以集成到现有的后端系统中作为前端界面
- 支持Docker容器化部署

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器支持

## 许可证

本项目仅用于演示和学习目的。

## 联系方式

如有问题或建议，请联系开发团队。
