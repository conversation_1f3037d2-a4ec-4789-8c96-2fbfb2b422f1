<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>日志与通知配置 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">日志管理</h1>
          <p class="page-description">操作日志、登录日志、设备日志、消息与通知配置</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">12,456</div>
                <div class="stat-label">操作日志</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-clipboard-list"></i>
              </div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">3,245</div>
                <div class="stat-label">登录日志</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-sign-in-alt"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">8,967</div>
                <div class="stat-label">设备日志</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-microchip"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">错误日志</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 日志筛选和搜索 -->
        <div class="card mb-lg">
          <div class="card-header">
            <h3 class="card-title">日志筛选</h3>
          </div>
          <div class="card-body">
            <div class="filter-row">
              <div class="filter-group">
                <label class="filter-label">日志类型</label>
                <select class="form-select" id="logTypeFilter">
                  <option value="">全部类型</option>
                  <option value="operation">操作日志</option>
                  <option value="login">登录日志</option>
                  <option value="device">设备日志</option>
                  <option value="error">错误日志</option>
                </select>
              </div>
              <div class="filter-group">
                <label class="filter-label">日志级别</label>
                <select class="form-select" id="logLevelFilter">
                  <option value="">全部级别</option>
                  <option value="info">信息</option>
                  <option value="warning">警告</option>
                  <option value="error">错误</option>
                  <option value="critical">严重</option>
                </select>
              </div>
              <div class="filter-group">
                <label class="filter-label">时间范围</label>
                <select class="form-select" id="timeRangeFilter">
                  <option value="today">今天</option>
                  <option value="week">最近7天</option>
                  <option value="month">最近30天</option>
                  <option value="custom">自定义</option>
                </select>
              </div>
              <div class="filter-group">
                <label class="filter-label">搜索关键词</label>
                <div class="search-input-group">
                  <input type="text" class="form-input" id="searchKeyword" placeholder="搜索用户、操作、设备等...">
                  <button class="btn btn-primary" id="searchBtn">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 日志列表 -->
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">日志记录</h3>
            <div class="card-actions">
              <button class="btn btn-secondary" id="refreshLogsBtn">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button class="btn btn-primary" id="exportLogsBtn">
                <i class="fas fa-download"></i>
                导出日志
              </button>
            </div>
          </div>
          <div class="card-body">
            <!-- 日志列表容器 -->
            <div class="logs-container" id="logsContainer">
              <!-- 日志数据将通过JavaScript动态加载 -->
            </div>

            <!-- 分页控件 -->
            <div class="logs-pagination">
              <div class="pagination-info">
                显示第 <span id="currentStart">1</span> - <span id="currentEnd">20</span> 条，共 <span id="totalRecords">1,245</span> 条记录
              </div>
              <div class="pagination-controls">
                <button class="btn btn-sm btn-secondary" id="prevPageBtn" disabled>
                  <i class="fas fa-chevron-left"></i>
                  上一页
                </button>
                <div class="pagination-numbers" id="paginationNumbers">
                  <button class="btn btn-sm btn-primary">1</button>
                  <button class="btn btn-sm btn-secondary">2</button>
                  <button class="btn btn-sm btn-secondary">3</button>
                  <span>...</span>
                  <button class="btn btn-sm btn-secondary">25</button>
                </div>
                <button class="btn btn-sm btn-secondary" id="nextPageBtn">
                  下一页
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 通知配置 -->
        <div class="card mt-lg">
          <div class="card-header">
            <h3 class="card-title">通知配置</h3>
          </div>
          <div class="card-body">
            <div class="notification-config-grid">
              <div class="config-section">
                <h4 class="config-title">邮件通知</h4>
                <div class="config-item">
                  <div class="config-label">
                    <span>系统告警通知</span>
                    <small>当系统出现严重错误时发送邮件</small>
                  </div>
                  <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                  </label>
                </div>
                <div class="config-item">
                  <div class="config-label">
                    <span>登录异常通知</span>
                    <small>检测到异常登录时发送邮件</small>
                  </div>
                  <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                  </label>
                </div>
                <div class="config-item">
                  <div class="config-label">
                    <span>设备离线通知</span>
                    <small>设备长时间离线时发送邮件</small>
                  </div>
                  <label class="switch">
                    <input type="checkbox">
                    <span class="slider"></span>
                  </label>
                </div>
              </div>

              <div class="config-section">
                <h4 class="config-title">短信通知</h4>
                <div class="config-item">
                  <div class="config-label">
                    <span>紧急告警通知</span>
                    <small>火灾、入侵等紧急情况发送短信</small>
                  </div>
                  <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                  </label>
                </div>
                <div class="config-item">
                  <div class="config-label">
                    <span>系统维护通知</span>
                    <small>系统维护前发送短信提醒</small>
                  </div>
                  <label class="switch">
                    <input type="checkbox">
                    <span class="slider"></span>
                  </label>
                </div>
              </div>

              <div class="config-section">
                <h4 class="config-title">站内消息</h4>
                <div class="config-item">
                  <div class="config-label">
                    <span>工单状态更新</span>
                    <small>工单状态变更时发送站内消息</small>
                  </div>
                  <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                  </label>
                </div>
                <div class="config-item">
                  <div class="config-label">
                    <span>设备状态变更</span>
                    <small>设备上线、离线时发送站内消息</small>
                  </div>
                  <label class="switch">
                    <input type="checkbox" checked>
                    <span class="slider"></span>
                  </label>
                </div>
              </div>
            </div>

            <div class="config-actions mt-lg">
              <button class="btn btn-primary">
                <i class="fas fa-save"></i>
                保存配置
              </button>
              <button class="btn btn-secondary">
                <i class="fas fa-paper-plane"></i>
                测试通知
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="assets/js/main.js"></script>
  <script>
    // 全局变量
    let currentPage = 1;
    let pageSize = 20;
    let totalRecords = 1245;
    let filteredLogs = [];
    let allLogs = [];

    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');

      // 初始化页面
      setTimeout(() => {
        setActiveNavigation();
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('日志管理');
        }

        // 初始化日志管理功能
        initLogManagement();
      }, 100);
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 设置当前页面导航激活状态
    function setActiveNavigation() {
      document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
      });
      const logNavItem = document.querySelector('.nav-item[data-page="log-notification"]');
      if (logNavItem) {
        logNavItem.classList.add('active');
      }
    }

    // 初始化日志管理功能
    function initLogManagement() {
      // 生成模拟日志数据
      generateMockLogs();

      // 初始加载日志
      loadLogs();

      // 绑定事件
      bindEvents();
    }

    // 生成模拟日志数据
    function generateMockLogs() {
      const logTypes = ['operation', 'login', 'device', 'error'];
      const logLevels = ['info', 'warning', 'error', 'critical'];
      const users = ['admin', '张三', '李四', '王五', '赵六', 'system'];
      const devices = ['摄像头001', '门禁设备A1', '传感器B2', '服务器主机', '网络设备'];
      const operations = [
        '用户登录', '用户登出', '修改密码', '创建用户', '删除用户', '修改权限',
        '设备上线', '设备离线', '设备故障', '数据备份', '系统重启', '配置更新',
        '告警触发', '告警处理', '工单创建', '工单完成', '文件上传', '文件下载'
      ];
      const ips = ['*************', '*************', '*************', '*********', '***********'];

      allLogs = [];
      for (let i = 0; i < 1245; i++) {
        const logType = logTypes[Math.floor(Math.random() * logTypes.length)];
        const level = logLevels[Math.floor(Math.random() * logLevels.length)];
        const user = users[Math.floor(Math.random() * users.length)];
        const operation = operations[Math.floor(Math.random() * operations.length)];
        const ip = ips[Math.floor(Math.random() * ips.length)];

        // 生成最近30天内的随机时间
        const now = new Date();
        const randomDays = Math.floor(Math.random() * 30);
        const randomHours = Math.floor(Math.random() * 24);
        const randomMinutes = Math.floor(Math.random() * 60);
        const logTime = new Date(now.getTime() - (randomDays * 24 * 60 * 60 * 1000) - (randomHours * 60 * 60 * 1000) - (randomMinutes * 60 * 1000));

        allLogs.push({
          id: i + 1,
          time: logTime,
          type: logType,
          level: level,
          user: logType === 'device' ? devices[Math.floor(Math.random() * devices.length)] : user,
          operation: operation,
          details: generateLogDetails(logType, operation),
          ip: ip
        });
      }

      // 按时间倒序排列
      allLogs.sort((a, b) => b.time - a.time);
      filteredLogs = [...allLogs];
    }

    // 生成日志详情
    function generateLogDetails(type, operation) {
      const details = {
        'operation': `执行${operation}操作成功`,
        'login': operation.includes('登录') ? '登录成功' : '登出成功',
        'device': `设备状态变更: ${operation}`,
        'error': `系统错误: ${operation}失败`
      };
      return details[type] || '操作完成';
    }

    // 加载日志数据
    function loadLogs() {
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageData = filteredLogs.slice(startIndex, endIndex);

      const container = document.getElementById('logsContainer');
      container.innerHTML = '';

      if (pageData.length === 0) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-search" style="font-size: 48px; color: var(--gray-400); margin-bottom: 16px;"></i>
            <p style="color: var(--gray-600); font-size: 16px;">没有找到匹配的日志记录</p>
          </div>
        `;
        return;
      }

      pageData.forEach(log => {
        const logCard = createLogCard(log);
        container.appendChild(logCard);
      });

      updatePagination();
    }

    // 创建日志卡片
    function createLogCard(log) {
      const card = document.createElement('div');
      card.className = 'log-card';

      const typeClass = {
        'operation': 'primary',
        'login': 'success',
        'device': 'info',
        'error': 'danger'
      };

      const levelClass = {
        'info': 'info',
        'warning': 'warning',
        'error': 'danger',
        'critical': 'danger'
      };

      const typeText = {
        'operation': '操作日志',
        'login': '登录日志',
        'device': '设备日志',
        'error': '错误日志'
      };

      const levelText = {
        'info': '信息',
        'warning': '警告',
        'error': '错误',
        'critical': '严重'
      };

      const typeIcon = {
        'operation': 'fas fa-cog',
        'login': 'fas fa-sign-in-alt',
        'device': 'fas fa-microchip',
        'error': 'fas fa-exclamation-triangle'
      };

      card.innerHTML = `
        <div class="log-card-header">
          <div class="log-time">
            <i class="fas fa-clock"></i>
            <span>${formatDateTime(log.time)}</span>
          </div>
          <div class="log-badges">
            <span class="badge badge-${typeClass[log.type]}">
              <i class="${typeIcon[log.type]}"></i>
              ${typeText[log.type]}
            </span>
            <span class="badge badge-${levelClass[log.level]}">${levelText[log.level]}</span>
          </div>
        </div>
        <div class="log-card-body">
          <div class="log-main-info">
            <div class="log-user">
              <i class="fas fa-user"></i>
              <span>${log.user}</span>
            </div>
            <div class="log-operation">
              <strong>${log.operation}</strong>
            </div>
            <div class="log-details">
              ${log.details}
            </div>
          </div>
          <div class="log-meta">
            <div class="log-ip">
              <i class="fas fa-globe"></i>
              <span>${log.ip}</span>
            </div>
            <button class="btn btn-sm btn-outline-primary log-view-btn" onclick="viewLogDetails(${log.id})">
              <i class="fas fa-eye"></i>
              查看详情
            </button>
          </div>
        </div>
      `;

      return card;
    }

    // 格式化日期时间
    function formatDateTime(date) {
      const now = new Date();
      const diff = now - date;
      const days = Math.floor(diff / (1000 * 60 * 60 * 24));

      if (days === 0) {
        return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } else if (days === 1) {
        return '昨天 ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      } else {
        return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
      }
    }

    // 更新分页信息
    function updatePagination() {
      const totalPages = Math.ceil(filteredLogs.length / pageSize);
      const startRecord = (currentPage - 1) * pageSize + 1;
      const endRecord = Math.min(currentPage * pageSize, filteredLogs.length);

      document.getElementById('currentStart').textContent = startRecord;
      document.getElementById('currentEnd').textContent = endRecord;
      document.getElementById('totalRecords').textContent = filteredLogs.length.toLocaleString();

      // 更新分页按钮状态
      document.getElementById('prevPageBtn').disabled = currentPage === 1;
      document.getElementById('nextPageBtn').disabled = currentPage === totalPages;

      // 更新页码按钮
      updatePageNumbers(totalPages);
    }

    // 更新页码按钮
    function updatePageNumbers(totalPages) {
      const container = document.getElementById('paginationNumbers');
      container.innerHTML = '';

      const maxVisible = 5;
      let startPage = Math.max(1, currentPage - Math.floor(maxVisible / 2));
      let endPage = Math.min(totalPages, startPage + maxVisible - 1);

      if (endPage - startPage < maxVisible - 1) {
        startPage = Math.max(1, endPage - maxVisible + 1);
      }

      for (let i = startPage; i <= endPage; i++) {
        const btn = document.createElement('button');
        btn.className = `btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-secondary'}`;
        btn.textContent = i;
        btn.onclick = () => goToPage(i);
        container.appendChild(btn);
      }

      if (endPage < totalPages) {
        const dots = document.createElement('span');
        dots.textContent = '...';
        container.appendChild(dots);

        const lastBtn = document.createElement('button');
        lastBtn.className = 'btn btn-sm btn-secondary';
        lastBtn.textContent = totalPages;
        lastBtn.onclick = () => goToPage(totalPages);
        container.appendChild(lastBtn);
      }
    }

    // 跳转到指定页面
    function goToPage(page) {
      currentPage = page;
      loadLogs();
    }

    // 绑定事件
    function bindEvents() {
      // 筛选事件
      document.getElementById('logTypeFilter').addEventListener('change', applyFilters);
      document.getElementById('logLevelFilter').addEventListener('change', applyFilters);
      document.getElementById('timeRangeFilter').addEventListener('change', applyFilters);

      // 搜索事件
      document.getElementById('searchBtn').addEventListener('click', applyFilters);
      document.getElementById('searchKeyword').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          applyFilters();
        }
      });

      // 分页事件
      document.getElementById('prevPageBtn').addEventListener('click', () => {
        if (currentPage > 1) {
          goToPage(currentPage - 1);
        }
      });

      document.getElementById('nextPageBtn').addEventListener('click', () => {
        const totalPages = Math.ceil(filteredLogs.length / pageSize);
        if (currentPage < totalPages) {
          goToPage(currentPage + 1);
        }
      });

      // 刷新和导出事件
      document.getElementById('refreshLogsBtn').addEventListener('click', refreshLogs);
      document.getElementById('exportLogsBtn').addEventListener('click', exportLogs);
    }

    // 应用筛选条件
    function applyFilters() {
      const typeFilter = document.getElementById('logTypeFilter').value;
      const levelFilter = document.getElementById('logLevelFilter').value;
      const timeFilter = document.getElementById('timeRangeFilter').value;
      const keyword = document.getElementById('searchKeyword').value.toLowerCase();

      filteredLogs = allLogs.filter(log => {
        // 类型筛选
        if (typeFilter && log.type !== typeFilter) return false;

        // 级别筛选
        if (levelFilter && log.level !== levelFilter) return false;

        // 时间筛选
        if (timeFilter) {
          const now = new Date();
          const logDate = new Date(log.time);
          const diffDays = Math.floor((now - logDate) / (1000 * 60 * 60 * 24));

          switch (timeFilter) {
            case 'today':
              if (diffDays > 0) return false;
              break;
            case 'week':
              if (diffDays > 7) return false;
              break;
            case 'month':
              if (diffDays > 30) return false;
              break;
          }
        }

        // 关键词搜索
        if (keyword) {
          const searchText = `${log.user} ${log.operation} ${log.details}`.toLowerCase();
          if (!searchText.includes(keyword)) return false;
        }

        return true;
      });

      currentPage = 1;
      loadLogs();
    }

    // 刷新日志
    function refreshLogs() {
      generateMockLogs();
      applyFilters();
      showNotification('日志数据已刷新', 'success');
    }

    // 导出日志
    function exportLogs() {
      const csvContent = generateCSV(filteredLogs);
      downloadCSV(csvContent, 'logs_export.csv');
      showNotification('日志导出成功', 'success');
    }

    // 生成CSV内容
    function generateCSV(logs) {
      const headers = ['时间', '类型', '级别', '用户/设备', '操作/事件', '详情', 'IP地址'];
      const csvRows = [headers.join(',')];

      logs.forEach(log => {
        const row = [
          log.time.toLocaleString('zh-CN'),
          getLogTypeText(log.type),
          getLogLevelText(log.level),
          log.user,
          log.operation,
          log.details,
          log.ip
        ];
        csvRows.push(row.map(field => `"${field}"`).join(','));
      });

      return csvRows.join('\n');
    }

    // 下载CSV文件
    function downloadCSV(content, filename) {
      const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
      const link = document.createElement('a');
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }

    // 查看日志详情
    function viewLogDetails(logId) {
      const log = allLogs.find(l => l.id === logId);
      if (log) {
        showLogModal(log);
      }
    }

    // 显示日志详情模态框
    function showLogModal(log) {
      const modal = document.createElement('div');
      modal.className = 'modal-overlay';
      modal.innerHTML = `
        <div class="modal-content">
          <div class="modal-header">
            <h3>日志详情</h3>
            <button class="modal-close" onclick="closeModal()">&times;</button>
          </div>
          <div class="modal-body">
            <div class="log-detail-grid">
              <div class="detail-item">
                <label>时间:</label>
                <span>${log.time.toLocaleString('zh-CN')}</span>
              </div>
              <div class="detail-item">
                <label>类型:</label>
                <span class="badge badge-${getLogTypeClass(log.type)}">${getLogTypeText(log.type)}</span>
              </div>
              <div class="detail-item">
                <label>级别:</label>
                <span class="badge badge-${getLogLevelClass(log.level)}">${getLogLevelText(log.level)}</span>
              </div>
              <div class="detail-item">
                <label>用户/设备:</label>
                <span>${log.user}</span>
              </div>
              <div class="detail-item">
                <label>操作/事件:</label>
                <span>${log.operation}</span>
              </div>
              <div class="detail-item">
                <label>IP地址:</label>
                <span>${log.ip}</span>
              </div>
              <div class="detail-item full-width">
                <label>详细信息:</label>
                <div class="log-detail-content">${log.details}</div>
              </div>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(modal);
      modal.addEventListener('click', function(e) {
        if (e.target === modal) {
          closeModal();
        }
      });
    }

    // 关闭模态框
    function closeModal() {
      const modal = document.querySelector('.modal-overlay');
      if (modal) {
        document.body.removeChild(modal);
      }
    }

    // 辅助函数
    function getLogTypeText(type) {
      const texts = {
        'operation': '操作日志',
        'login': '登录日志',
        'device': '设备日志',
        'error': '错误日志'
      };
      return texts[type] || type;
    }

    function getLogLevelText(level) {
      const texts = {
        'info': '信息',
        'warning': '警告',
        'error': '错误',
        'critical': '严重'
      };
      return texts[level] || level;
    }

    function getLogTypeClass(type) {
      const classes = {
        'operation': 'primary',
        'login': 'success',
        'device': 'info',
        'error': 'danger'
      };
      return classes[type] || 'secondary';
    }

    function getLogLevelClass(level) {
      const classes = {
        'info': 'info',
        'warning': 'warning',
        'error': 'danger',
        'critical': 'danger'
      };
      return classes[level] || 'secondary';
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);

      setTimeout(() => {
        notification.classList.add('show');
      }, 100);

      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => {
          if (document.body.contains(notification)) {
            document.body.removeChild(notification);
          }
        }, 300);
      }, 3000);
    }
  </script>
</body>
</html>
