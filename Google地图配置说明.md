# Google地图配置说明

## 概述

本项目的地图监控页面已更换为Google地图API来展示广州区域的智慧楼宇监控信息。Google地图具有更好的国际化支持和稳定性。

## 申请Google地图API密钥

### 1. 创建Google Cloud项目

1. 访问Google Cloud Console：https://console.cloud.google.com/
2. 使用Google账号登录
3. 创建新项目或选择现有项目
4. 项目名称建议：`智慧楼宇监控系统`

### 2. 启用Maps JavaScript API

1. 在Google Cloud Console中，导航到"API和服务" > "库"
2. 搜索"Maps JavaScript API"
3. 点击"启用"按钮
4. 等待API启用完成

### 3. 创建API密钥

1. 导航到"API和服务" > "凭据"
2. 点击"创建凭据" > "API密钥"
3. 复制生成的API密钥
4. 建议点击"限制密钥"进行安全配置

### 4. 配置API密钥限制（推荐）

**应用程序限制：**
- 选择"HTTP引荐来源网址"
- 添加允许的网址：
  - 开发环境：`http://localhost:*/*`
  - 生产环境：`https://yourdomain.com/*`

**API限制：**
- 选择"限制密钥"
- 勾选"Maps JavaScript API"

## 配置API密钥

### 方法1：直接修改HTML文件

打开 `map-monitoring.html` 文件，找到以下行：

```html
<script async defer src="https://maps.googleapis.com/maps/api/js?key=YOUR_GOOGLE_MAPS_API_KEY&callback=initGoogleMap"></script>
```

将 `YOUR_GOOGLE_MAPS_API_KEY` 替换为您申请到的实际API密钥：

```html
<script async defer src="https://maps.googleapis.com/maps/api/js?key=您的实际API密钥&callback=initGoogleMap"></script>
```

### 方法2：使用环境变量（推荐）

创建配置文件来管理API密钥：

```javascript
// config.js
const CONFIG = {
  GOOGLE_MAPS_API_KEY: 'YOUR_ACTUAL_API_KEY'
};
```

然后动态加载Google地图API：

```javascript
function loadGoogleMapsAPI() {
  const script = document.createElement('script');
  script.src = `https://maps.googleapis.com/maps/api/js?key=${CONFIG.GOOGLE_MAPS_API_KEY}&callback=initGoogleMap`;
  script.async = true;
  script.defer = true;
  document.head.appendChild(script);
}
```

## 功能特性

### 地图显示
- **地图中心**：广州市天河CBD（纬度：23.150533，经度：113.324520）
- **默认缩放级别**：13级
- **地图类型**：道路地图（ROADMAP）
- **地图样式**：隐藏POI和交通标签，突出监控信息

### 监控点类型
1. **视频监控**（📹）：绿色(在线)/橙色(告警)/红色(离线)
2. **门禁设备**（🚪）：蓝色(在线)/红色(离线)
3. **告警事件**（⚠️）：红色标识，支持高中低级别
4. **人员位置**（👤）：紫色标识
5. **车辆轨迹**（🚗）：绿色标识

### 地图控件
- **缩放控件**：启用
- **地图类型控件**：禁用
- **比例尺控件**：启用
- **街景控件**：禁用
- **旋转控件**：禁用
- **全屏控件**：禁用

### 交互功能
- **点击标记**：显示设备详细信息窗口
- **图层控制**：可开启/关闭不同类型监控点
- **地图控制**：放大、缩小、定位功能
- **数据刷新**：实时刷新监控数据
- **信息窗口**：自动关闭之前的窗口，避免重叠

## 示例数据

系统预置了广州地区的示例监控数据：

### 视频监控点（6个）
```javascript
{ id: 'v1', name: '天河CBD监控点1', lng: 113.324520, lat: 23.150533, status: 'online' }
{ id: 'v2', name: '珠江新城监控点2', lng: 113.334520, lat: 23.120533, status: 'online' }
{ id: 'v3', name: '体育西路监控点3', lng: 113.314520, lat: 23.140533, status: 'warning' }
{ id: 'v4', name: '天河北路监控点4', lng: 113.344520, lat: 23.160533, status: 'online' }
{ id: 'v5', name: '林和西路监控点5', lng: 113.354520, lat: 23.135533, status: 'offline' }
{ id: 'v6', name: '花城大道监控点6', lng: 113.364520, lat: 23.125533, status: 'online' }
```

### 门禁设备（4个）
```javascript
{ id: 'a1', name: '主楼入口门禁', lng: 113.314520, lat: 23.130533, status: 'online' }
{ id: 'a2', name: '停车场门禁', lng: 113.304520, lat: 23.125533, status: 'online' }
{ id: 'a3', name: '后门门禁', lng: 113.324520, lat: 23.135533, status: 'warning' }
{ id: 'a4', name: '地下室门禁', lng: 113.334520, lat: 23.145533, status: 'online' }
```

### 告警事件（3个）
```javascript
{ id: 'al1', name: '火灾报警', lng: 113.344520, lat: 23.135533, status: 'alarm', level: 'high' }
{ id: 'al2', name: '入侵检测', lng: 113.354520, lat: 23.145533, status: 'alarm', level: 'medium' }
{ id: 'al3', name: '设备故障', lng: 113.364520, lat: 23.155533, status: 'alarm', level: 'low' }
```

## 自定义配置

### 修改地图中心点

```javascript
// 在initGoogleMap函数中修改
const centerPoint = { lat: 纬度, lng: 经度 };
googleMap = new google.maps.Map(document.getElementById("googleMap"), {
  zoom: 缩放级别,
  center: centerPoint,
  // 其他配置...
});
```

### 自定义地图样式

```javascript
// 在地图初始化时添加样式
const mapStyles = [
  {
    "featureType": "poi",
    "elementType": "labels",
    "stylers": [{ "visibility": "off" }]
  },
  {
    "featureType": "transit",
    "elementType": "labels", 
    "stylers": [{ "visibility": "off" }]
  }
  // 更多样式配置...
];

googleMap = new google.maps.Map(document.getElementById("googleMap"), {
  // 其他配置...
  styles: mapStyles
});
```

### 添加新的监控点

```javascript
// 在loadMapData函数中添加
const newDevice = {
  id: '设备ID',
  name: '设备名称',
  lng: 经度,
  lat: 纬度,
  status: '状态', // online, offline, warning, alarm
  type: '类型'    // video, access, alarm, personnel, vehicle
};
```

## 与百度地图的主要区别

### 坐标系统
- **Google地图**：使用WGS84坐标系（国际标准）
- **百度地图**：使用BD09坐标系（百度专用）

### API调用方式
- **Google地图**：通过callback函数初始化
- **百度地图**：直接实例化对象

### 标记点创建
- **Google地图**：使用`google.maps.Marker`
- **百度地图**：使用`BMap.Marker`

### 信息窗口
- **Google地图**：使用`google.maps.InfoWindow`
- **百度地图**：使用`BMap.InfoWindow`

## 注意事项

### 1. API配额和计费
- Google地图API有免费配额（每月$200信用额度）
- 超出免费配额后按使用量计费
- 建议设置预算提醒

### 2. 网络访问
- 在中国大陆地区，Google服务可能需要特殊网络环境
- 建议在海外服务器部署或使用CDN加速

### 3. 性能优化
- 大量标记点时建议使用MarkerClusterer进行聚合
- 合理设置地图缩放级别范围
- 按需加载标记点数据

### 4. 安全配置
- 务必配置API密钥的HTTP引荐来源网址限制
- 不要在客户端代码中暴露敏感信息
- 定期轮换API密钥

## 故障排除

### 地图无法加载
1. 检查API密钥是否正确
2. 确认API已启用
3. 检查网络连接
4. 查看浏览器控制台错误信息

### 标记点不显示
1. 检查坐标格式（lat, lng）
2. 确认图层开关状态
3. 检查数据格式是否正确

### 信息窗口异常
1. 检查HTML内容格式
2. 确认CSS样式无冲突
3. 检查JavaScript语法错误

## 技术支持

- Google地图API文档：https://developers.google.com/maps/documentation/javascript
- Google Cloud支持：https://cloud.google.com/support
- 开发者社区：https://stackoverflow.com/questions/tagged/google-maps

## 更新日志

- **v2.0.0**：从百度地图迁移到Google地图
- **v2.1.0**：优化信息窗口显示和交互体验
- **v2.2.0**：添加图层控制和数据刷新功能
