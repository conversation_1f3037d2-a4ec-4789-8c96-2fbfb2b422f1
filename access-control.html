<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>门禁与通行控制 - 广州农行智慧楼宇</title>
  
  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- 引入主样式 -->
  <link rel="stylesheet" href="assets/css/main.css">
  
  <style>
    .access-control-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: var(--spacing-lg);
    }
    
    .door-status-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      gap: var(--spacing-md);
    }
    
    .door-card {
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      transition: all 0.3s ease;
    }
    
    .door-card:hover {
      box-shadow: var(--shadow-md);
    }
    
    .door-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-md);
    }
    
    .door-info {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }
    
    .door-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: white;
    }
    
    .door-icon.open { background: var(--success-color); }
    .door-icon.closed { background: var(--gray-500); }
    .door-icon.error { background: var(--danger-color); }
    
    .door-details h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
    }
    
    .door-location {
      font-size: 12px;
      color: var(--gray-500);
      margin-top: 2px;
    }
    
    .door-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .door-status.open {
      background: rgba(16, 185, 129, 0.1);
      color: var(--success-color);
    }
    
    .door-status.closed {
      background: rgba(107, 114, 128, 0.1);
      color: var(--gray-500);
    }
    
    .door-status.error {
      background: rgba(239, 68, 68, 0.1);
      color: var(--danger-color);
    }
    
    .door-controls {
      display: flex;
      gap: var(--spacing-sm);
      margin-top: var(--spacing-md);
    }
    
    .door-control-btn {
      flex: 1;
      padding: var(--spacing-sm);
      border: 1px solid var(--gray-300);
      background: white;
      border-radius: var(--radius-md);
      cursor: pointer;
      transition: all 0.3s ease;
      font-size: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-xs);
    }
    
    .door-control-btn:hover {
      background: var(--gray-50);
    }
    
    .door-control-btn.primary {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }
    
    .door-control-btn.primary:hover {
      background: var(--primary-dark);
    }
    
    .access-records {
      max-height: 400px;
      overflow-y: auto;
    }
    
    .record-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      border-bottom: 1px solid var(--gray-100);
    }
    
    .record-item:last-child {
      border-bottom: none;
    }
    
    .record-avatar {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      background: var(--primary-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 14px;
    }
    
    .record-content {
      flex: 1;
    }
    
    .record-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--gray-900);
      margin-bottom: 2px;
    }
    
    .record-action {
      font-size: 12px;
      color: var(--gray-600);
    }
    
    .record-time {
      font-size: 11px;
      color: var(--gray-400);
      text-align: right;
    }
    
    .record-status {
      padding: 2px 6px;
      border-radius: 8px;
      font-size: 10px;
      font-weight: 500;
      margin-top: 2px;
    }
    
    .record-status.success {
      background: rgba(16, 185, 129, 0.1);
      color: var(--success-color);
    }
    
    .record-status.failed {
      background: rgba(239, 68, 68, 0.1);
      color: var(--danger-color);
    }
    
    .permission-tree {
      max-height: 500px;
      overflow-y: auto;
    }
    
    .permission-group {
      margin-bottom: var(--spacing-md);
    }
    
    .permission-group-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--gray-900);
      margin-bottom: var(--spacing-sm);
      padding: var(--spacing-sm);
      background: var(--gray-50);
      border-radius: var(--radius-md);
    }
    
    .permission-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-sm);
      margin-left: var(--spacing-md);
    }
    
    .permission-checkbox {
      width: 16px;
      height: 16px;
    }
    
    .permission-label {
      font-size: 13px;
      color: var(--gray-700);
      flex: 1;
    }
    
    .permission-users {
      font-size: 11px;
      color: var(--gray-500);
      background: var(--gray-100);
      padding: 2px 6px;
      border-radius: 8px;
    }
    
    @media (max-width: 1024px) {
      .access-control-grid {
        grid-template-columns: 1fr;
      }
      
      .door-status-grid {
        grid-template-columns: 1fr;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div id="sidebarContainer"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部导航 -->
      <div id="headerContainer"></div>

      <!-- 页面内容 -->
      <div class="page-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">门禁与通行控制</h1>
          <p class="page-description">门禁设备配置与实时控制、通行权限设置、开门记录与门状态查询</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid mb-lg">
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">18</div>
                <div class="stat-label">在线门禁</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-door-open"></i>
              </div>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">2</div>
                <div class="stat-label">异常设备</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">今日通行</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-users"></i>
              </div>
            </div>
          </div>

          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">12</div>
                <div class="stat-label">权限组</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-key"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 门禁状态监控 -->
        <div class="card mb-lg">
          <div class="card-header">
            <h3 class="card-title">门禁状态监控</h3>
            <div class="d-flex gap-sm">
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i>
                添加设备
              </button>
            </div>
          </div>
          <div class="card-body">
            <div class="door-status-grid">
              <div class="door-card">
                <div class="door-header">
                  <div class="door-info">
                    <div class="door-icon open">
                      <i class="fas fa-door-open"></i>
                    </div>
                    <div class="door-details">
                      <h4>1号楼大厅门禁</h4>
                      <div class="door-location">1号楼 - 主入口</div>
                    </div>
                  </div>
                  <div class="door-status open">开启</div>
                </div>
                <div class="door-controls">
                  <button class="door-control-btn">
                    <i class="fas fa-lock"></i>
                    关闭
                  </button>
                  <button class="door-control-btn primary">
                    <i class="fas fa-unlock"></i>
                    开启
                  </button>
                  <button class="door-control-btn">
                    <i class="fas fa-cog"></i>
                    设置
                  </button>
                </div>
              </div>

              <div class="door-card">
                <div class="door-header">
                  <div class="door-info">
                    <div class="door-icon closed">
                      <i class="fas fa-door-closed"></i>
                    </div>
                    <div class="door-details">
                      <h4>2号楼侧门</h4>
                      <div class="door-location">2号楼 - 侧入口</div>
                    </div>
                  </div>
                  <div class="door-status closed">关闭</div>
                </div>
                <div class="door-controls">
                  <button class="door-control-btn primary">
                    <i class="fas fa-lock"></i>
                    关闭
                  </button>
                  <button class="door-control-btn">
                    <i class="fas fa-unlock"></i>
                    开启
                  </button>
                  <button class="door-control-btn">
                    <i class="fas fa-cog"></i>
                    设置
                  </button>
                </div>
              </div>

              <div class="door-card">
                <div class="door-header">
                  <div class="door-info">
                    <div class="door-icon error">
                      <i class="fas fa-exclamation-triangle"></i>
                    </div>
                    <div class="door-details">
                      <h4>地下车库门禁</h4>
                      <div class="door-location">地下1层 - 车库入口</div>
                    </div>
                  </div>
                  <div class="door-status error">故障</div>
                </div>
                <div class="door-controls">
                  <button class="door-control-btn">
                    <i class="fas fa-tools"></i>
                    维修
                  </button>
                  <button class="door-control-btn">
                    <i class="fas fa-sync-alt"></i>
                    重启
                  </button>
                  <button class="door-control-btn">
                    <i class="fas fa-cog"></i>
                    设置
                  </button>
                </div>
              </div>

              <div class="door-card">
                <div class="door-header">
                  <div class="door-info">
                    <div class="door-icon closed">
                      <i class="fas fa-door-closed"></i>
                    </div>
                    <div class="door-details">
                      <h4>会议室门禁</h4>
                      <div class="door-location">3号楼 - 会议室A</div>
                    </div>
                  </div>
                  <div class="door-status closed">关闭</div>
                </div>
                <div class="door-controls">
                  <button class="door-control-btn primary">
                    <i class="fas fa-lock"></i>
                    关闭
                  </button>
                  <button class="door-control-btn">
                    <i class="fas fa-unlock"></i>
                    开启
                  </button>
                  <button class="door-control-btn">
                    <i class="fas fa-cog"></i>
                    设置
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 通行记录和权限管理 -->
        <div class="access-control-grid">
          <!-- 最新通行记录 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">最新通行记录</h3>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-history"></i>
                查看全部
              </button>
            </div>
            <div class="card-body p-0">
              <div class="access-records">
                <div class="record-item">
                  <div class="record-avatar">张</div>
                  <div class="record-content">
                    <div class="record-name">张三</div>
                    <div class="record-action">刷卡进入 - 1号楼大厅</div>
                    <div class="record-status success">成功</div>
                  </div>
                  <div class="record-time">
                    <div>14:30:25</div>
                    <div>刚刚</div>
                  </div>
                </div>

                <div class="record-item">
                  <div class="record-avatar">李</div>
                  <div class="record-content">
                    <div class="record-name">李四</div>
                    <div class="record-action">人脸识别 - 2号楼侧门</div>
                    <div class="record-status success">成功</div>
                  </div>
                  <div class="record-time">
                    <div>14:28:12</div>
                    <div>2分钟前</div>
                  </div>
                </div>

                <div class="record-item">
                  <div class="record-avatar">王</div>
                  <div class="record-content">
                    <div class="record-name">王五</div>
                    <div class="record-action">刷卡失败 - 会议室A</div>
                    <div class="record-status failed">失败</div>
                  </div>
                  <div class="record-time">
                    <div>14:25:45</div>
                    <div>5分钟前</div>
                  </div>
                </div>

                <div class="record-item">
                  <div class="record-avatar">赵</div>
                  <div class="record-content">
                    <div class="record-name">赵六</div>
                    <div class="record-action">密码开门 - 地下车库</div>
                    <div class="record-status success">成功</div>
                  </div>
                  <div class="record-time">
                    <div>14:20:30</div>
                    <div>10分钟前</div>
                  </div>
                </div>

                <div class="record-item">
                  <div class="record-avatar">陈</div>
                  <div class="record-content">
                    <div class="record-name">陈七</div>
                    <div class="record-action">访客登记 - 1号楼大厅</div>
                    <div class="record-status success">成功</div>
                  </div>
                  <div class="record-time">
                    <div>14:15:18</div>
                    <div>15分钟前</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 权限管理 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">权限管理</h3>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i>
                新增权限组
              </button>
            </div>
            <div class="card-body">
              <div class="permission-tree">
                <div class="permission-group">
                  <div class="permission-group-title">
                    <i class="fas fa-users"></i>
                    管理员权限组
                  </div>
                  <div class="permission-item">
                    <input type="checkbox" class="permission-checkbox" checked>
                    <label class="permission-label">1号楼大厅门禁</label>
                    <span class="permission-users">5人</span>
                  </div>
                  <div class="permission-item">
                    <input type="checkbox" class="permission-checkbox" checked>
                    <label class="permission-label">2号楼侧门</label>
                    <span class="permission-users">5人</span>
                  </div>
                  <div class="permission-item">
                    <input type="checkbox" class="permission-checkbox" checked>
                    <label class="permission-label">会议室A</label>
                    <span class="permission-users">5人</span>
                  </div>
                </div>

                <div class="permission-group">
                  <div class="permission-group-title">
                    <i class="fas fa-user-tie"></i>
                    员工权限组
                  </div>
                  <div class="permission-item">
                    <input type="checkbox" class="permission-checkbox" checked>
                    <label class="permission-label">1号楼大厅门禁</label>
                    <span class="permission-users">25人</span>
                  </div>
                  <div class="permission-item">
                    <input type="checkbox" class="permission-checkbox" checked>
                    <label class="permission-label">2号楼侧门</label>
                    <span class="permission-users">25人</span>
                  </div>
                  <div class="permission-item">
                    <input type="checkbox" class="permission-checkbox">
                    <label class="permission-label">会议室A</label>
                    <span class="permission-users">0人</span>
                  </div>
                </div>

                <div class="permission-group">
                  <div class="permission-group-title">
                    <i class="fas fa-user-friends"></i>
                    访客权限组
                  </div>
                  <div class="permission-item">
                    <input type="checkbox" class="permission-checkbox" checked>
                    <label class="permission-label">1号楼大厅门禁</label>
                    <span class="permission-users">8人</span>
                  </div>
                  <div class="permission-item">
                    <input type="checkbox" class="permission-checkbox">
                    <label class="permission-label">2号楼侧门</label>
                    <span class="permission-users">0人</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入主JavaScript -->
  <script src="assets/js/main.js"></script>
  
  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载组件
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      
      // 初始化门禁控制功能
      initAccessControls();
      
      // 更新面包屑
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('门禁与通行控制');
        }
      }, 100);
    });

    // 加载组件
    function loadComponent(containerId, componentPath) {
      fetch(componentPath)
        .then(response => response.text())
        .then(html => {
          document.getElementById(containerId).innerHTML = html;
        })
        .catch(error => {
          console.error('Error loading component:', error);
        });
    }

    // 初始化门禁控制
    function initAccessControls() {
      // 门禁控制按钮事件
      document.querySelectorAll('.door-control-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          const action = this.textContent.trim();
          const doorCard = this.closest('.door-card');
          const doorName = doorCard.querySelector('h4').textContent;
          
          if (window.app) {
            window.app.showNotification(`${doorName} - ${action}操作已执行`, 'success');
          }
          
          // 模拟状态更新
          if (action === '开启') {
            updateDoorStatus(doorCard, 'open');
          } else if (action === '关闭') {
            updateDoorStatus(doorCard, 'closed');
          }
        });
      });

      // 权限复选框事件
      document.querySelectorAll('.permission-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          const label = this.nextElementSibling.textContent;
          const isChecked = this.checked;
          
          if (window.app) {
            window.app.showNotification(
              `${label} 权限已${isChecked ? '启用' : '禁用'}`,
              'info'
            );
          }
        });
      });
    }

    // 更新门禁状态
    function updateDoorStatus(doorCard, status) {
      const doorIcon = doorCard.querySelector('.door-icon');
      const doorStatus = doorCard.querySelector('.door-status');
      const controls = doorCard.querySelectorAll('.door-control-btn');
      
      // 更新图标和状态
      doorIcon.className = `door-icon ${status}`;
      doorStatus.className = `door-status ${status}`;
      
      if (status === 'open') {
        doorIcon.innerHTML = '<i class="fas fa-door-open"></i>';
        doorStatus.textContent = '开启';
        // 更新按钮状态
        controls[0].classList.remove('primary');
        controls[1].classList.add('primary');
      } else if (status === 'closed') {
        doorIcon.innerHTML = '<i class="fas fa-door-closed"></i>';
        doorStatus.textContent = '关闭';
        // 更新按钮状态
        controls[0].classList.add('primary');
        controls[1].classList.remove('primary');
      }
    }
  </script>
</body>
</html>
