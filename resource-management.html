<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>资源接入与管理 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">资源接入与管理</h1>
          <p class="page-description">视频、门禁等资源注册、移动端权限分配与入口配置</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">24</div>
                <div class="stat-label">视频资源</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-video"></i>
              </div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">18</div>
                <div class="stat-label">门禁资源</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-door-open"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">36</div>
                <div class="stat-label">传感器</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-microchip"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">5</div>
                <div class="stat-label">离线设备</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>
        </div>
        <div class="card">
          <div class="card-header">
            <h3 class="card-title">资源管理</h3>
            <div class="card-actions">
              <button class="btn btn-secondary" id="refreshBtn">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button class="btn btn-primary" id="addResourceBtn">
                <i class="fas fa-plus"></i>
                接入资源
              </button>
            </div>
          </div>

          <!-- 筛选和搜索区域 -->
          <div class="card-filters">
            <div class="filter-row">
              <div class="filter-group">
                <label>资源类型</label>
                <select id="typeFilter" class="form-select">
                  <option value="">全部类型</option>
                  <option value="video">视频监控</option>
                  <option value="access">门禁系统</option>
                  <option value="sensor">传感器</option>
                  <option value="alarm">报警设备</option>
                </select>
              </div>
              <div class="filter-group">
                <label>状态</label>
                <select id="statusFilter" class="form-select">
                  <option value="">全部状态</option>
                  <option value="online">在线</option>
                  <option value="offline">离线</option>
                  <option value="maintenance">维护中</option>
                </select>
              </div>
              <div class="filter-group">
                <label>楼层</label>
                <select id="floorFilter" class="form-select">
                  <option value="">全部楼层</option>
                  <option value="B1">地下一层</option>
                  <option value="1F">一楼</option>
                  <option value="2F">二楼</option>
                  <option value="3F">三楼</option>
                  <option value="4F">四楼</option>
                  <option value="5F">五楼</option>
                </select>
              </div>
              <div class="filter-group search-group">
                <label>搜索</label>
                <div class="search-input-wrapper">
                  <input type="text" id="searchInput" class="form-input" placeholder="搜索资源名称、位置或IP地址...">
                  <i class="fas fa-search search-icon"></i>
                </div>
              </div>
            </div>
          </div>

          <div class="card-body p-0">
            <!-- 资源列表容器 -->
            <div class="resources-container" id="resourcesContainer">
              <!-- 资源数据将通过JavaScript动态加载 -->
            </div>

            <!-- 分页控件 -->
            <div class="resources-pagination">
              <div class="pagination-info">
                显示第 <span id="currentStart">1</span> - <span id="currentEnd">10</span> 条，共 <span id="totalRecords">78</span> 条记录
              </div>
              <div class="pagination-controls">
                <button class="btn btn-sm btn-secondary" id="prevPageBtn" disabled>
                  <i class="fas fa-chevron-left"></i>
                  上一页
                </button>
                <div class="pagination-numbers" id="paginationNumbers">
                  <button class="btn btn-sm btn-primary">1</button>
                  <button class="btn btn-sm btn-secondary">2</button>
                  <button class="btn btn-sm btn-secondary">3</button>
                  <span>...</span>
                  <button class="btn btn-sm btn-secondary">8</button>
                </div>
                <button class="btn btn-sm btn-secondary" id="nextPageBtn">
                  下一页
                  <i class="fas fa-chevron-right"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('资源接入与管理');
        }
        initResourceManagement();
      }, 100);
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 资源管理初始化
    let allResources = [];
    let filteredResources = [];
    let currentPage = 1;
    const pageSize = 10;

    function initResourceManagement() {
      generateMockData();
      setupEventListeners();
      loadResources();
    }

    // 生成模拟数据
    function generateMockData() {
      const resourceTypes = [
        { type: 'video', name: '视频监控', icon: 'fas fa-video' },
        { type: 'access', name: '门禁系统', icon: 'fas fa-door-open' },
        { type: 'sensor', name: '传感器', icon: 'fas fa-microchip' },
        { type: 'alarm', name: '报警设备', icon: 'fas fa-bell' }
      ];

      const floors = ['B1', '1F', '2F', '3F', '4F', '5F'];
      const statuses = ['online', 'offline', 'maintenance'];
      const locations = [
        '大厅', '会议室A', '会议室B', '办公区', '走廊', '电梯间',
        '楼梯间', '停车场', '机房', '休息区', '接待室', '储物间'
      ];

      allResources = [];
      for (let i = 1; i <= 78; i++) {
        const resourceType = resourceTypes[Math.floor(Math.random() * resourceTypes.length)];
        const floor = floors[Math.floor(Math.random() * floors.length)];
        const location = locations[Math.floor(Math.random() * locations.length)];
        const status = statuses[Math.floor(Math.random() * statuses.length)];

        // 确保离线设备数量符合统计卡片显示的5个
        const finalStatus = i <= 5 ? 'offline' : (Math.random() > 0.1 ? 'online' : status);

        allResources.push({
          id: i,
          name: `${resourceType.name}${String(i).padStart(3, '0')}`,
          type: resourceType.type,
          typeName: resourceType.name,
          icon: resourceType.icon,
          status: finalStatus,
          floor: floor,
          location: `${floor}-${location}`,
          ip: `192.168.1.${100 + i}`,
          port: 8000 + Math.floor(Math.random() * 1000),
          manufacturer: ['海康威视', '大华', '宇视', '华为', '中兴'][Math.floor(Math.random() * 5)],
          model: `Model-${Math.random().toString(36).substr(2, 6).toUpperCase()}`,
          installDate: new Date(2023, Math.floor(Math.random() * 12), Math.floor(Math.random() * 28) + 1),
          lastOnline: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
          description: `${floor}${location}的${resourceType.name}设备，用于${resourceType.type === 'video' ? '视频监控' : resourceType.type === 'access' ? '门禁控制' : resourceType.type === 'sensor' ? '环境监测' : '安全报警'}`
        });
      }

      filteredResources = [...allResources];
    }

    // 设置事件监听器
    function setupEventListeners() {
      // 筛选器事件
      document.getElementById('typeFilter').addEventListener('change', applyFilters);
      document.getElementById('statusFilter').addEventListener('change', applyFilters);
      document.getElementById('floorFilter').addEventListener('change', applyFilters);
      document.getElementById('searchInput').addEventListener('input', applyFilters);

      // 按钮事件
      document.getElementById('refreshBtn').addEventListener('click', refreshResources);
      document.getElementById('addResourceBtn').addEventListener('click', showAddResourceModal);

      // 分页事件
      document.getElementById('prevPageBtn').addEventListener('click', () => changePage(currentPage - 1));
      document.getElementById('nextPageBtn').addEventListener('click', () => changePage(currentPage + 1));
    }

    // 应用筛选器
    function applyFilters() {
      const typeFilter = document.getElementById('typeFilter').value;
      const statusFilter = document.getElementById('statusFilter').value;
      const floorFilter = document.getElementById('floorFilter').value;
      const searchQuery = document.getElementById('searchInput').value.toLowerCase();

      filteredResources = allResources.filter(resource => {
        const matchType = !typeFilter || resource.type === typeFilter;
        const matchStatus = !statusFilter || resource.status === statusFilter;
        const matchFloor = !floorFilter || resource.floor === floorFilter;
        const matchSearch = !searchQuery ||
          resource.name.toLowerCase().includes(searchQuery) ||
          resource.location.toLowerCase().includes(searchQuery) ||
          resource.ip.includes(searchQuery) ||
          resource.manufacturer.toLowerCase().includes(searchQuery);

        return matchType && matchStatus && matchFloor && matchSearch;
      });

      currentPage = 1;
      loadResources();
    }

    // 加载资源数据
    function loadResources() {
      const startIndex = (currentPage - 1) * pageSize;
      const endIndex = startIndex + pageSize;
      const pageData = filteredResources.slice(startIndex, endIndex);

      const container = document.getElementById('resourcesContainer');
      container.innerHTML = '';

      if (pageData.length === 0) {
        container.innerHTML = `
          <div class="empty-state">
            <i class="fas fa-search" style="font-size: 48px; color: var(--gray-400); margin-bottom: 16px;"></i>
            <p style="color: var(--gray-600); font-size: 16px;">没有找到匹配的资源</p>
          </div>
        `;
        return;
      }

      pageData.forEach(resource => {
        const resourceCard = createResourceCard(resource);
        container.appendChild(resourceCard);
      });

      updatePagination();
    }

    // 创建资源卡片
    function createResourceCard(resource) {
      const card = document.createElement('div');
      card.className = 'resource-card';

      const statusClass = {
        'online': 'success',
        'offline': 'danger',
        'maintenance': 'warning'
      };

      const statusText = {
        'online': '在线',
        'offline': '离线',
        'maintenance': '维护中'
      };

      const statusIcon = {
        'online': 'fas fa-circle',
        'offline': 'fas fa-circle',
        'maintenance': 'fas fa-tools'
      };

      card.innerHTML = `
        <div class="resource-card-header">
          <div class="resource-info">
            <div class="resource-icon ${resource.type}">
              <i class="${resource.icon}"></i>
            </div>
            <div class="resource-details">
              <h4 class="resource-name">${resource.name}</h4>
              <p class="resource-type">${resource.typeName}</p>
            </div>
          </div>
          <div class="resource-status">
            <span class="status-badge ${statusClass[resource.status]}">
              <i class="${statusIcon[resource.status]}"></i>
              ${statusText[resource.status]}
            </span>
          </div>
        </div>

        <div class="resource-card-body">
          <div class="resource-specs">
            <div class="spec-item">
              <i class="fas fa-map-marker-alt"></i>
              <span class="spec-label">位置:</span>
              <span class="spec-value">${resource.location}</span>
            </div>
            <div class="spec-item">
              <i class="fas fa-network-wired"></i>
              <span class="spec-label">IP地址:</span>
              <span class="spec-value">${resource.ip}:${resource.port}</span>
            </div>
            <div class="spec-item">
              <i class="fas fa-industry"></i>
              <span class="spec-label">厂商:</span>
              <span class="spec-value">${resource.manufacturer}</span>
            </div>
            <div class="spec-item">
              <i class="fas fa-tag"></i>
              <span class="spec-label">型号:</span>
              <span class="spec-value">${resource.model}</span>
            </div>
            <div class="spec-item">
              <i class="fas fa-calendar"></i>
              <span class="spec-label">安装日期:</span>
              <span class="spec-value">${formatDate(resource.installDate)}</span>
            </div>
            <div class="spec-item">
              <i class="fas fa-clock"></i>
              <span class="spec-label">最后在线:</span>
              <span class="spec-value">${formatDateTime(resource.lastOnline)}</span>
            </div>
          </div>

          <div class="resource-description">
            <p>${resource.description}</p>
          </div>
        </div>

        <div class="resource-card-footer">
          <div class="resource-actions">
            <button class="btn btn-sm btn-outline-primary" onclick="viewResourceDetails(${resource.id})">
              <i class="fas fa-eye"></i>
              查看详情
            </button>
            <button class="btn btn-sm btn-outline-secondary" onclick="editResource(${resource.id})">
              <i class="fas fa-edit"></i>
              编辑
            </button>
            <button class="btn btn-sm btn-outline-info" onclick="testConnection(${resource.id})">
              <i class="fas fa-plug"></i>
              测试连接
            </button>
            <button class="btn btn-sm btn-outline-danger" onclick="deleteResource(${resource.id})">
              <i class="fas fa-trash"></i>
              删除
            </button>
          </div>
        </div>
      `;

      return card;
    }

    // 更新分页
    function updatePagination() {
      const totalPages = Math.ceil(filteredResources.length / pageSize);
      const startIndex = (currentPage - 1) * pageSize + 1;
      const endIndex = Math.min(currentPage * pageSize, filteredResources.length);

      document.getElementById('currentStart').textContent = filteredResources.length > 0 ? startIndex : 0;
      document.getElementById('currentEnd').textContent = endIndex;
      document.getElementById('totalRecords').textContent = filteredResources.length;

      document.getElementById('prevPageBtn').disabled = currentPage <= 1;
      document.getElementById('nextPageBtn').disabled = currentPage >= totalPages;

      // 更新页码按钮
      const paginationNumbers = document.getElementById('paginationNumbers');
      paginationNumbers.innerHTML = '';

      const maxVisiblePages = 5;
      let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
      let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

      if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }

      if (startPage > 1) {
        const btn = document.createElement('button');
        btn.className = 'btn btn-sm btn-secondary';
        btn.textContent = '1';
        btn.onclick = () => changePage(1);
        paginationNumbers.appendChild(btn);

        if (startPage > 2) {
          const ellipsis = document.createElement('span');
          ellipsis.textContent = '...';
          paginationNumbers.appendChild(ellipsis);
        }
      }

      for (let i = startPage; i <= endPage; i++) {
        const btn = document.createElement('button');
        btn.className = `btn btn-sm ${i === currentPage ? 'btn-primary' : 'btn-secondary'}`;
        btn.textContent = i;
        btn.onclick = () => changePage(i);
        paginationNumbers.appendChild(btn);
      }

      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          const ellipsis = document.createElement('span');
          ellipsis.textContent = '...';
          paginationNumbers.appendChild(ellipsis);
        }

        const btn = document.createElement('button');
        btn.className = 'btn btn-sm btn-secondary';
        btn.textContent = totalPages;
        btn.onclick = () => changePage(totalPages);
        paginationNumbers.appendChild(btn);
      }
    }

    // 切换页面
    function changePage(page) {
      const totalPages = Math.ceil(filteredResources.length / pageSize);
      if (page >= 1 && page <= totalPages) {
        currentPage = page;
        loadResources();
      }
    }

    // 刷新资源
    function refreshResources() {
      const refreshBtn = document.getElementById('refreshBtn');
      const icon = refreshBtn.querySelector('i');

      icon.classList.add('fa-spin');
      refreshBtn.disabled = true;

      setTimeout(() => {
        generateMockData();
        applyFilters();
        icon.classList.remove('fa-spin');
        refreshBtn.disabled = false;
        showNotification('资源列表已刷新', 'success');
      }, 1000);
    }

    // 显示添加资源模态框
    function showAddResourceModal() {
      showNotification('添加资源功能开发中...', 'info');
    }

    // 查看资源详情
    function viewResourceDetails(resourceId) {
      const resource = allResources.find(r => r.id === resourceId);
      if (resource) {
        showNotification(`查看资源详情: ${resource.name}`, 'info');
      }
    }

    // 编辑资源
    function editResource(resourceId) {
      const resource = allResources.find(r => r.id === resourceId);
      if (resource) {
        showNotification(`编辑资源: ${resource.name}`, 'info');
      }
    }

    // 测试连接
    function testConnection(resourceId) {
      const resource = allResources.find(r => r.id === resourceId);
      if (resource) {
        showNotification('正在测试连接...', 'info');

        setTimeout(() => {
          const success = Math.random() > 0.3; // 70% 成功率
          if (success) {
            showNotification(`${resource.name} 连接测试成功`, 'success');
          } else {
            showNotification(`${resource.name} 连接测试失败`, 'error');
          }
        }, 2000);
      }
    }

    // 删除资源
    function deleteResource(resourceId) {
      const resource = allResources.find(r => r.id === resourceId);
      if (resource && confirm(`确定要删除资源 "${resource.name}" 吗？`)) {
        allResources = allResources.filter(r => r.id !== resourceId);
        applyFilters();
        showNotification(`资源 "${resource.name}" 已删除`, 'success');
      }
    }

    // 格式化日期
    function formatDate(date) {
      return date.toLocaleDateString('zh-CN');
    }

    // 格式化日期时间
    function formatDateTime(date) {
      return date.toLocaleString('zh-CN');
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      // 创建通知元素
      const notification = document.createElement('div');
      notification.className = `notification ${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      // 添加到页面
      document.body.appendChild(notification);

      // 显示动画
      setTimeout(() => notification.classList.add('show'), 100);

      // 自动隐藏
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
