<!-- 头部导航组件 -->
<header class="header">
  <div class="header-left">
    <button class="sidebar-toggle" id="sidebarToggle">
      <i class="fas fa-bars"></i>
    </button>
    
    <nav class="breadcrumb">
      <div class="breadcrumb-item">
        <i class="fas fa-home"></i>
        <span id="breadcrumbHome">首页</span>
      </div>
      <div class="breadcrumb-separator" id="breadcrumbSeparator" style="display: none;">
        <i class="fas fa-chevron-right"></i>
      </div>
      <div class="breadcrumb-item" id="breadcrumbCurrent" style="display: none;">
        <span id="breadcrumbCurrentText"></span>
      </div>
    </nav>
  </div>

  <div class="header-right">
    <!-- 搜索 -->
    <button class="header-action" title="搜索">
      <i class="fas fa-search"></i>
    </button>

    <!-- 通知 -->
    <button class="header-action" title="通知" id="notificationBtn">
      <i class="fas fa-bell"></i>
      <span class="badge" id="notificationBadge">3</span>
    </button>

    <!-- 消息 -->
    <button class="header-action" title="消息" id="messageBtn">
      <i class="fas fa-envelope"></i>
      <span class="badge" id="messageBadge">5</span>
    </button>

    <!-- 全屏 -->
    <button class="header-action" title="全屏" id="fullscreenBtn">
      <i class="fas fa-expand"></i>
    </button>

    <!-- 用户菜单 -->
    <div class="user-menu" id="userMenu">
      <div class="user-avatar">
        <i class="fas fa-user"></i>
      </div>
      <div class="user-info">
        <div class="user-name">管理员</div>
        <div class="user-role">系统管理员</div>
      </div>
      <i class="fas fa-chevron-down"></i>
    </div>
  </div>
</header>

<!-- 通知下拉菜单 -->
<div class="dropdown-menu" id="notificationDropdown" style="display: none; position: absolute; top: 60px; right: 200px; width: 320px; background: white; border-radius: 8px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); z-index: 1000; border: 1px solid #e5e7eb;">
  <div style="padding: 16px; border-bottom: 1px solid #e5e7eb;">
    <div style="display: flex; justify-content: space-between; align-items: center;">
      <h3 style="margin: 0; font-size: 16px; font-weight: 600;">通知</h3>
      <button style="background: none; border: none; color: #3b82f6; font-size: 12px; cursor: pointer;">全部标记为已读</button>
    </div>
  </div>
  <div style="max-height: 300px; overflow-y: auto;">
    <div class="notification-item" style="padding: 12px 16px; border-bottom: 1px solid #f3f4f6; cursor: pointer; transition: background 0.3s;">
      <div style="display: flex; gap: 12px;">
        <div style="width: 8px; height: 8px; background: #ef4444; border-radius: 50%; margin-top: 6px; flex-shrink: 0;"></div>
        <div style="flex: 1;">
          <div style="font-size: 14px; font-weight: 500; color: #111827; margin-bottom: 4px;">设备故障告警</div>
          <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">1号楼空调系统出现故障，需要立即处理</div>
          <div style="font-size: 11px; color: #9ca3af;">2分钟前</div>
        </div>
      </div>
    </div>
    <div class="notification-item" style="padding: 12px 16px; border-bottom: 1px solid #f3f4f6; cursor: pointer; transition: background 0.3s;">
      <div style="display: flex; gap: 12px;">
        <div style="width: 8px; height: 8px; background: #f59e0b; border-radius: 50%; margin-top: 6px; flex-shrink: 0;"></div>
        <div style="flex: 1;">
          <div style="font-size: 14px; font-weight: 500; color: #111827; margin-bottom: 4px;">温度异常</div>
          <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">机房温度超过预设阈值</div>
          <div style="font-size: 11px; color: #9ca3af;">15分钟前</div>
        </div>
      </div>
    </div>
    <div class="notification-item" style="padding: 12px 16px; cursor: pointer; transition: background 0.3s;">
      <div style="display: flex; gap: 12px;">
        <div style="width: 8px; height: 8px; background: #10b981; border-radius: 50%; margin-top: 6px; flex-shrink: 0;"></div>
        <div style="flex: 1;">
          <div style="font-size: 14px; font-weight: 500; color: #111827; margin-bottom: 4px;">工单完成</div>
          <div style="font-size: 12px; color: #6b7280; margin-bottom: 4px;">电梯维护工单已完成</div>
          <div style="font-size: 11px; color: #9ca3af;">1小时前</div>
        </div>
      </div>
    </div>
  </div>
  <div style="padding: 12px 16px; border-top: 1px solid #e5e7eb; text-align: center;">
    <button style="background: none; border: none; color: #3b82f6; font-size: 12px; cursor: pointer;">查看全部通知</button>
  </div>
</div>

<!-- 用户菜单下拉 -->
<div class="dropdown-menu" id="userDropdown" style="display: none; position: absolute; top: 60px; right: 20px; width: 200px; background: white; border-radius: 8px; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); z-index: 1000; border: 1px solid #e5e7eb;">
  <div style="padding: 8px 0;">
    <a href="#" style="display: block; padding: 8px 16px; color: #374151; text-decoration: none; font-size: 14px; transition: background 0.3s;">
      <i class="fas fa-user" style="width: 16px; margin-right: 8px;"></i>
      个人资料
    </a>
    <a href="#" style="display: block; padding: 8px 16px; color: #374151; text-decoration: none; font-size: 14px; transition: background 0.3s;">
      <i class="fas fa-cog" style="width: 16px; margin-right: 8px;"></i>
      账户设置
    </a>
    <a href="#" style="display: block; padding: 8px 16px; color: #374151; text-decoration: none; font-size: 14px; transition: background 0.3s;">
      <i class="fas fa-question-circle" style="width: 16px; margin-right: 8px;"></i>
      帮助中心
    </a>
    <div style="height: 1px; background: #e5e7eb; margin: 8px 0;"></div>
    <a href="#" style="display: block; padding: 8px 16px; color: #ef4444; text-decoration: none; font-size: 14px; transition: background 0.3s;">
      <i class="fas fa-sign-out-alt" style="width: 16px; margin-right: 8px;"></i>
      退出登录
    </a>
  </div>
</div>

<script>
// 头部组件交互逻辑
document.addEventListener('DOMContentLoaded', function() {
  // 通知下拉菜单
  const notificationBtn = document.getElementById('notificationBtn');
  const notificationDropdown = document.getElementById('notificationDropdown');
  
  if (notificationBtn && notificationDropdown) {
    notificationBtn.addEventListener('click', function(e) {
      e.stopPropagation();
      notificationDropdown.style.display = notificationDropdown.style.display === 'none' ? 'block' : 'none';
      // 关闭用户菜单
      const userDropdown = document.getElementById('userDropdown');
      if (userDropdown) userDropdown.style.display = 'none';
    });
  }

  // 用户菜单下拉
  const userMenu = document.getElementById('userMenu');
  const userDropdown = document.getElementById('userDropdown');
  
  if (userMenu && userDropdown) {
    userMenu.addEventListener('click', function(e) {
      e.stopPropagation();
      userDropdown.style.display = userDropdown.style.display === 'none' ? 'block' : 'none';
      // 关闭通知菜单
      if (notificationDropdown) notificationDropdown.style.display = 'none';
    });
  }

  // 点击其他地方关闭下拉菜单
  document.addEventListener('click', function() {
    if (notificationDropdown) notificationDropdown.style.display = 'none';
    if (userDropdown) userDropdown.style.display = 'none';
  });

  // 全屏功能
  const fullscreenBtn = document.getElementById('fullscreenBtn');
  if (fullscreenBtn) {
    fullscreenBtn.addEventListener('click', function() {
      if (!document.fullscreenElement) {
        document.documentElement.requestFullscreen();
        this.innerHTML = '<i class="fas fa-compress"></i>';
      } else {
        document.exitFullscreen();
        this.innerHTML = '<i class="fas fa-expand"></i>';
      }
    });
  }

  // 监听全屏状态变化
  document.addEventListener('fullscreenchange', function() {
    const fullscreenBtn = document.getElementById('fullscreenBtn');
    if (fullscreenBtn) {
      if (document.fullscreenElement) {
        fullscreenBtn.innerHTML = '<i class="fas fa-compress"></i>';
      } else {
        fullscreenBtn.innerHTML = '<i class="fas fa-expand"></i>';
      }
    }
  });

  // 通知项悬停效果
  const notificationItems = document.querySelectorAll('.notification-item');
  notificationItems.forEach(item => {
    item.addEventListener('mouseenter', function() {
      this.style.background = '#f9fafb';
    });
    item.addEventListener('mouseleave', function() {
      this.style.background = 'transparent';
    });
  });

  // 用户菜单项悬停效果
  const userMenuItems = document.querySelectorAll('#userDropdown a');
  userMenuItems.forEach(item => {
    item.addEventListener('mouseenter', function() {
      this.style.background = '#f9fafb';
    });
    item.addEventListener('mouseleave', function() {
      this.style.background = 'transparent';
    });
  });
});

// 更新面包屑导航
function updateBreadcrumb(currentPageName) {
  const breadcrumbCurrent = document.getElementById('breadcrumbCurrent');
  const breadcrumbCurrentText = document.getElementById('breadcrumbCurrentText');
  const breadcrumbSeparator = document.getElementById('breadcrumbSeparator');
  
  if (currentPageName && currentPageName !== '首页') {
    breadcrumbCurrentText.textContent = currentPageName;
    breadcrumbCurrent.style.display = 'block';
    breadcrumbSeparator.style.display = 'block';
  } else {
    breadcrumbCurrent.style.display = 'none';
    breadcrumbSeparator.style.display = 'none';
  }
}
</script>
