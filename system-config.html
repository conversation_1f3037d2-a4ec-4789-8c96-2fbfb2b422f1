<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>系统参数配置 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">系统参数配置</h1>
          <p class="page-description">地图服务配置、时间校准、系统基础参数设置</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">正常</div>
                <div class="stat-label">系统状态</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">v2.1.0</div>
                <div class="stat-label">系统版本</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-code-branch"></i>
              </div>
            </div>
          </div>
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">15天</div>
                <div class="stat-label">运行时间</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-clock"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">85%</div>
                <div class="stat-label">磁盘使用</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-hdd"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 基础系统设置和地图服务配置 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-cogs text-primary"></i>
                基础系统设置
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-info btn-sm" onclick="resetSystemSettings()">
                  <i class="fas fa-undo"></i>
                  重置默认
                </button>
                <button class="btn btn-success btn-sm" onclick="saveSystemSettings()">
                  <i class="fas fa-save"></i>
                  保存设置
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 系统基本信息 -->
              <div class="config-section">
                <h5 class="section-title">
                  <i class="fas fa-info-circle"></i>
                  系统基本信息
                </h5>
                <div class="config-settings">
                  <div class="setting-group">
                    <label class="setting-label">系统名称</label>
                    <div class="setting-control">
                      <input type="text" class="form-control" value="广州农行智慧楼宇" onchange="updateSystemConfig()">
                      <small class="form-text">系统显示名称，将在页面标题和登录界面显示</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">系统版本</label>
                    <div class="setting-control">
                      <input type="text" class="form-control" value="v2.1.0" readonly>
                      <small class="form-text">当前系统版本号，由系统自动管理</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">系统描述</label>
                    <div class="setting-control">
                      <textarea class="form-control" rows="3" onchange="updateSystemConfig()">集成化智慧楼宇管理系统，提供设备监控、能耗管理、安全防护、环境控制等全方位服务</textarea>
                      <small class="form-text">系统功能描述，用于系统介绍和帮助文档</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">管理员邮箱</label>
                    <div class="setting-control">
                      <input type="email" class="form-control" value="<EMAIL>" onchange="updateSystemConfig()">
                      <small class="form-text">系统管理员邮箱，用于接收系统通知和告警</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">系统时区</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateSystemConfig()">
                        <option value="Asia/Shanghai" selected>中国标准时间 (UTC+8)</option>
                        <option value="UTC">协调世界时 (UTC+0)</option>
                        <option value="America/New_York">美国东部时间 (UTC-5)</option>
                        <option value="Europe/London">英国时间 (UTC+0)</option>
                        <option value="Asia/Tokyo">日本时间 (UTC+9)</option>
                      </select>
                      <small class="form-text">系统默认时区设置</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">语言设置</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateSystemConfig()">
                        <option value="zh-CN" selected>简体中文</option>
                        <option value="zh-TW">繁体中文</option>
                        <option value="en-US">English (US)</option>
                        <option value="ja-JP">日本語</option>
                        <option value="ko-KR">한국어</option>
                      </select>
                      <small class="form-text">系统界面显示语言</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 系统运行参数 -->
              <div class="config-section">
                <h5 class="section-title">
                  <i class="fas fa-server"></i>
                  系统运行参数
                </h5>
                <div class="config-settings">
                  <div class="setting-group">
                    <label class="setting-label">会话超时时间</label>
                    <div class="setting-control">
                      <div class="input-group">
                        <input type="number" class="form-control" value="120" min="30" max="480" onchange="updateSystemConfig()">
                        <span class="input-group-text">分钟</span>
                      </div>
                      <small class="form-text">用户无操作后自动退出的时间</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">数据刷新间隔</label>
                    <div class="setting-control">
                      <div class="input-group">
                        <input type="number" class="form-control" value="30" min="10" max="300" onchange="updateSystemConfig()">
                        <span class="input-group-text">秒</span>
                      </div>
                      <small class="form-text">系统数据自动刷新的时间间隔</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">最大并发用户数</label>
                    <div class="setting-control">
                      <input type="number" class="form-control" value="500" min="10" max="10000" onchange="updateSystemConfig()">
                      <small class="form-text">系统支持的最大同时在线用户数</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">系统维护模式</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" onchange="toggleMaintenanceMode(this)">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">启用后将阻止普通用户访问系统</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">调试模式</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" onchange="toggleDebugMode(this)">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">启用后将显示详细的调试信息</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 系统状态监控 -->
              <div class="system-status">
                <h6>系统状态监控</h6>
                <div class="status-grid">
                  <div class="status-item">
                    <div class="status-icon success">
                      <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="status-info">
                      <div class="status-title">系统服务</div>
                      <div class="status-value">正常运行</div>
                    </div>
                  </div>
                  <div class="status-item">
                    <div class="status-icon success">
                      <i class="fas fa-database"></i>
                    </div>
                    <div class="status-info">
                      <div class="status-title">数据库连接</div>
                      <div class="status-value">连接正常</div>
                    </div>
                  </div>
                  <div class="status-item">
                    <div class="status-icon warning">
                      <i class="fas fa-hdd"></i>
                    </div>
                    <div class="status-info">
                      <div class="status-title">磁盘空间</div>
                      <div class="status-value">85% 使用中</div>
                    </div>
                  </div>
                  <div class="status-item">
                    <div class="status-icon success">
                      <i class="fas fa-memory"></i>
                    </div>
                    <div class="status-info">
                      <div class="status-title">内存使用</div>
                      <div class="status-value">62% 使用中</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-map text-success"></i>
                地图服务配置
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="testMapService()">
                <i class="fas fa-vial"></i>
                测试连接
              </button>
            </div>
            <div class="card-body">
              <!-- 地图服务提供商 -->
              <div class="config-section">
                <h5 class="section-title">
                  <i class="fas fa-globe"></i>
                  地图服务提供商
                </h5>
                <div class="config-settings">
                  <div class="setting-group">
                    <label class="setting-label">地图服务商</label>
                    <div class="setting-control">
                      <div class="radio-group">
                        <label class="radio-item">
                          <input type="radio" name="mapProvider" value="baidu" checked onchange="updateMapConfig()">
                          <span class="radio-mark"></span>
                          百度地图 (推荐)
                        </label>
                        <label class="radio-item">
                          <input type="radio" name="mapProvider" value="amap" onchange="updateMapConfig()">
                          <span class="radio-mark"></span>
                          高德地图
                        </label>
                        <label class="radio-item">
                          <input type="radio" name="mapProvider" value="tencent" onchange="updateMapConfig()">
                          <span class="radio-mark"></span>
                          腾讯地图
                        </label>
                        <label class="radio-item">
                          <input type="radio" name="mapProvider" value="google" onchange="updateMapConfig()">
                          <span class="radio-mark"></span>
                          Google Maps
                        </label>
                      </div>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">API密钥</label>
                    <div class="setting-control">
                      <div class="input-group">
                        <input type="password" class="form-control" value="BDak7j2hNpXxxxxxxxxxxxxxxxxxxx" onchange="updateMapConfig()">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this)">
                          <i class="fas fa-eye"></i>
                        </button>
                      </div>
                      <small class="form-text">地图服务API密钥，用于访问地图服务</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">默认地图中心</label>
                    <div class="setting-control">
                      <div class="coordinate-input">
                        <div class="input-group">
                          <span class="input-group-text">经度</span>
                          <input type="number" class="form-control" value="116.404" step="0.000001" onchange="updateMapConfig()">
                        </div>
                        <div class="input-group">
                          <span class="input-group-text">纬度</span>
                          <input type="number" class="form-control" value="39.915" step="0.000001" onchange="updateMapConfig()">
                        </div>
                      </div>
                      <small class="form-text">系统默认显示的地图中心坐标 (北京天安门)</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">默认缩放级别</label>
                    <div class="setting-control">
                      <input type="range" class="form-range" id="mapZoomLevel" min="1" max="20" value="12" onchange="updateMapZoom(this.value)">
                      <span class="range-value" id="mapZoomValue">12级</span>
                      <small class="form-text">地图初始显示的缩放级别</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 地图显示选项 -->
              <div class="config-section">
                <h5 class="section-title">
                  <i class="fas fa-layer-group"></i>
                  地图显示选项
                </h5>
                <div class="config-settings">
                  <div class="setting-group">
                    <label class="setting-label">地图主题</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateMapConfig()">
                        <option value="normal" selected>标准地图</option>
                        <option value="satellite">卫星地图</option>
                        <option value="hybrid">混合地图</option>
                        <option value="terrain">地形地图</option>
                        <option value="dark">深色主题</option>
                      </select>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">显示交通信息</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" checked onchange="updateMapConfig()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">在地图上显示实时交通状况</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">显示建筑物3D</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" onchange="updateMapConfig()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">启用建筑物3D显示效果</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">地图控件</label>
                    <div class="setting-control">
                      <div class="checkbox-group">
                        <label class="checkbox-item">
                          <input type="checkbox" checked onchange="updateMapConfig()">
                          <span class="checkmark"></span>
                          缩放控件
                        </label>
                        <label class="checkbox-item">
                          <input type="checkbox" checked onchange="updateMapConfig()">
                          <span class="checkmark"></span>
                          比例尺
                        </label>
                        <label class="checkbox-item">
                          <input type="checkbox" onchange="updateMapConfig()">
                          <span class="checkmark"></span>
                          全屏按钮
                        </label>
                        <label class="checkbox-item">
                          <input type="checkbox" checked onchange="updateMapConfig()">
                          <span class="checkmark"></span>
                          地图类型切换
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 地图服务状态 -->
              <div class="map-status">
                <h6>地图服务状态</h6>
                <div class="service-status">
                  <div class="status-indicator">
                    <div class="indicator-dot success"></div>
                    <span>百度地图API连接正常</span>
                  </div>
                  <div class="status-details">
                    <div class="detail-item">
                      <span class="detail-label">API调用次数:</span>
                      <span class="detail-value">1,245 / 10,000 (今日)</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">响应时间:</span>
                      <span class="detail-value">156ms (平均)</span>
                    </div>
                    <div class="detail-item">
                      <span class="detail-label">成功率:</span>
                      <span class="detail-value">99.8%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 时间校准设置和数据库配置 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-clock text-info"></i>
                时间校准设置
              </h3>
              <button class="btn btn-primary btn-sm" onclick="syncSystemTime()">
                <i class="fas fa-sync-alt"></i>
                立即同步
              </button>
            </div>
            <div class="card-body">
              <!-- NTP服务器配置 -->
              <div class="config-section">
                <h5 class="section-title">
                  <i class="fas fa-server"></i>
                  NTP时间服务器
                </h5>
                <div class="config-settings">
                  <div class="setting-group">
                    <label class="setting-label">主NTP服务器</label>
                    <div class="setting-control">
                      <input type="text" class="form-control" value="ntp.aliyun.com" onchange="updateTimeConfig()">
                      <small class="form-text">主要的NTP时间同步服务器地址</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">备用NTP服务器</label>
                    <div class="setting-control">
                      <div class="ntp-servers">
                        <input type="text" class="form-control mb-xs" value="time.windows.com" onchange="updateTimeConfig()">
                        <input type="text" class="form-control mb-xs" value="pool.ntp.org" onchange="updateTimeConfig()">
                        <input type="text" class="form-control" value="cn.pool.ntp.org" onchange="updateTimeConfig()">
                      </div>
                      <small class="form-text">备用NTP服务器列表，主服务器不可用时使用</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">同步间隔</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateTimeConfig()">
                        <option value="300">5分钟</option>
                        <option value="900">15分钟</option>
                        <option value="1800" selected>30分钟</option>
                        <option value="3600">1小时</option>
                        <option value="21600">6小时</option>
                        <option value="86400">24小时</option>
                      </select>
                      <small class="form-text">系统自动同步时间的间隔</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">自动时间同步</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" checked onchange="updateTimeConfig()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">启用后系统将自动与NTP服务器同步时间</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 时间显示设置 -->
              <div class="config-section">
                <h5 class="section-title">
                  <i class="fas fa-calendar-alt"></i>
                  时间显示设置
                </h5>
                <div class="config-settings">
                  <div class="setting-group">
                    <label class="setting-label">日期格式</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateTimeConfig()">
                        <option value="YYYY-MM-DD" selected>2024-01-15 (ISO格式)</option>
                        <option value="YYYY/MM/DD">2024/01/15</option>
                        <option value="DD/MM/YYYY">15/01/2024</option>
                        <option value="MM/DD/YYYY">01/15/2024 (美式)</option>
                        <option value="DD-MM-YYYY">15-01-2024</option>
                      </select>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">时间格式</label>
                    <div class="setting-control">
                      <div class="radio-group">
                        <label class="radio-item">
                          <input type="radio" name="timeFormat" value="24" checked onchange="updateTimeConfig()">
                          <span class="radio-mark"></span>
                          24小时制 (14:30:25)
                        </label>
                        <label class="radio-item">
                          <input type="radio" name="timeFormat" value="12" onchange="updateTimeConfig()">
                          <span class="radio-mark"></span>
                          12小时制 (2:30:25 PM)
                        </label>
                      </div>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">显示毫秒</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" onchange="updateTimeConfig()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">在时间显示中包含毫秒信息</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">工作日设置</label>
                    <div class="setting-control">
                      <div class="weekday-selector">
                        <label class="weekday-item">
                          <input type="checkbox" checked>
                          <span class="weekday-mark">一</span>
                        </label>
                        <label class="weekday-item">
                          <input type="checkbox" checked>
                          <span class="weekday-mark">二</span>
                        </label>
                        <label class="weekday-item">
                          <input type="checkbox" checked>
                          <span class="weekday-mark">三</span>
                        </label>
                        <label class="weekday-item">
                          <input type="checkbox" checked>
                          <span class="weekday-mark">四</span>
                        </label>
                        <label class="weekday-item">
                          <input type="checkbox" checked>
                          <span class="weekday-mark">五</span>
                        </label>
                        <label class="weekday-item">
                          <input type="checkbox">
                          <span class="weekday-mark">六</span>
                        </label>
                        <label class="weekday-item">
                          <input type="checkbox">
                          <span class="weekday-mark">日</span>
                        </label>
                      </div>
                      <small class="form-text">定义系统工作日，用于报表和统计</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 时间同步状态 -->
              <div class="time-sync-status">
                <h6>时间同步状态</h6>
                <div class="sync-info">
                  <div class="sync-item">
                    <div class="sync-icon success">
                      <i class="fas fa-check-circle"></i>
                    </div>
                    <div class="sync-details">
                      <div class="sync-title">最后同步时间</div>
                      <div class="sync-value">2024-01-15 14:30:25</div>
                    </div>
                  </div>
                  <div class="sync-item">
                    <div class="sync-icon info">
                      <i class="fas fa-clock"></i>
                    </div>
                    <div class="sync-details">
                      <div class="sync-title">当前系统时间</div>
                      <div class="sync-value" id="currentTime">2024-01-15 14:35:42</div>
                    </div>
                  </div>
                  <div class="sync-item">
                    <div class="sync-icon success">
                      <i class="fas fa-wifi"></i>
                    </div>
                    <div class="sync-details">
                      <div class="sync-title">NTP服务器状态</div>
                      <div class="sync-value">连接正常 (延迟: 12ms)</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-database text-warning"></i>
                数据库配置
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="testDatabaseConnection()">
                  <i class="fas fa-plug"></i>
                  测试连接
                </button>
                <button class="btn btn-outline-success btn-sm" onclick="optimizeDatabase()">
                  <i class="fas fa-magic"></i>
                  优化数据库
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 数据库连接配置 -->
              <div class="config-section">
                <h5 class="section-title">
                  <i class="fas fa-plug"></i>
                  数据库连接配置
                </h5>
                <div class="config-settings">
                  <div class="setting-group">
                    <label class="setting-label">数据库类型</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateDatabaseConfig()">
                        <option value="mysql" selected>MySQL</option>
                        <option value="postgresql">PostgreSQL</option>
                        <option value="oracle">Oracle</option>
                        <option value="sqlserver">SQL Server</option>
                        <option value="mongodb">MongoDB</option>
                      </select>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">服务器地址</label>
                    <div class="setting-control">
                      <input type="text" class="form-control" value="localhost" onchange="updateDatabaseConfig()">
                      <small class="form-text">数据库服务器IP地址或域名</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">端口号</label>
                    <div class="setting-control">
                      <input type="number" class="form-control" value="3306" min="1" max="65535" onchange="updateDatabaseConfig()">
                      <small class="form-text">数据库服务端口号</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">数据库名称</label>
                    <div class="setting-control">
                      <input type="text" class="form-control" value="smart_building" onchange="updateDatabaseConfig()">
                      <small class="form-text">系统使用的数据库名称</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">用户名</label>
                    <div class="setting-control">
                      <input type="text" class="form-control" value="admin" onchange="updateDatabaseConfig()">
                      <small class="form-text">数据库连接用户名</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">密码</label>
                    <div class="setting-control">
                      <div class="input-group">
                        <input type="password" class="form-control" value="********" onchange="updateDatabaseConfig()">
                        <button class="btn btn-outline-secondary" type="button" onclick="togglePasswordVisibility(this)">
                          <i class="fas fa-eye"></i>
                        </button>
                      </div>
                      <small class="form-text">数据库连接密码</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 连接池配置 -->
              <div class="config-section">
                <h5 class="section-title">
                  <i class="fas fa-swimming-pool"></i>
                  连接池配置
                </h5>
                <div class="config-settings">
                  <div class="setting-group">
                    <label class="setting-label">最小连接数</label>
                    <div class="setting-control">
                      <input type="number" class="form-control" value="5" min="1" max="100" onchange="updateDatabaseConfig()">
                      <small class="form-text">连接池保持的最小连接数量</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">最大连接数</label>
                    <div class="setting-control">
                      <input type="number" class="form-control" value="50" min="5" max="1000" onchange="updateDatabaseConfig()">
                      <small class="form-text">连接池允许的最大连接数量</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">连接超时时间</label>
                    <div class="setting-control">
                      <div class="input-group">
                        <input type="number" class="form-control" value="30" min="5" max="300" onchange="updateDatabaseConfig()">
                        <span class="input-group-text">秒</span>
                      </div>
                      <small class="form-text">获取数据库连接的超时时间</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">空闲连接超时</label>
                    <div class="setting-control">
                      <div class="input-group">
                        <input type="number" class="form-control" value="600" min="60" max="3600" onchange="updateDatabaseConfig()">
                        <span class="input-group-text">秒</span>
                      </div>
                      <small class="form-text">空闲连接的超时时间</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 数据库状态监控 -->
              <div class="database-status">
                <h6>数据库状态监控</h6>
                <div class="db-metrics">
                  <div class="metric-item">
                    <div class="metric-icon success">
                      <i class="fas fa-link"></i>
                    </div>
                    <div class="metric-info">
                      <div class="metric-title">连接状态</div>
                      <div class="metric-value">正常 (15/50)</div>
                    </div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-icon info">
                      <i class="fas fa-tachometer-alt"></i>
                    </div>
                    <div class="metric-info">
                      <div class="metric-title">响应时间</div>
                      <div class="metric-value">8ms (平均)</div>
                    </div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-icon success">
                      <i class="fas fa-chart-line"></i>
                    </div>
                    <div class="metric-info">
                      <div class="metric-title">查询性能</div>
                      <div class="metric-value">1,245 QPS</div>
                    </div>
                  </div>
                  <div class="metric-item">
                    <div class="metric-icon warning">
                      <i class="fas fa-hdd"></i>
                    </div>
                    <div class="metric-info">
                      <div class="metric-title">存储空间</div>
                      <div class="metric-value">2.8GB / 10GB</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('系统参数配置');
        }
      }, 100);

      // 初始化系统配置功能
      initSystemConfigSystem();
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 初始化系统配置系统
    function initSystemConfigSystem() {
      // 初始化地图缩放滑块
      initMapZoomSlider();
      // 启动实时时间显示
      startRealTimeClock();
    }

    // 初始化地图缩放滑块
    function initMapZoomSlider() {
      const slider = document.getElementById('mapZoomLevel');
      const valueDisplay = document.getElementById('mapZoomValue');

      if (slider && valueDisplay) {
        slider.addEventListener('input', function() {
          updateMapZoom(this.value);
        });
      }
    }

    // 启动实时时间显示
    function startRealTimeClock() {
      function updateClock() {
        const now = new Date();
        const timeString = now.toLocaleString('zh-CN', {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          hour12: false
        });

        const clockElement = document.getElementById('currentTime');
        if (clockElement) {
          clockElement.textContent = timeString;
        }
      }

      updateClock();
      setInterval(updateClock, 1000);
    }

    // 基础系统设置功能
    function updateSystemConfig() {
      console.log('更新系统配置');
      showNotification('系统配置已更新', 'success');
    }

    function resetSystemSettings() {
      if (confirm('确定要重置为默认系统设置吗？')) {
        console.log('重置系统设置');
        showNotification('系统设置已重置为默认配置', 'info');
      }
    }

    function saveSystemSettings() {
      console.log('保存系统设置');
      showNotification('系统设置保存成功', 'success');
    }

    function toggleMaintenanceMode(checkbox) {
      const isEnabled = checkbox.checked;
      if (isEnabled) {
        if (confirm('启用维护模式将阻止普通用户访问系统，确定要启用吗？')) {
          console.log('启用维护模式');
          showNotification('系统维护模式已启用', 'warning');
        } else {
          checkbox.checked = false;
        }
      } else {
        console.log('关闭维护模式');
        showNotification('系统维护模式已关闭', 'success');
      }
    }

    function toggleDebugMode(checkbox) {
      const isEnabled = checkbox.checked;
      console.log('调试模式:', isEnabled ? '启用' : '关闭');
      showNotification(`调试模式已${isEnabled ? '启用' : '关闭'}`, 'info');
    }

    // 地图服务配置功能
    function updateMapConfig() {
      console.log('更新地图配置');
      showNotification('地图配置已更新', 'success');
    }

    function updateMapZoom(value) {
      document.getElementById('mapZoomValue').textContent = value + '级';
      console.log('更新地图缩放级别:', value);
    }

    function testMapService() {
      console.log('测试地图服务连接');
      showNotification('正在测试地图服务连接...', 'info');

      // 模拟测试过程
      setTimeout(() => {
        showNotification('地图服务连接测试成功', 'success');
      }, 2000);
    }

    function togglePasswordVisibility(button) {
      const input = button.previousElementSibling;
      const icon = button.querySelector('i');

      if (input.type === 'password') {
        input.type = 'text';
        icon.className = 'fas fa-eye-slash';
      } else {
        input.type = 'password';
        icon.className = 'fas fa-eye';
      }
    }

    // 时间校准设置功能
    function updateTimeConfig() {
      console.log('更新时间配置');
      showNotification('时间配置已更新', 'success');
    }

    function syncSystemTime() {
      console.log('同步系统时间');
      showNotification('正在同步系统时间...', 'info');

      // 模拟同步过程
      setTimeout(() => {
        showNotification('系统时间同步成功', 'success');
      }, 3000);
    }

    // 数据库配置功能
    function updateDatabaseConfig() {
      console.log('更新数据库配置');
      showNotification('数据库配置已更新', 'success');
    }

    function testDatabaseConnection() {
      console.log('测试数据库连接');
      showNotification('正在测试数据库连接...', 'info');

      // 模拟测试过程
      setTimeout(() => {
        const success = Math.random() > 0.2; // 80%成功率
        if (success) {
          showNotification('数据库连接测试成功', 'success');
        } else {
          showNotification('数据库连接测试失败，请检查配置', 'error');
        }
      }, 2000);
    }

    function optimizeDatabase() {
      if (confirm('数据库优化可能需要较长时间，确定要执行吗？')) {
        console.log('优化数据库');
        showNotification('正在优化数据库，请稍候...', 'info');

        // 模拟优化过程
        setTimeout(() => {
          showNotification('数据库优化完成', 'success');
        }, 5000);
      }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'times-circle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);
      setTimeout(() => notification.classList.add('show'), 100);

      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
