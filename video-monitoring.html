<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>视频监控系统 - 广州农行智慧楼宇</title>
  
  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- 引入主样式 -->
  <link rel="stylesheet" href="assets/css/main.css">
  
  <style>
    .video-layout {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: var(--spacing-lg);
      height: calc(100vh - 200px);
    }
    
    .video-sidebar {
      background: white;
      border-radius: var(--radius-lg);
      border: 1px solid var(--gray-200);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    .video-main {
      background: white;
      border-radius: var(--radius-lg);
      border: 1px solid var(--gray-200);
      overflow: hidden;
      display: flex;
      flex-direction: column;
    }
    
    .video-tree {
      flex: 1;
      overflow-y: auto;
      padding: var(--spacing-md);
    }
    
    .tree-node {
      margin-bottom: var(--spacing-xs);
    }
    
    .tree-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-sm);
      border-radius: var(--radius-sm);
      cursor: pointer;
      transition: background 0.3s ease;
      font-size: 14px;
    }
    
    .tree-item:hover {
      background: var(--gray-50);
    }
    
    .tree-item.active {
      background: var(--primary-color);
      color: white;
    }
    
    .tree-icon {
      width: 16px;
      margin-right: var(--spacing-sm);
      color: var(--gray-500);
    }
    
    .tree-item.active .tree-icon {
      color: white;
    }
    
    .tree-children {
      margin-left: var(--spacing-lg);
      margin-top: var(--spacing-xs);
    }
    
    .video-grid {
      flex: 1;
      display: grid;
      grid-template-columns: 1fr 1fr;
      grid-template-rows: 1fr 1fr;
      gap: 2px;
      background: var(--gray-300);
      padding: 2px;
    }
    
    .video-cell {
      background: #000;
      position: relative;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 14px;
      cursor: pointer;
      transition: all 0.3s ease;
    }
    
    .video-cell:hover {
      transform: scale(1.02);
      z-index: 10;
    }
    
    .video-cell.active {
      border: 2px solid var(--primary-color);
    }
    
    .video-placeholder {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: var(--spacing-sm);
      opacity: 0.7;
    }
    
    .video-placeholder i {
      font-size: 32px;
    }
    
    .video-info {
      position: absolute;
      top: var(--spacing-sm);
      left: var(--spacing-sm);
      background: rgba(0, 0, 0, 0.7);
      padding: 4px 8px;
      border-radius: var(--radius-sm);
      font-size: 12px;
    }
    
    .video-controls {
      position: absolute;
      bottom: var(--spacing-sm);
      right: var(--spacing-sm);
      display: flex;
      gap: var(--spacing-xs);
      opacity: 0;
      transition: opacity 0.3s ease;
    }
    
    .video-cell:hover .video-controls {
      opacity: 1;
    }
    
    .video-control-btn {
      width: 24px;
      height: 24px;
      background: rgba(0, 0, 0, 0.7);
      border: none;
      border-radius: var(--radius-sm);
      color: white;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 10px;
    }
    
    .video-control-btn:hover {
      background: rgba(0, 0, 0, 0.9);
    }
    
    .video-toolbar {
      padding: var(--spacing-md);
      border-bottom: 1px solid var(--gray-200);
      display: flex;
      align-items: center;
      justify-content: space-between;
      background: var(--gray-50);
    }
    
    .layout-buttons {
      display: flex;
      gap: var(--spacing-xs);
    }
    
    .layout-btn {
      width: 32px;
      height: 32px;
      border: 1px solid var(--gray-300);
      background: white;
      border-radius: var(--radius-sm);
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
    }
    
    .layout-btn:hover {
      background: var(--gray-50);
    }
    
    .layout-btn.active {
      background: var(--primary-color);
      color: white;
      border-color: var(--primary-color);
    }
    
    .playback-controls {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      padding: var(--spacing-md);
      border-top: 1px solid var(--gray-200);
      background: var(--gray-50);
    }
    
    .playback-timeline {
      flex: 1;
      height: 4px;
      background: var(--gray-300);
      border-radius: 2px;
      position: relative;
      cursor: pointer;
    }
    
    .playback-progress {
      height: 100%;
      background: var(--primary-color);
      border-radius: 2px;
      width: 30%;
    }
    
    .playback-time {
      font-size: 12px;
      color: var(--gray-600);
      min-width: 120px;
      text-align: center;
    }
    
    @media (max-width: 1024px) {
      .video-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
      }
      
      .video-sidebar {
        max-height: 200px;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div id="sidebarContainer"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部导航 -->
      <div id="headerContainer"></div>

      <!-- 页面内容 -->
      <div class="page-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">视频监控系统</h1>
          <p class="page-description">视频设备管理、实时预览与录像回放、视频上墙与轮巡控制</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid mb-lg">
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">24</div>
                <div class="stat-label">在线摄像头</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-video"></i>
              </div>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">2</div>
                <div class="stat-label">离线设备</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">8</div>
                <div class="stat-label">录像存储(TB)</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-hdd"></i>
              </div>
            </div>
          </div>

          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">4</div>
                <div class="stat-label">轮巡任务</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-sync-alt"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 视频监控主界面 -->
        <div class="video-layout">
          <!-- 设备树 -->
          <div class="video-sidebar">
            <div class="card-header">
              <h3 class="card-title">设备列表</h3>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i>
              </button>
            </div>
            <div class="video-tree">
              <div class="tree-node">
                <div class="tree-item">
                  <i class="fas fa-building tree-icon"></i>
                  <span>1号楼</span>
                </div>
                <div class="tree-children">
                  <div class="tree-item active">
                    <i class="fas fa-video tree-icon"></i>
                    <span>大厅摄像头01</span>
                  </div>
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>电梯摄像头01</span>
                  </div>
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>走廊摄像头01</span>
                  </div>
                </div>
              </div>
              
              <div class="tree-node">
                <div class="tree-item">
                  <i class="fas fa-building tree-icon"></i>
                  <span>2号楼</span>
                </div>
                <div class="tree-children">
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>大厅摄像头02</span>
                  </div>
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>会议室摄像头01</span>
                  </div>
                </div>
              </div>
              
              <div class="tree-node">
                <div class="tree-item">
                  <i class="fas fa-car tree-icon"></i>
                  <span>停车场</span>
                </div>
                <div class="tree-children">
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>入口摄像头</span>
                  </div>
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>出口摄像头</span>
                  </div>
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>地下车库01</span>
                  </div>
                </div>
              </div>
              
              <div class="tree-node">
                <div class="tree-item">
                  <i class="fas fa-shield-alt tree-icon"></i>
                  <span>周界监控</span>
                </div>
                <div class="tree-children">
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>东门摄像头</span>
                  </div>
                  <div class="tree-item">
                    <i class="fas fa-video tree-icon"></i>
                    <span>西门摄像头</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 视频显示区域 -->
          <div class="video-main">
            <!-- 工具栏 -->
            <div class="video-toolbar">
              <div class="d-flex gap-md">
                <div class="layout-buttons">
                  <button class="layout-btn" title="1画面">
                    <i class="fas fa-square"></i>
                  </button>
                  <button class="layout-btn active" title="4画面">
                    <i class="fas fa-th-large"></i>
                  </button>
                  <button class="layout-btn" title="9画面">
                    <i class="fas fa-th"></i>
                  </button>
                  <button class="layout-btn" title="16画面">
                    <i class="fas fa-border-all"></i>
                  </button>
                </div>
                
                <div class="d-flex gap-sm">
                  <button class="btn btn-sm btn-primary">
                    <i class="fas fa-play"></i>
                    轮巡
                  </button>
                  <button class="btn btn-sm btn-secondary">
                    <i class="fas fa-camera"></i>
                    抓拍
                  </button>
                  <button class="btn btn-sm btn-secondary">
                    <i class="fas fa-record-vinyl"></i>
                    录像
                  </button>
                </div>
              </div>
              
              <div class="d-flex gap-sm">
                <button class="btn btn-sm btn-secondary">
                  <i class="fas fa-expand"></i>
                  全屏
                </button>
                <button class="btn btn-sm btn-secondary">
                  <i class="fas fa-cog"></i>
                  设置
                </button>
              </div>
            </div>

            <!-- 视频网格 -->
            <div class="video-grid">
              <div class="video-cell active">
                <div class="video-info">大厅摄像头01</div>
                <div class="video-placeholder">
                  <i class="fas fa-video"></i>
                  <span>实时视频流</span>
                </div>
                <div class="video-controls">
                  <button class="video-control-btn" title="云台控制">
                    <i class="fas fa-arrows-alt"></i>
                  </button>
                  <button class="video-control-btn" title="放大">
                    <i class="fas fa-search-plus"></i>
                  </button>
                  <button class="video-control-btn" title="录像">
                    <i class="fas fa-record-vinyl"></i>
                  </button>
                </div>
              </div>
              
              <div class="video-cell">
                <div class="video-info">电梯摄像头01</div>
                <div class="video-placeholder">
                  <i class="fas fa-video-slash"></i>
                  <span>点击选择摄像头</span>
                </div>
              </div>
              
              <div class="video-cell">
                <div class="video-info">走廊摄像头01</div>
                <div class="video-placeholder">
                  <i class="fas fa-video-slash"></i>
                  <span>点击选择摄像头</span>
                </div>
              </div>
              
              <div class="video-cell">
                <div class="video-info">停车场摄像头</div>
                <div class="video-placeholder">
                  <i class="fas fa-video-slash"></i>
                  <span>点击选择摄像头</span>
                </div>
              </div>
            </div>

            <!-- 回放控制 -->
            <div class="playback-controls">
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-step-backward"></i>
              </button>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-play"></i>
              </button>
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-pause"></i>
              </button>
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-step-forward"></i>
              </button>
              
              <div class="playback-time">14:30:25 / 17:45:12</div>
              
              <div class="playback-timeline">
                <div class="playback-progress"></div>
              </div>
              
              <div class="d-flex gap-sm">
                <input type="date" class="form-control" style="width: 140px; padding: 4px 8px; font-size: 12px;" value="2024-01-15">
                <input type="time" class="form-control" style="width: 100px; padding: 4px 8px; font-size: 12px;" value="14:30">
                <button class="btn btn-sm btn-primary">
                  <i class="fas fa-search"></i>
                  查询
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入主JavaScript -->
  <script src="assets/js/main.js"></script>
  
  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载组件
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      
      // 初始化视频监控功能
      initVideoControls();
      
      // 更新面包屑
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('视频监控系统');
        }
      }, 100);
    });

    // 加载组件
    function loadComponent(containerId, componentPath) {
      fetch(componentPath)
        .then(response => response.text())
        .then(html => {
          document.getElementById(containerId).innerHTML = html;
        })
        .catch(error => {
          console.error('Error loading component:', error);
        });
    }

    // 初始化视频控制
    function initVideoControls() {
      // 设备树点击事件
      document.querySelectorAll('.tree-item').forEach(item => {
        item.addEventListener('click', function() {
          // 移除其他活动状态
          document.querySelectorAll('.tree-item').forEach(i => i.classList.remove('active'));
          // 添加当前活动状态
          this.classList.add('active');
          
          // 如果是摄像头设备，加载到视频窗口
          if (this.querySelector('.fa-video')) {
            const cameraName = this.textContent.trim();
            loadCameraToActiveCell(cameraName);
          }
        });
      });

      // 布局按钮点击事件
      document.querySelectorAll('.layout-btn').forEach(btn => {
        btn.addEventListener('click', function() {
          document.querySelectorAll('.layout-btn').forEach(b => b.classList.remove('active'));
          this.classList.add('active');
          
          // 根据布局调整视频网格
          const videoGrid = document.querySelector('.video-grid');
          if (this.title === '1画面') {
            videoGrid.style.gridTemplateColumns = '1fr';
            videoGrid.style.gridTemplateRows = '1fr';
          } else if (this.title === '4画面') {
            videoGrid.style.gridTemplateColumns = '1fr 1fr';
            videoGrid.style.gridTemplateRows = '1fr 1fr';
          } else if (this.title === '9画面') {
            videoGrid.style.gridTemplateColumns = '1fr 1fr 1fr';
            videoGrid.style.gridTemplateRows = '1fr 1fr 1fr';
          } else if (this.title === '16画面') {
            videoGrid.style.gridTemplateColumns = '1fr 1fr 1fr 1fr';
            videoGrid.style.gridTemplateRows = '1fr 1fr 1fr 1fr';
          }
        });
      });

      // 视频窗口点击事件
      document.querySelectorAll('.video-cell').forEach(cell => {
        cell.addEventListener('click', function() {
          document.querySelectorAll('.video-cell').forEach(c => c.classList.remove('active'));
          this.classList.add('active');
        });
      });
    }

    // 加载摄像头到活动窗口
    function loadCameraToActiveCell(cameraName) {
      const activeCell = document.querySelector('.video-cell.active');
      if (activeCell) {
        const videoInfo = activeCell.querySelector('.video-info');
        const placeholder = activeCell.querySelector('.video-placeholder');
        
        if (videoInfo) {
          videoInfo.textContent = cameraName;
        }
        
        if (placeholder) {
          placeholder.innerHTML = `
            <i class="fas fa-video"></i>
            <span>实时视频流</span>
          `;
        }
        
        if (window.app) {
          window.app.showNotification(`已加载 ${cameraName}`, 'success');
        }
      }
    }
  </script>
</body>
</html>
