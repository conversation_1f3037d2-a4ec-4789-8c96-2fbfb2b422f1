<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录与安全策略 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">登录与安全策略</h1>
          <p class="page-description">密码策略、验证码设置、IP限制、登录安全配置</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">1,245</div>
                <div class="stat-label">今日登录</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-sign-in-alt"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">23</div>
                <div class="stat-label">登录失败</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">在线用户</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-users"></i>
              </div>
            </div>
          </div>
          <div class="stat-card danger">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">5</div>
                <div class="stat-label">IP黑名单</div>
              </div>
              <div class="stat-icon danger">
                <i class="fas fa-ban"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 密码策略配置和登录安全设置 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-key text-primary"></i>
                密码策略配置
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-info btn-sm" onclick="resetPasswordPolicy()">
                  <i class="fas fa-undo"></i>
                  重置默认
                </button>
                <button class="btn btn-success btn-sm" onclick="savePasswordPolicy()">
                  <i class="fas fa-save"></i>
                  保存策略
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 密码复杂度设置 -->
              <div class="policy-section">
                <h5 class="section-title">
                  <i class="fas fa-shield-alt"></i>
                  密码复杂度要求
                </h5>
                <div class="policy-settings">
                  <div class="setting-group">
                    <label class="setting-label">最小密码长度</label>
                    <div class="setting-control">
                      <input type="range" class="form-range" id="minPasswordLength" min="6" max="20" value="8" onchange="updatePasswordLength(this.value)">
                      <span class="range-value" id="passwordLengthValue">8位</span>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">密码组成要求</label>
                    <div class="setting-control">
                      <div class="checkbox-group">
                        <label class="checkbox-item">
                          <input type="checkbox" checked onchange="updatePasswordPolicy()">
                          <span class="checkmark"></span>
                          包含大写字母 (A-Z)
                        </label>
                        <label class="checkbox-item">
                          <input type="checkbox" checked onchange="updatePasswordPolicy()">
                          <span class="checkmark"></span>
                          包含小写字母 (a-z)
                        </label>
                        <label class="checkbox-item">
                          <input type="checkbox" checked onchange="updatePasswordPolicy()">
                          <span class="checkmark"></span>
                          包含数字 (0-9)
                        </label>
                        <label class="checkbox-item">
                          <input type="checkbox" onchange="updatePasswordPolicy()">
                          <span class="checkmark"></span>
                          包含特殊字符 (!@#$%^&*)
                        </label>
                      </div>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">密码历史限制</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updatePasswordPolicy()">
                        <option value="0">不限制</option>
                        <option value="3">不能重复最近3个密码</option>
                        <option value="5" selected>不能重复最近5个密码</option>
                        <option value="10">不能重复最近10个密码</option>
                      </select>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">密码有效期</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updatePasswordPolicy()">
                        <option value="0">永不过期</option>
                        <option value="30">30天</option>
                        <option value="60">60天</option>
                        <option value="90" selected>90天</option>
                        <option value="180">180天</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 密码策略预览 -->
              <div class="policy-preview">
                <h6>当前密码策略预览</h6>
                <div class="preview-content">
                  <div class="preview-item">
                    <i class="fas fa-check-circle text-success"></i>
                    <span>密码长度至少8位</span>
                  </div>
                  <div class="preview-item">
                    <i class="fas fa-check-circle text-success"></i>
                    <span>必须包含大写字母</span>
                  </div>
                  <div class="preview-item">
                    <i class="fas fa-check-circle text-success"></i>
                    <span>必须包含小写字母</span>
                  </div>
                  <div class="preview-item">
                    <i class="fas fa-check-circle text-success"></i>
                    <span>必须包含数字</span>
                  </div>
                  <div class="preview-item">
                    <i class="fas fa-times-circle text-muted"></i>
                    <span>特殊字符可选</span>
                  </div>
                  <div class="preview-item">
                    <i class="fas fa-clock text-warning"></i>
                    <span>密码90天后过期</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-sign-in-alt text-success"></i>
                登录安全设置
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="testLoginSettings()">
                <i class="fas fa-vial"></i>
                测试设置
              </button>
            </div>
            <div class="card-body">
              <!-- 登录尝试限制 -->
              <div class="policy-section">
                <h5 class="section-title">
                  <i class="fas fa-lock"></i>
                  登录尝试限制
                </h5>
                <div class="policy-settings">
                  <div class="setting-group">
                    <label class="setting-label">最大失败次数</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateLoginPolicy()">
                        <option value="3">3次</option>
                        <option value="5" selected>5次</option>
                        <option value="10">10次</option>
                        <option value="0">不限制</option>
                      </select>
                      <small class="form-text">超过限制后将锁定账户</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">锁定时间</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateLoginPolicy()">
                        <option value="5">5分钟</option>
                        <option value="15" selected>15分钟</option>
                        <option value="30">30分钟</option>
                        <option value="60">1小时</option>
                        <option value="1440">24小时</option>
                      </select>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">会话超时</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateLoginPolicy()">
                        <option value="30">30分钟</option>
                        <option value="60">1小时</option>
                        <option value="120" selected>2小时</option>
                        <option value="480">8小时</option>
                        <option value="0">不超时</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 登录安全选项 -->
              <div class="policy-section">
                <h5 class="section-title">
                  <i class="fas fa-shield-check"></i>
                  安全选项
                </h5>
                <div class="policy-settings">
                  <div class="setting-group">
                    <label class="setting-label">强制HTTPS登录</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" checked onchange="updateSecurityOptions()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">强制使用HTTPS协议登录</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">记住登录状态</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" checked onchange="updateSecurityOptions()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">允许用户选择记住登录状态</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">单点登录 (SSO)</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" onchange="updateSecurityOptions()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">启用单点登录功能</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">异地登录提醒</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" checked onchange="updateSecurityOptions()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">检测到异地登录时发送提醒</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- IP访问控制和验证码配置 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-globe text-warning"></i>
                IP访问控制
              </h3>
              <button class="btn btn-primary btn-sm" onclick="showAddIPModal()">
                <i class="fas fa-plus"></i>
                添加IP规则
              </button>
            </div>
            <div class="card-body">
              <!-- IP控制策略 -->
              <div class="policy-section">
                <h5 class="section-title">
                  <i class="fas fa-filter"></i>
                  访问控制策略
                </h5>
                <div class="policy-settings">
                  <div class="setting-group">
                    <label class="setting-label">默认访问策略</label>
                    <div class="setting-control">
                      <div class="radio-group">
                        <label class="radio-item">
                          <input type="radio" name="defaultPolicy" value="allow" checked onchange="updateIPPolicy()">
                          <span class="radio-mark"></span>
                          允许所有IP访问 (推荐)
                        </label>
                        <label class="radio-item">
                          <input type="radio" name="defaultPolicy" value="deny" onchange="updateIPPolicy()">
                          <span class="radio-mark"></span>
                          拒绝所有IP访问 (仅白名单)
                        </label>
                      </div>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">IP地理位置限制</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" onchange="updateIPPolicy()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">启用IP地理位置检测和限制</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- IP白名单 -->
              <div class="ip-section">
                <h6 class="ip-section-title">
                  <i class="fas fa-check-circle text-success"></i>
                  IP白名单 (允许访问)
                </h6>
                <div class="ip-list">
                  <div class="ip-item allow">
                    <div class="ip-info">
                      <div class="ip-address">***********/24</div>
                      <div class="ip-description">内网办公网段</div>
                      <div class="ip-meta">
                        <span class="ip-type">网段</span>
                        <span class="ip-status active">生效中</span>
                        <span class="ip-date">添加时间: 2024-01-10</span>
                      </div>
                    </div>
                    <div class="ip-actions">
                      <button class="btn btn-xs btn-outline-primary" onclick="editIPRule('ALLOW_001')">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button class="btn btn-xs btn-outline-danger" onclick="deleteIPRule('ALLOW_001')">
                        <i class="fas fa-trash"></i>
                        删除
                      </button>
                    </div>
                  </div>

                  <div class="ip-item allow">
                    <div class="ip-info">
                      <div class="ip-address">************</div>
                      <div class="ip-description">VPN出口IP</div>
                      <div class="ip-meta">
                        <span class="ip-type">单IP</span>
                        <span class="ip-status active">生效中</span>
                        <span class="ip-date">添加时间: 2024-01-12</span>
                      </div>
                    </div>
                    <div class="ip-actions">
                      <button class="btn btn-xs btn-outline-primary" onclick="editIPRule('ALLOW_002')">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button class="btn btn-xs btn-outline-danger" onclick="deleteIPRule('ALLOW_002')">
                        <i class="fas fa-trash"></i>
                        删除
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <!-- IP黑名单 -->
              <div class="ip-section">
                <h6 class="ip-section-title">
                  <i class="fas fa-ban text-danger"></i>
                  IP黑名单 (拒绝访问)
                </h6>
                <div class="ip-list">
                  <div class="ip-item deny">
                    <div class="ip-info">
                      <div class="ip-address">************</div>
                      <div class="ip-description">恶意攻击IP</div>
                      <div class="ip-meta">
                        <span class="ip-type">单IP</span>
                        <span class="ip-status active">生效中</span>
                        <span class="ip-date">添加时间: 2024-01-14</span>
                      </div>
                    </div>
                    <div class="ip-actions">
                      <button class="btn btn-xs btn-outline-primary" onclick="editIPRule('DENY_001')">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button class="btn btn-xs btn-outline-success" onclick="removeFromBlacklist('DENY_001')">
                        <i class="fas fa-unlock"></i>
                        解封
                      </button>
                    </div>
                  </div>

                  <div class="ip-item deny">
                    <div class="ip-info">
                      <div class="ip-address">***********/24</div>
                      <div class="ip-description">可疑网段</div>
                      <div class="ip-meta">
                        <span class="ip-type">网段</span>
                        <span class="ip-status active">生效中</span>
                        <span class="ip-date">添加时间: 2024-01-13</span>
                      </div>
                    </div>
                    <div class="ip-actions">
                      <button class="btn btn-xs btn-outline-primary" onclick="editIPRule('DENY_002')">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                      <button class="btn btn-xs btn-outline-success" onclick="removeFromBlacklist('DENY_002')">
                        <i class="fas fa-unlock"></i>
                        解封
                      </button>
                    </div>
                  </div>

                  <div class="ip-item deny temporary">
                    <div class="ip-info">
                      <div class="ip-address">***********</div>
                      <div class="ip-description">暴力破解尝试</div>
                      <div class="ip-meta">
                        <span class="ip-type">单IP</span>
                        <span class="ip-status temporary">临时封禁</span>
                        <span class="ip-date">剩余时间: 2小时15分</span>
                      </div>
                    </div>
                    <div class="ip-actions">
                      <button class="btn btn-xs btn-outline-success" onclick="removeFromBlacklist('DENY_003')">
                        <i class="fas fa-unlock"></i>
                        立即解封
                      </button>
                      <button class="btn btn-xs btn-outline-danger" onclick="permanentBan('DENY_003')">
                        <i class="fas fa-ban"></i>
                        永久封禁
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-puzzle-piece text-info"></i>
                验证码配置
              </h3>
              <button class="btn btn-outline-success btn-sm" onclick="testCaptcha()">
                <i class="fas fa-eye"></i>
                预览验证码
              </button>
            </div>
            <div class="card-body">
              <!-- 验证码类型设置 -->
              <div class="policy-section">
                <h5 class="section-title">
                  <i class="fas fa-cogs"></i>
                  验证码类型
                </h5>
                <div class="policy-settings">
                  <div class="setting-group">
                    <label class="setting-label">验证码类型</label>
                    <div class="setting-control">
                      <div class="radio-group">
                        <label class="radio-item">
                          <input type="radio" name="captchaType" value="image" checked onchange="updateCaptchaSettings()">
                          <span class="radio-mark"></span>
                          图片验证码
                        </label>
                        <label class="radio-item">
                          <input type="radio" name="captchaType" value="slider" onchange="updateCaptchaSettings()">
                          <span class="radio-mark"></span>
                          滑动验证码
                        </label>
                        <label class="radio-item">
                          <input type="radio" name="captchaType" value="puzzle" onchange="updateCaptchaSettings()">
                          <span class="radio-mark"></span>
                          拼图验证码
                        </label>
                        <label class="radio-item">
                          <input type="radio" name="captchaType" value="sms" onchange="updateCaptchaSettings()">
                          <span class="radio-mark"></span>
                          短信验证码
                        </label>
                      </div>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">验证码难度</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateCaptchaSettings()">
                        <option value="easy">简单 (4位数字)</option>
                        <option value="medium" selected>中等 (4位字母数字)</option>
                        <option value="hard">困难 (6位字母数字)</option>
                        <option value="extreme">极难 (复杂图形)</option>
                      </select>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">验证码有效期</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateCaptchaSettings()">
                        <option value="60">1分钟</option>
                        <option value="300" selected>5分钟</option>
                        <option value="600">10分钟</option>
                        <option value="1800">30分钟</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 验证码触发条件 -->
              <div class="policy-section">
                <h5 class="section-title">
                  <i class="fas fa-play-circle"></i>
                  触发条件
                </h5>
                <div class="policy-settings">
                  <div class="setting-group">
                    <label class="setting-label">登录失败次数</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateCaptchaSettings()">
                        <option value="1">第1次失败后</option>
                        <option value="2">第2次失败后</option>
                        <option value="3" selected>第3次失败后</option>
                        <option value="5">第5次失败后</option>
                        <option value="0">始终显示</option>
                      </select>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">可疑IP自动触发</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" checked onchange="updateCaptchaSettings()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">检测到可疑IP时自动显示验证码</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">异地登录触发</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" checked onchange="updateCaptchaSettings()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">检测到异地登录时显示验证码</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 验证码预览 -->
              <div class="captcha-preview">
                <h6>验证码预览</h6>
                <div class="preview-container">
                  <div class="captcha-demo">
                    <div class="captcha-image">
                      <img src="https://via.placeholder.com/120x40/4F46E5/FFFFFF?text=A8K9" alt="验证码">
                      <button class="refresh-captcha" onclick="refreshCaptchaPreview()">
                        <i class="fas fa-sync-alt"></i>
                      </button>
                    </div>
                    <input type="text" class="captcha-input" placeholder="请输入验证码" readonly>
                  </div>
                  <div class="captcha-info">
                    <div class="info-item">
                      <i class="fas fa-clock"></i>
                      <span>有效期: 5分钟</span>
                    </div>
                    <div class="info-item">
                      <i class="fas fa-shield-alt"></i>
                      <span>难度: 中等</span>
                    </div>
                    <div class="info-item">
                      <i class="fas fa-eye"></i>
                      <span>类型: 图片验证码</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 会话管理和安全审计 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-clock text-success"></i>
                会话管理
              </h3>
              <button class="btn btn-outline-danger btn-sm" onclick="forceLogoutAll()">
                <i class="fas fa-sign-out-alt"></i>
                强制全部下线
              </button>
            </div>
            <div class="card-body">
              <!-- 会话策略设置 -->
              <div class="policy-section">
                <h5 class="section-title">
                  <i class="fas fa-user-clock"></i>
                  会话策略
                </h5>
                <div class="policy-settings">
                  <div class="setting-group">
                    <label class="setting-label">最大并发会话数</label>
                    <div class="setting-control">
                      <select class="form-control" onchange="updateSessionPolicy()">
                        <option value="1">1个 (单点登录)</option>
                        <option value="3" selected>3个</option>
                        <option value="5">5个</option>
                        <option value="10">10个</option>
                        <option value="0">不限制</option>
                      </select>
                      <small class="form-text">每个用户最多同时在线的会话数</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">会话空闲超时</label>
                    <div class="setting-control">
                      <input type="range" class="form-range" id="sessionTimeout" min="15" max="480" value="120" onchange="updateSessionTimeout(this.value)">
                      <span class="range-value" id="sessionTimeoutValue">120分钟</span>
                      <small class="form-text">用户无操作后自动退出时间</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">会话延长提醒</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" checked onchange="updateSessionPolicy()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">会话即将过期时提醒用户延长</small>
                    </div>
                  </div>

                  <div class="setting-group">
                    <label class="setting-label">跨设备会话同步</label>
                    <div class="setting-control">
                      <label class="switch">
                        <input type="checkbox" onchange="updateSessionPolicy()">
                        <span class="slider"></span>
                      </label>
                      <small class="form-text">在一个设备退出时同步退出其他设备</small>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 当前活跃会话 -->
              <div class="session-list">
                <h6>当前活跃会话 (156个)</h6>
                <div class="session-items">
                  <div class="session-item">
                    <div class="session-info">
                      <div class="session-user">
                        <div class="user-avatar">
                          <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                          <div class="user-name">张三 (技术部)</div>
                          <div class="user-role">系统管理员</div>
                        </div>
                      </div>
                      <div class="session-details">
                        <div class="detail-item">
                          <i class="fas fa-desktop"></i>
                          <span>Windows 10 - Chrome 120</span>
                        </div>
                        <div class="detail-item">
                          <i class="fas fa-map-marker-alt"></i>
                          <span>************* (北京)</span>
                        </div>
                        <div class="detail-item">
                          <i class="fas fa-clock"></i>
                          <span>登录时间: 08:30 | 活跃: 2分钟前</span>
                        </div>
                      </div>
                    </div>
                    <div class="session-status">
                      <span class="status-badge success">活跃</span>
                      <div class="session-actions">
                        <button class="btn btn-xs btn-outline-primary" onclick="viewSessionDetail('SESSION_001')">
                          <i class="fas fa-eye"></i>
                          详情
                        </button>
                        <button class="btn btn-xs btn-outline-danger" onclick="forceLogout('SESSION_001')">
                          <i class="fas fa-sign-out-alt"></i>
                          强制下线
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="session-item">
                    <div class="session-info">
                      <div class="session-user">
                        <div class="user-avatar">
                          <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                          <div class="user-name">李四 (财务部)</div>
                          <div class="user-role">普通用户</div>
                        </div>
                      </div>
                      <div class="session-details">
                        <div class="detail-item">
                          <i class="fas fa-mobile-alt"></i>
                          <span>iPhone 15 - Safari</span>
                        </div>
                        <div class="detail-item">
                          <i class="fas fa-map-marker-alt"></i>
                          <span>********* (上海)</span>
                        </div>
                        <div class="detail-item">
                          <i class="fas fa-clock"></i>
                          <span>登录时间: 09:15 | 活跃: 15分钟前</span>
                        </div>
                      </div>
                    </div>
                    <div class="session-status">
                      <span class="status-badge warning">空闲</span>
                      <div class="session-actions">
                        <button class="btn btn-xs btn-outline-primary" onclick="viewSessionDetail('SESSION_002')">
                          <i class="fas fa-eye"></i>
                          详情
                        </button>
                        <button class="btn btn-xs btn-outline-danger" onclick="forceLogout('SESSION_002')">
                          <i class="fas fa-sign-out-alt"></i>
                          强制下线
                        </button>
                      </div>
                    </div>
                  </div>

                  <div class="session-item suspicious">
                    <div class="session-info">
                      <div class="session-user">
                        <div class="user-avatar">
                          <i class="fas fa-user"></i>
                        </div>
                        <div class="user-details">
                          <div class="user-name">王五 (市场部)</div>
                          <div class="user-role">普通用户</div>
                        </div>
                      </div>
                      <div class="session-details">
                        <div class="detail-item">
                          <i class="fas fa-desktop"></i>
                          <span>Linux - Firefox 121</span>
                        </div>
                        <div class="detail-item">
                          <i class="fas fa-map-marker-alt"></i>
                          <span>************ (广州)</span>
                        </div>
                        <div class="detail-item">
                          <i class="fas fa-exclamation-triangle"></i>
                          <span>异地登录 | 活跃: 刚刚</span>
                        </div>
                      </div>
                    </div>
                    <div class="session-status">
                      <span class="status-badge danger">可疑</span>
                      <div class="session-actions">
                        <button class="btn btn-xs btn-outline-warning" onclick="verifySession('SESSION_003')">
                          <i class="fas fa-shield-alt"></i>
                          验证
                        </button>
                        <button class="btn btn-xs btn-outline-danger" onclick="forceLogout('SESSION_003')">
                          <i class="fas fa-ban"></i>
                          立即阻断
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-file-alt text-info"></i>
                安全审计日志
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshAuditLog()">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
                <button class="btn btn-outline-success btn-sm" onclick="exportAuditLog()">
                  <i class="fas fa-download"></i>
                  导出日志
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 日志筛选 -->
              <div class="audit-filters mb-md">
                <div class="filter-row">
                  <select class="form-control" id="logLevel" onchange="filterAuditLog()">
                    <option value="">全部级别</option>
                    <option value="info">信息</option>
                    <option value="warning">警告</option>
                    <option value="error">错误</option>
                    <option value="critical">严重</option>
                  </select>
                  <select class="form-control" id="logCategory" onchange="filterAuditLog()">
                    <option value="">全部分类</option>
                    <option value="login">登录事件</option>
                    <option value="logout">退出事件</option>
                    <option value="security">安全事件</option>
                    <option value="system">系统事件</option>
                  </select>
                  <input type="date" class="form-control" id="logDate" value="2024-01-15" onchange="filterAuditLog()">
                  <input type="text" class="form-control" placeholder="搜索用户或IP..." id="logSearch">
                  <button class="btn btn-outline-primary" onclick="searchAuditLog()">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>

              <!-- 审计日志列表 -->
              <div class="audit-log">
                <div class="log-item info">
                  <div class="log-time">
                    <div class="time-value">08:30:15</div>
                    <div class="date-value">2024-01-15</div>
                  </div>
                  <div class="log-level info">
                    <i class="fas fa-info-circle"></i>
                    <span>信息</span>
                  </div>
                  <div class="log-content">
                    <div class="log-title">用户登录成功</div>
                    <div class="log-details">
                      <div class="detail-item">用户: 张三 (<EMAIL>)</div>
                      <div class="detail-item">IP: ************* (北京)</div>
                      <div class="detail-item">设备: Windows 10 - Chrome 120</div>
                    </div>
                  </div>
                  <div class="log-actions">
                    <button class="btn btn-xs btn-outline-primary" onclick="viewLogDetail('LOG_001')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                  </div>
                </div>

                <div class="log-item warning">
                  <div class="log-time">
                    <div class="time-value">09:45:22</div>
                    <div class="date-value">2024-01-15</div>
                  </div>
                  <div class="log-level warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <span>警告</span>
                  </div>
                  <div class="log-content">
                    <div class="log-title">登录失败尝试</div>
                    <div class="log-details">
                      <div class="detail-item">用户: admin (密码错误)</div>
                      <div class="detail-item">IP: ************ (未知地区)</div>
                      <div class="detail-item">尝试次数: 3次</div>
                    </div>
                  </div>
                  <div class="log-actions">
                    <button class="btn btn-xs btn-outline-primary" onclick="viewLogDetail('LOG_002')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-xs btn-outline-danger" onclick="blockIP('************')">
                      <i class="fas fa-ban"></i>
                      封禁IP
                    </button>
                  </div>
                </div>

                <div class="log-item error">
                  <div class="log-time">
                    <div class="time-value">10:12:08</div>
                    <div class="date-value">2024-01-15</div>
                  </div>
                  <div class="log-level error">
                    <i class="fas fa-times-circle"></i>
                    <span>错误</span>
                  </div>
                  <div class="log-content">
                    <div class="log-title">可疑登录行为</div>
                    <div class="log-details">
                      <div class="detail-item">用户: 王五 (<EMAIL>)</div>
                      <div class="detail-item">IP: ************ (广州) - 异地登录</div>
                      <div class="detail-item">风险评分: 85/100 (高风险)</div>
                    </div>
                  </div>
                  <div class="log-actions">
                    <button class="btn btn-xs btn-outline-primary" onclick="viewLogDetail('LOG_003')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-xs btn-outline-warning" onclick="requireVerification('LOG_003')">
                      <i class="fas fa-shield-alt"></i>
                      要求验证
                    </button>
                  </div>
                </div>

                <div class="log-item critical">
                  <div class="log-time">
                    <div class="time-value">11:30:45</div>
                    <div class="date-value">2024-01-15</div>
                  </div>
                  <div class="log-level critical">
                    <i class="fas fa-exclamation-circle"></i>
                    <span>严重</span>
                  </div>
                  <div class="log-content">
                    <div class="log-title">暴力破解攻击</div>
                    <div class="log-details">
                      <div class="detail-item">目标用户: admin, root, administrator</div>
                      <div class="detail-item">攻击IP: *********** (俄罗斯)</div>
                      <div class="detail-item">尝试次数: 50次/分钟</div>
                    </div>
                  </div>
                  <div class="log-actions">
                    <button class="btn btn-xs btn-outline-primary" onclick="viewLogDetail('LOG_004')">
                      <i class="fas fa-eye"></i>
                      详情
                    </button>
                    <button class="btn btn-xs btn-danger" onclick="emergencyBlock('***********')">
                      <i class="fas fa-shield-alt"></i>
                      紧急阻断
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('登录与安全策略');
        }
      }, 100);

      // 初始化安全策略配置功能
      initSecurityPolicySystem();
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 初始化安全策略配置系统
    function initSecurityPolicySystem() {
      // 初始化密码策略预览
      updatePasswordPolicyPreview();
      // 初始化会话超时滑块
      initSessionTimeoutSlider();
    }

    // 初始化会话超时滑块
    function initSessionTimeoutSlider() {
      const slider = document.getElementById('sessionTimeout');
      const valueDisplay = document.getElementById('sessionTimeoutValue');

      if (slider && valueDisplay) {
        slider.addEventListener('input', function() {
          updateSessionTimeout(this.value);
        });
      }
    }

    // 密码策略配置功能
    function updatePasswordLength(value) {
      document.getElementById('passwordLengthValue').textContent = value + '位';
      updatePasswordPolicyPreview();
    }

    function updatePasswordPolicy() {
      console.log('更新密码策略');
      updatePasswordPolicyPreview();
      showNotification('密码策略已更新', 'success');
    }

    function updatePasswordPolicyPreview() {
      // 这里可以根据当前设置更新预览内容
      console.log('更新密码策略预览');
    }

    function resetPasswordPolicy() {
      if (confirm('确定要重置为默认密码策略吗？')) {
        console.log('重置密码策略');
        showNotification('密码策略已重置为默认设置', 'info');
      }
    }

    function savePasswordPolicy() {
      console.log('保存密码策略');
      showNotification('密码策略保存成功', 'success');
    }

    // 登录安全设置功能
    function updateLoginPolicy() {
      console.log('更新登录策略');
      showNotification('登录策略已更新', 'success');
    }

    function updateSecurityOptions() {
      console.log('更新安全选项');
      showNotification('安全选项已更新', 'success');
    }

    function testLoginSettings() {
      console.log('测试登录设置');
      showNotification('登录设置测试功能开发中', 'info');
    }

    // IP访问控制功能
    function updateIPPolicy() {
      console.log('更新IP策略');
      showNotification('IP访问策略已更新', 'success');
    }

    function showAddIPModal() {
      console.log('显示添加IP规则模态框');
      showNotification('添加IP规则功能开发中', 'info');
    }

    function editIPRule(ruleId) {
      console.log('编辑IP规则:', ruleId);
      showNotification('IP规则编辑功能开发中', 'info');
    }

    function deleteIPRule(ruleId) {
      if (confirm('确定要删除这个IP规则吗？')) {
        console.log('删除IP规则:', ruleId);
        showNotification('IP规则删除成功', 'success');
      }
    }

    function removeFromBlacklist(ruleId) {
      if (confirm('确定要从黑名单中移除这个IP吗？')) {
        console.log('移除黑名单IP:', ruleId);
        showNotification('IP已从黑名单移除', 'success');
      }
    }

    function permanentBan(ruleId) {
      if (confirm('确定要永久封禁这个IP吗？')) {
        console.log('永久封禁IP:', ruleId);
        showNotification('IP已永久封禁', 'warning');
      }
    }

    // 验证码配置功能
    function updateCaptchaSettings() {
      console.log('更新验证码设置');
      showNotification('验证码设置已更新', 'success');
    }

    function testCaptcha() {
      console.log('预览验证码');
      refreshCaptchaPreview();
      showNotification('验证码预览已刷新', 'info');
    }

    function refreshCaptchaPreview() {
      console.log('刷新验证码预览');
      // 这里可以更新验证码图片
      const captchaImg = document.querySelector('.captcha-image img');
      if (captchaImg) {
        const randomCode = Math.random().toString(36).substring(2, 6).toUpperCase();
        captchaImg.src = `https://via.placeholder.com/120x40/4F46E5/FFFFFF?text=${randomCode}`;
      }
    }

    // 会话管理功能
    function updateSessionPolicy() {
      console.log('更新会话策略');
      showNotification('会话策略已更新', 'success');
    }

    function updateSessionTimeout(value) {
      document.getElementById('sessionTimeoutValue').textContent = value + '分钟';
      console.log('更新会话超时:', value);
    }

    function forceLogoutAll() {
      if (confirm('确定要强制所有用户下线吗？这将影响所有在线用户！')) {
        console.log('强制全部用户下线');
        showNotification('所有用户已强制下线', 'warning');
      }
    }

    function viewSessionDetail(sessionId) {
      console.log('查看会话详情:', sessionId);
      showNotification('会话详情功能开发中', 'info');
    }

    function forceLogout(sessionId) {
      if (confirm('确定要强制该用户下线吗？')) {
        console.log('强制用户下线:', sessionId);
        showNotification('用户已强制下线', 'warning');
      }
    }

    function verifySession(sessionId) {
      console.log('验证可疑会话:', sessionId);
      showNotification('会话验证功能开发中', 'info');
    }

    // 安全审计日志功能
    function refreshAuditLog() {
      console.log('刷新审计日志');
      showNotification('审计日志已刷新', 'success');
    }

    function exportAuditLog() {
      console.log('导出审计日志');
      showNotification('审计日志导出功能开发中', 'info');
    }

    function filterAuditLog() {
      const level = document.getElementById('logLevel').value;
      const category = document.getElementById('logCategory').value;
      const date = document.getElementById('logDate').value;
      console.log('筛选审计日志:', level, category, date);
      showNotification('日志筛选完成', 'success');
    }

    function searchAuditLog() {
      const keyword = document.getElementById('logSearch').value;
      console.log('搜索审计日志:', keyword);
      showNotification('日志搜索完成', 'success');
    }

    function viewLogDetail(logId) {
      console.log('查看日志详情:', logId);
      showNotification('日志详情功能开发中', 'info');
    }

    function blockIP(ip) {
      if (confirm(`确定要封禁IP ${ip} 吗？`)) {
        console.log('封禁IP:', ip);
        showNotification(`IP ${ip} 已加入黑名单`, 'warning');
      }
    }

    function requireVerification(logId) {
      console.log('要求用户验证:', logId);
      showNotification('已要求用户进行身份验证', 'info');
    }

    function emergencyBlock(ip) {
      if (confirm(`检测到严重安全威胁，确定要紧急阻断IP ${ip} 吗？`)) {
        console.log('紧急阻断IP:', ip);
        showNotification(`IP ${ip} 已紧急阻断`, 'danger');
      }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : type === 'danger' ? 'times-circle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);
      setTimeout(() => notification.classList.add('show'), 100);

      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
