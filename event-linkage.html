<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>事件与联动中心 - 广州农行智慧楼宇</title>
  
  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- 引入主样式 -->
  <link rel="stylesheet" href="assets/css/main.css">
  
  <style>
    .event-filters {
      background: white;
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      margin-bottom: var(--spacing-lg);
      border: 1px solid var(--gray-200);
    }
    
    .filter-row {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: var(--spacing-md);
      align-items: end;
    }
    
    .event-table-container {
      background: white;
      border-radius: var(--radius-lg);
      border: 1px solid var(--gray-200);
      overflow: hidden;
    }
    
    .event-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-md);
      border-bottom: 1px solid var(--gray-100);
      transition: background 0.3s ease;
    }
    
    .event-item:hover {
      background: var(--gray-50);
    }
    
    .event-item:last-child {
      border-bottom: none;
    }
    
    .event-priority {
      width: 4px;
      height: 40px;
      border-radius: 2px;
      margin-right: var(--spacing-md);
    }
    
    .event-priority.high { background: var(--danger-color); }
    .event-priority.medium { background: var(--warning-color); }
    .event-priority.low { background: var(--success-color); }
    
    .event-icon {
      width: 40px;
      height: 40px;
      border-radius: var(--radius-md);
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: var(--spacing-md);
      color: white;
      font-size: 16px;
    }
    
    .event-content {
      flex: 1;
      min-width: 0;
    }
    
    .event-title {
      font-size: 14px;
      font-weight: 600;
      color: var(--gray-900);
      margin-bottom: 4px;
    }
    
    .event-desc {
      font-size: 12px;
      color: var(--gray-600);
      margin-bottom: 4px;
    }
    
    .event-meta {
      display: flex;
      align-items: center;
      gap: var(--spacing-md);
      font-size: 11px;
      color: var(--gray-500);
    }
    
    .event-actions {
      display: flex;
      gap: var(--spacing-sm);
    }
    
    .linkage-rules {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: var(--spacing-md);
    }
    
    .rule-card {
      background: white;
      border: 1px solid var(--gray-200);
      border-radius: var(--radius-lg);
      padding: var(--spacing-lg);
      transition: all 0.3s ease;
    }
    
    .rule-card:hover {
      box-shadow: var(--shadow-md);
    }
    
    .rule-header {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: var(--spacing-md);
    }
    
    .rule-title {
      font-size: 16px;
      font-weight: 600;
      color: var(--gray-900);
    }
    
    .rule-status {
      padding: 4px 8px;
      border-radius: 12px;
      font-size: 12px;
      font-weight: 500;
    }
    
    .rule-status.active {
      background: rgba(16, 185, 129, 0.1);
      color: var(--success-color);
    }
    
    .rule-status.inactive {
      background: rgba(107, 114, 128, 0.1);
      color: var(--gray-500);
    }
    
    .rule-condition {
      background: var(--gray-50);
      border-radius: var(--radius-md);
      padding: var(--spacing-sm);
      margin-bottom: var(--spacing-sm);
      font-size: 12px;
      color: var(--gray-700);
    }
    
    .rule-actions-list {
      list-style: none;
      padding: 0;
      margin: 0;
    }
    
    .rule-actions-list li {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      padding: var(--spacing-xs) 0;
      font-size: 12px;
      color: var(--gray-600);
    }
    
    .rule-actions-list li i {
      width: 16px;
      color: var(--primary-color);
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div id="sidebarContainer"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部导航 -->
      <div id="headerContainer"></div>

      <!-- 页面内容 -->
      <div class="page-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">事件与联动中心</h1>
          <p class="page-description">实时报警事件查看、历史事件查询与导出、联动规则配置</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid mb-lg">
          <div class="stat-card danger">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">5</div>
                <div class="stat-label">活跃告警</div>
              </div>
              <div class="stat-icon danger">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">23</div>
                <div class="stat-label">今日事件</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-calendar-day"></i>
              </div>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">18</div>
                <div class="stat-label">已处理</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-check-circle"></i>
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">12</div>
                <div class="stat-label">联动规则</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-link"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 事件筛选 -->
        <div class="event-filters">
          <div class="filter-row">
            <div class="form-group mb-0">
              <label class="form-label">事件类型</label>
              <select class="form-control form-select">
                <option>全部类型</option>
                <option>设备故障</option>
                <option>入侵告警</option>
                <option>火灾报警</option>
                <option>门禁异常</option>
                <option>视频丢失</option>
              </select>
            </div>
            <div class="form-group mb-0">
              <label class="form-label">优先级</label>
              <select class="form-control form-select">
                <option>全部优先级</option>
                <option>高</option>
                <option>中</option>
                <option>低</option>
              </select>
            </div>
            <div class="form-group mb-0">
              <label class="form-label">状态</label>
              <select class="form-control form-select">
                <option>全部状态</option>
                <option>待处理</option>
                <option>处理中</option>
                <option>已处理</option>
                <option>已忽略</option>
              </select>
            </div>
            <div class="form-group mb-0">
              <label class="form-label">时间范围</label>
              <select class="form-control form-select">
                <option>今天</option>
                <option>最近3天</option>
                <option>最近7天</option>
                <option>最近30天</option>
                <option>自定义</option>
              </select>
            </div>
            <div class="form-group mb-0">
              <label class="form-label">&nbsp;</label>
              <div class="d-flex gap-sm">
                <button class="btn btn-primary">
                  <i class="fas fa-search"></i>
                  查询
                </button>
                <button class="btn btn-secondary">
                  <i class="fas fa-redo"></i>
                  重置
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 事件列表 -->
        <div class="dashboard-grid">
          <div class="card" style="grid-column: 1 / -1;">
            <div class="card-header">
              <h3 class="card-title">实时事件列表</h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-sm btn-success">
                  <i class="fas fa-download"></i>
                  导出
                </button>
                <button class="btn btn-sm btn-primary">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
              </div>
            </div>
            <div class="card-body p-0">
              <div class="event-table-container">
                <div class="event-item">
                  <div class="event-priority high"></div>
                  <div class="event-icon" style="background: #ef4444;">
                    <i class="fas fa-fire"></i>
                  </div>
                  <div class="event-content">
                    <div class="event-title">火灾报警</div>
                    <div class="event-desc">1号楼3层西侧烟感器触发火灾报警</div>
                    <div class="event-meta">
                      <span><i class="fas fa-map-marker-alt"></i> 1号楼3层</span>
                      <span><i class="fas fa-clock"></i> 2024-01-15 14:30:25</span>
                      <span><i class="fas fa-user"></i> 系统自动</span>
                    </div>
                  </div>
                  <div class="event-actions">
                    <button class="btn btn-sm btn-primary">处理</button>
                    <button class="btn btn-sm btn-secondary">详情</button>
                  </div>
                </div>

                <div class="event-item">
                  <div class="event-priority high"></div>
                  <div class="event-icon" style="background: #f59e0b;">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="event-content">
                    <div class="event-title">入侵告警</div>
                    <div class="event-desc">2号楼东门检测到非法入侵行为</div>
                    <div class="event-meta">
                      <span><i class="fas fa-map-marker-alt"></i> 2号楼东门</span>
                      <span><i class="fas fa-clock"></i> 2024-01-15 14:25:12</span>
                      <span><i class="fas fa-user"></i> 智能分析</span>
                    </div>
                  </div>
                  <div class="event-actions">
                    <button class="btn btn-sm btn-primary">处理</button>
                    <button class="btn btn-sm btn-secondary">详情</button>
                  </div>
                </div>

                <div class="event-item">
                  <div class="event-priority medium"></div>
                  <div class="event-icon" style="background: #3b82f6;">
                    <i class="fas fa-door-open"></i>
                  </div>
                  <div class="event-content">
                    <div class="event-title">门禁异常</div>
                    <div class="event-desc">3号楼门禁读卡器通信中断</div>
                    <div class="event-meta">
                      <span><i class="fas fa-map-marker-alt"></i> 3号楼大厅</span>
                      <span><i class="fas fa-clock"></i> 2024-01-15 14:20:45</span>
                      <span><i class="fas fa-user"></i> 设备监控</span>
                    </div>
                  </div>
                  <div class="event-actions">
                    <button class="btn btn-sm btn-primary">处理</button>
                    <button class="btn btn-sm btn-secondary">详情</button>
                  </div>
                </div>

                <div class="event-item">
                  <div class="event-priority low"></div>
                  <div class="event-icon" style="background: #10b981;">
                    <i class="fas fa-check-circle"></i>
                  </div>
                  <div class="event-content">
                    <div class="event-title">设备恢复</div>
                    <div class="event-desc">1号楼电梯故障已修复，恢复正常运行</div>
                    <div class="event-meta">
                      <span><i class="fas fa-map-marker-alt"></i> 1号楼电梯</span>
                      <span><i class="fas fa-clock"></i> 2024-01-15 14:15:30</span>
                      <span><i class="fas fa-user"></i> 维修人员</span>
                    </div>
                  </div>
                  <div class="event-actions">
                    <button class="btn btn-sm btn-success">已处理</button>
                    <button class="btn btn-sm btn-secondary">详情</button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 联动规则配置 -->
        <div class="card mt-lg">
          <div class="card-header">
            <h3 class="card-title">联动规则配置</h3>
            <button class="btn btn-primary">
              <i class="fas fa-plus"></i>
              新增规则
            </button>
          </div>
          <div class="card-body">
            <div class="linkage-rules">
              <div class="rule-card">
                <div class="rule-header">
                  <div class="rule-title">火灾报警联动</div>
                  <div class="rule-status active">启用</div>
                </div>
                <div class="rule-condition">
                  <strong>触发条件：</strong>烟感器或温感器触发火灾报警
                </div>
                <ul class="rule-actions-list">
                  <li><i class="fas fa-video"></i> 自动调取现场视频</li>
                  <li><i class="fas fa-camera"></i> 触发现场抓拍</li>
                  <li><i class="fas fa-door-open"></i> 自动开启疏散通道</li>
                  <li><i class="fas fa-broadcast-tower"></i> 发送紧急广播</li>
                </ul>
                <div class="d-flex gap-sm mt-md">
                  <button class="btn btn-sm btn-primary">编辑</button>
                  <button class="btn btn-sm btn-secondary">测试</button>
                </div>
              </div>

              <div class="rule-card">
                <div class="rule-header">
                  <div class="rule-title">入侵检测联动</div>
                  <div class="rule-status active">启用</div>
                </div>
                <div class="rule-condition">
                  <strong>触发条件：</strong>智能分析检测到入侵行为
                </div>
                <ul class="rule-actions-list">
                  <li><i class="fas fa-video"></i> 自动跟踪录像</li>
                  <li><i class="fas fa-camera"></i> 连续抓拍</li>
                  <li><i class="fas fa-bell"></i> 发送告警通知</li>
                  <li><i class="fas fa-phone"></i> 自动拨打报警电话</li>
                </ul>
                <div class="d-flex gap-sm mt-md">
                  <button class="btn btn-sm btn-primary">编辑</button>
                  <button class="btn btn-sm btn-secondary">测试</button>
                </div>
              </div>

              <div class="rule-card">
                <div class="rule-header">
                  <div class="rule-title">设备故障联动</div>
                  <div class="rule-status inactive">禁用</div>
                </div>
                <div class="rule-condition">
                  <strong>触发条件：</strong>关键设备离线或故障
                </div>
                <ul class="rule-actions-list">
                  <li><i class="fas fa-tools"></i> 自动创建维修工单</li>
                  <li><i class="fas fa-user"></i> 通知维修人员</li>
                  <li><i class="fas fa-clipboard-list"></i> 记录故障日志</li>
                  <li><i class="fas fa-backup"></i> 启动备用设备</li>
                </ul>
                <div class="d-flex gap-sm mt-md">
                  <button class="btn btn-sm btn-primary">编辑</button>
                  <button class="btn btn-sm btn-secondary">启用</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入主JavaScript -->
  <script src="assets/js/main.js"></script>
  
  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载组件
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      
      // 更新面包屑
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('事件与联动中心');
        }
      }, 100);
    });

    // 加载组件
    function loadComponent(containerId, componentPath) {
      fetch(componentPath)
        .then(response => response.text())
        .then(html => {
          document.getElementById(containerId).innerHTML = html;
        })
        .catch(error => {
          console.error('Error loading component:', error);
        });
    }
  </script>
</body>
</html>
