<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>地图监控中心 - 广州农行智慧楼宇</title>

  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

  <!-- 引入主样式 -->
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div id="sidebarContainer"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部导航 -->
      <div id="headerContainer"></div>

      <!-- 页面内容 -->
      <div class="page-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">地图监控</h1>
          <p class="page-description">实时地图展示，视频/告警/门禁/人员等资源上图，支持GPS轨迹回放</p>
        </div>

        <!-- 地图区域 -->
        <div class="card mb-lg">
          <div class="card-header">
            <h3 class="card-title">实时监控地图</h3>
            <div class="d-flex gap-sm">
              <button class="btn btn-sm btn-secondary" id="refreshMapBtn">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
              <button class="btn btn-sm btn-primary" id="fullscreenMapBtn">
                <i class="fas fa-expand"></i>
                全屏
              </button>
            </div>
          </div>
          <div class="card-body p-0">
            <div class="map-container" id="mapContainer">
              <!-- 地图图层控制 -->
              <div class="map-layers">
                <h4 style="margin: 0 0 12px 0; font-size: 14px; font-weight: 600;">图层控制</h4>
                <div class="layer-item">
                  <input type="checkbox" class="layer-checkbox" id="videoLayer" checked>
                  <label class="layer-label" for="videoLayer">视频监控</label>
                  <span class="layer-count">24</span>
                </div>
                <div class="layer-item">
                  <input type="checkbox" class="layer-checkbox" id="accessLayer" checked>
                  <label class="layer-label" for="accessLayer">门禁设备</label>
                  <span class="layer-count">18</span>
                </div>
                <div class="layer-item">
                  <input type="checkbox" class="layer-checkbox" id="alarmLayer" checked>
                  <label class="layer-label" for="alarmLayer">告警事件</label>
                  <span class="layer-count">5</span>
                </div>
                <div class="layer-item">
                  <input type="checkbox" class="layer-checkbox" id="personnelLayer">
                  <label class="layer-label" for="personnelLayer">人员位置</label>
                  <span class="layer-count">12</span>
                </div>
                <div class="layer-item">
                  <input type="checkbox" class="layer-checkbox" id="vehicleLayer">
                  <label class="layer-label" for="vehicleLayer">车辆轨迹</label>
                  <span class="layer-count">8</span>
                </div>
              </div>

              <!-- 地图控制按钮 -->
              <div class="map-controls">
                <button class="map-control-btn" title="放大" id="zoomInBtn">
                  <i class="fas fa-plus"></i>
                </button>
                <button class="map-control-btn" title="缩小" id="zoomOutBtn">
                  <i class="fas fa-minus"></i>
                </button>
                <button class="map-control-btn" title="定位" id="locateBtn">
                  <i class="fas fa-crosshairs"></i>
                </button>
                <button class="map-control-btn" title="测距" id="measureBtn">
                  <i class="fas fa-ruler"></i>
                </button>
              </div>

              <!-- 地图占位符 -->
              <div class="map-placeholder">
                <div style="text-align: center;">
                  <i class="fas fa-map-marked-alt" style="font-size: 48px; margin-bottom: 16px; opacity: 0.7;"></i>
                  <div>智慧楼宇监控地图</div>
                  <div style="font-size: 14px; opacity: 0.8; margin-top: 8px;">
                    实时显示设备状态、告警信息、人员位置等
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 功能区域 -->
        <div class="dashboard-grid">
          <!-- 实时统计 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">实时统计</h3>
            </div>
            <div class="card-body">
              <div class="stats-grid" style="grid-template-columns: 1fr 1fr; gap: var(--spacing-md); margin-bottom: 0;">
                <div class="stat-card info">
                  <div class="stat-header">
                    <div class="stat-content">
                      <div class="stat-value">24</div>
                      <div class="stat-label">视频设备</div>
                    </div>
                    <div class="stat-icon info">
                      <i class="fas fa-video"></i>
                    </div>
                  </div>
                </div>
                <div class="stat-card success">
                  <div class="stat-header">
                    <div class="stat-content">
                      <div class="stat-value">18</div>
                      <div class="stat-label">门禁设备</div>
                    </div>
                    <div class="stat-icon success">
                      <i class="fas fa-door-open"></i>
                    </div>
                  </div>
                </div>
                <div class="stat-card warning">
                  <div class="stat-header">
                    <div class="stat-content">
                      <div class="stat-value">5</div>
                      <div class="stat-label">活跃告警</div>
                    </div>
                    <div class="stat-icon warning">
                      <i class="fas fa-exclamation-triangle"></i>
                    </div>
                  </div>
                </div>
                <div class="stat-card primary">
                  <div class="stat-header">
                    <div class="stat-content">
                      <div class="stat-value">12</div>
                      <div class="stat-label">在线人员</div>
                    </div>
                    <div class="stat-icon primary">
                      <i class="fas fa-users"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 轨迹回放 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">GPS轨迹回放</h3>
            </div>
            <div class="card-body">
              <div class="form-group">
                <label class="form-label">选择目标</label>
                <select class="form-control form-select">
                  <option>请选择回放目标</option>
                  <option>保安001 - 张三</option>
                  <option>保安002 - 李四</option>
                  <option>巡逻车001</option>
                  <option>巡逻车002</option>
                </select>
              </div>
              <div class="form-row">
                <div class="form-group">
                  <label class="form-label">开始时间</label>
                  <input type="datetime-local" class="form-control" value="2024-01-15T09:00">
                </div>
                <div class="form-group">
                  <label class="form-label">结束时间</label>
                  <input type="datetime-local" class="form-control" value="2024-01-15T17:00">
                </div>
              </div>
              <div class="d-flex gap-sm">
                <button class="btn btn-primary">
                  <i class="fas fa-play"></i>
                  开始回放
                </button>
                <button class="btn btn-secondary">
                  <i class="fas fa-download"></i>
                  导出轨迹
                </button>
              </div>
            </div>
          </div>

          <!-- 快捷操作 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">快捷操作</h3>
            </div>
            <div class="card-body">
              <div class="quick-actions" style="grid-template-columns: 1fr 1fr;">
                <button class="btn btn-primary mb-sm">
                  <i class="fas fa-video"></i>
                  视频联动
                </button>
                <button class="btn btn-success mb-sm">
                  <i class="fas fa-door-open"></i>
                  门禁控制
                </button>
                <button class="btn btn-warning mb-sm">
                  <i class="fas fa-exclamation-triangle"></i>
                  告警处理
                </button>
                <button class="btn btn-info mb-sm">
                  <i class="fas fa-broadcast-tower"></i>
                  广播通知
                </button>
              </div>
            </div>
          </div>

          <!-- 最新事件 -->
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">最新事件</h3>
              <button class="btn btn-sm btn-secondary">
                <i class="fas fa-sync-alt"></i>
              </button>
            </div>
            <div class="card-body p-0">
              <div class="recent-list" id="recentEvents">
                <div class="recent-item">
                  <div class="recent-icon" style="background: #ef4444">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">入侵告警</div>
                    <div class="recent-desc">1号楼东门检测到异常入侵</div>
                  </div>
                  <div class="recent-time">2分钟前</div>
                </div>
                <div class="recent-item">
                  <div class="recent-icon" style="background: #f59e0b">
                    <i class="fas fa-door-open"></i>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">门禁异常</div>
                    <div class="recent-desc">2号楼门禁读卡器离线</div>
                  </div>
                  <div class="recent-time">15分钟前</div>
                </div>
                <div class="recent-item">
                  <div class="recent-icon" style="background: #10b981">
                    <i class="fas fa-user-check"></i>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">人员进入</div>
                    <div class="recent-desc">张三通过人脸识别进入大厅</div>
                  </div>
                  <div class="recent-time">30分钟前</div>
                </div>
                <div class="recent-item">
                  <div class="recent-icon" style="background: #3b82f6">
                    <i class="fas fa-car"></i>
                  </div>
                  <div class="recent-content">
                    <div class="recent-title">车辆进入</div>
                    <div class="recent-desc">京A12345进入地下停车场</div>
                  </div>
                  <div class="recent-time">45分钟前</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入主JavaScript -->
  <script src="assets/js/main.js"></script>
  
  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载组件
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');

      // 初始化地图功能
      initMapControls();

      // 设置当前页面导航激活状态
      setTimeout(() => {
        setActiveNavigation();

        // 更新面包屑
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('地图监控');
        }
      }, 100);
    });

    // 加载组件
    function loadComponent(containerId, componentPath) {
      fetch(componentPath)
        .then(response => response.text())
        .then(html => {
          document.getElementById(containerId).innerHTML = html;
        })
        .catch(error => {
          console.error('Error loading component:', error);
        });
    }

    // 设置当前页面导航激活状态
    function setActiveNavigation() {
      // 移除所有导航项的激活状态
      document.querySelectorAll('.nav-item').forEach(item => {
        item.classList.remove('active');
      });

      // 设置地图监控页面为激活状态
      const mapNavItem = document.querySelector('.nav-item[data-page="map-monitoring"]');
      if (mapNavItem) {
        mapNavItem.classList.add('active');
      }
    }

    // 初始化地图控制
    function initMapControls() {
      // 图层控制
      document.querySelectorAll('.layer-checkbox').forEach(checkbox => {
        checkbox.addEventListener('change', function() {
          const layerId = this.id;
          const isChecked = this.checked;
          console.log(`图层 ${layerId} ${isChecked ? '显示' : '隐藏'}`);
          
          // 这里可以添加实际的地图图层控制逻辑
          if (window.app) {
            window.app.showNotification(
              `${this.nextElementSibling.textContent} ${isChecked ? '已显示' : '已隐藏'}`,
              'info'
            );
          }
        });
      });

      // 地图控制按钮
      document.getElementById('zoomInBtn')?.addEventListener('click', () => {
        console.log('地图放大');
        if (window.app) {
          window.app.showNotification('地图已放大', 'info');
        }
      });

      document.getElementById('zoomOutBtn')?.addEventListener('click', () => {
        console.log('地图缩小');
        if (window.app) {
          window.app.showNotification('地图已缩小', 'info');
        }
      });

      document.getElementById('locateBtn')?.addEventListener('click', () => {
        console.log('定位到当前位置');
        if (window.app) {
          window.app.showNotification('已定位到当前位置', 'success');
        }
      });

      document.getElementById('measureBtn')?.addEventListener('click', () => {
        console.log('开启测距工具');
        if (window.app) {
          window.app.showNotification('测距工具已开启', 'info');
        }
      });

      // 刷新地图
      document.getElementById('refreshMapBtn')?.addEventListener('click', () => {
        console.log('刷新地图数据');
        if (window.app) {
          window.app.showNotification('地图数据已刷新', 'success');
        }
      });

      // 全屏地图
      document.getElementById('fullscreenMapBtn')?.addEventListener('click', () => {
        const mapContainer = document.getElementById('mapContainer');
        if (mapContainer.requestFullscreen) {
          mapContainer.requestFullscreen();
        }
      });
    }


  </script>
</body>
</html>
