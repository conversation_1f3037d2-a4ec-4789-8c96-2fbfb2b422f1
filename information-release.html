<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>信息发布系统 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">信息发布系统</h1>
          <p class="page-description">素材管理、节目编辑器与排班、播放计划管理、信息终端远程控制</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">12</div>
                <div class="stat-label">发布终端</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-tv"></i>
              </div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">素材文件</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-file-image"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">8</div>
                <div class="stat-label">播放节目</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-play-circle"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">3</div>
                <div class="stat-label">待审核</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 素材管理和节目编辑 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-folder-open text-success"></i>
                素材管理
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshMaterials()">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
                <button class="btn btn-success btn-sm" onclick="showUploadModal()">
                  <i class="fas fa-upload"></i>
                  上传素材
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 素材分类筛选 -->
              <div class="material-filters mb-md">
                <div class="filter-tabs">
                  <button class="filter-tab active" onclick="filterMaterials('all')">
                    <i class="fas fa-th"></i>
                    全部 (156)
                  </button>
                  <button class="filter-tab" onclick="filterMaterials('image')">
                    <i class="fas fa-image"></i>
                    图片 (89)
                  </button>
                  <button class="filter-tab" onclick="filterMaterials('video')">
                    <i class="fas fa-video"></i>
                    视频 (45)
                  </button>
                  <button class="filter-tab" onclick="filterMaterials('text')">
                    <i class="fas fa-font"></i>
                    文本 (22)
                  </button>
                </div>
                <div class="search-box">
                  <input type="text" class="form-control" placeholder="搜索素材..." id="materialSearch">
                  <button class="btn btn-outline-primary" onclick="searchMaterials()">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>

              <!-- 素材网格 -->
              <div class="material-grid">
                <div class="material-item image" data-type="image">
                  <div class="material-preview">
                    <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=400&h=300&fit=crop&crop=center" alt="企业宣传">
                    <div class="material-overlay">
                      <button class="btn btn-sm btn-primary" onclick="previewMaterial('IMG_001')">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-success" onclick="addToProgram('IMG_001')">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-danger" onclick="deleteMaterial('IMG_001')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="material-info">
                    <div class="material-name">企业宣传图</div>
                    <div class="material-meta">
                      <span class="material-type">JPG</span>
                      <span class="material-size">2.3MB</span>
                      <span class="material-date">2024-01-15</span>
                    </div>
                  </div>
                </div>

                <div class="material-item video" data-type="video">
                  <div class="material-preview">
                    <div class="video-thumbnail">
                      <img src="https://images.unsplash.com/photo-1581092160562-40aa08e78837?w=400&h=300&fit=crop&crop=center" alt="安全培训">
                      <div class="video-duration">03:45</div>
                      <div class="video-play-icon">
                        <i class="fas fa-play"></i>
                      </div>
                    </div>
                    <div class="material-overlay">
                      <button class="btn btn-sm btn-primary" onclick="previewMaterial('VID_001')">
                        <i class="fas fa-play"></i>
                      </button>
                      <button class="btn btn-sm btn-success" onclick="addToProgram('VID_001')">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-danger" onclick="deleteMaterial('VID_001')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="material-info">
                    <div class="material-name">安全培训视频</div>
                    <div class="material-meta">
                      <span class="material-type">MP4</span>
                      <span class="material-size">45.2MB</span>
                      <span class="material-date">2024-01-12</span>
                    </div>
                  </div>
                </div>

                <div class="material-item text" data-type="text">
                  <div class="material-preview">
                    <div class="text-preview">
                      <div class="text-content">
                        <h4>重要通知</h4>
                        <p>关于春节放假安排的通知...</p>
                      </div>
                    </div>
                    <div class="material-overlay">
                      <button class="btn btn-sm btn-primary" onclick="previewMaterial('TXT_001')">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-success" onclick="addToProgram('TXT_001')">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" onclick="editMaterial('TXT_001')">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </div>
                  <div class="material-info">
                    <div class="material-name">春节放假通知</div>
                    <div class="material-meta">
                      <span class="material-type">文本</span>
                      <span class="material-size">1.2KB</span>
                      <span class="material-date">2024-01-10</span>
                    </div>
                  </div>
                </div>

                <div class="material-item image" data-type="image">
                  <div class="material-preview">
                    <img src="https://images.unsplash.com/photo-1511795409834-ef04bbd61622?w=400&h=300&fit=crop&crop=center" alt="活动海报">
                    <div class="material-overlay">
                      <button class="btn btn-sm btn-primary" onclick="previewMaterial('IMG_002')">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-success" onclick="addToProgram('IMG_002')">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-danger" onclick="deleteMaterial('IMG_002')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="material-info">
                    <div class="material-name">年会活动海报</div>
                    <div class="material-meta">
                      <span class="material-type">PNG</span>
                      <span class="material-size">3.8MB</span>
                      <span class="material-date">2024-01-08</span>
                    </div>
                  </div>
                </div>

                <div class="material-item video" data-type="video">
                  <div class="material-preview">
                    <div class="video-thumbnail">
                      <img src="https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=300&fit=crop&crop=center" alt="消防演练">
                      <div class="video-duration">08:20</div>
                      <div class="video-play-icon">
                        <i class="fas fa-play"></i>
                      </div>
                    </div>
                    <div class="material-overlay">
                      <button class="btn btn-sm btn-primary" onclick="previewMaterial('VID_002')">
                        <i class="fas fa-play"></i>
                      </button>
                      <button class="btn btn-sm btn-success" onclick="addToProgram('VID_002')">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-danger" onclick="deleteMaterial('VID_002')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="material-info">
                    <div class="material-name">消防演练记录</div>
                    <div class="material-meta">
                      <span class="material-type">MP4</span>
                      <span class="material-size">78.5MB</span>
                      <span class="material-date">2024-01-05</span>
                    </div>
                  </div>
                </div>

                <div class="material-item text" data-type="text">
                  <div class="material-preview">
                    <div class="text-preview">
                      <div class="text-content">
                        <h4>温馨提示</h4>
                        <p>请注意办公区域卫生...</p>
                      </div>
                    </div>
                    <div class="material-overlay">
                      <button class="btn btn-sm btn-primary" onclick="previewMaterial('TXT_002')">
                        <i class="fas fa-eye"></i>
                      </button>
                      <button class="btn btn-sm btn-success" onclick="addToProgram('TXT_002')">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-warning" onclick="editMaterial('TXT_002')">
                        <i class="fas fa-edit"></i>
                      </button>
                    </div>
                  </div>
                  <div class="material-info">
                    <div class="material-name">卫生提示</div>
                    <div class="material-meta">
                      <span class="material-type">文本</span>
                      <span class="material-size">0.8KB</span>
                      <span class="material-date">2024-01-03</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-edit text-info"></i>
                节目编辑器
              </h3>
              <button class="btn btn-primary btn-sm" onclick="showCreateProgramModal()">
                <i class="fas fa-plus"></i>
                新建节目
              </button>
            </div>
            <div class="card-body">
              <div class="program-editor">
                <!-- 当前编辑的节目 -->
                <div class="current-program mb-md">
                  <div class="program-header">
                    <div class="program-info">
                      <h5>正在编辑：企业文化宣传节目</h5>
                      <div class="program-meta">
                        <span class="program-duration">总时长: 05:30</span>
                        <span class="program-status">状态: 编辑中</span>
                      </div>
                    </div>
                    <div class="program-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="previewProgram()">
                        <i class="fas fa-play"></i>
                        预览
                      </button>
                      <button class="btn btn-sm btn-success" onclick="saveProgram()">
                        <i class="fas fa-save"></i>
                        保存
                      </button>
                      <button class="btn btn-sm btn-warning" onclick="submitForReview()">
                        <i class="fas fa-paper-plane"></i>
                        提交审核
                      </button>
                    </div>
                  </div>
                </div>

                <!-- 时间轴编辑器 -->
                <div class="timeline-editor">
                  <div class="timeline-header">
                    <h6>时间轴</h6>
                    <div class="timeline-controls">
                      <button class="btn btn-xs btn-outline-primary" onclick="zoomTimeline('in')">
                        <i class="fas fa-search-plus"></i>
                      </button>
                      <button class="btn btn-xs btn-outline-primary" onclick="zoomTimeline('out')">
                        <i class="fas fa-search-minus"></i>
                      </button>
                      <button class="btn btn-xs btn-outline-danger" onclick="clearTimeline()">
                        <i class="fas fa-trash"></i>
                        清空
                      </button>
                    </div>
                  </div>
                  <div class="timeline-container">
                    <div class="timeline-ruler">
                      <div class="time-marker">0:00</div>
                      <div class="time-marker">1:00</div>
                      <div class="time-marker">2:00</div>
                      <div class="time-marker">3:00</div>
                      <div class="time-marker">4:00</div>
                      <div class="time-marker">5:00</div>
                    </div>
                    <div class="timeline-tracks">
                      <div class="timeline-track">
                        <div class="track-label">视频轨道</div>
                        <div class="track-content">
                          <div class="timeline-item video" style="left: 0%; width: 40%;" onclick="selectTimelineItem(this)">
                            <div class="item-content">
                              <i class="fas fa-video"></i>
                              <span>企业宣传片</span>
                            </div>
                            <div class="item-duration">2:15</div>
                          </div>
                          <div class="timeline-item image" style="left: 45%; width: 30%;" onclick="selectTimelineItem(this)">
                            <div class="item-content">
                              <i class="fas fa-image"></i>
                              <span>活动海报</span>
                            </div>
                            <div class="item-duration">1:30</div>
                          </div>
                        </div>
                      </div>
                      <div class="timeline-track">
                        <div class="track-label">文本轨道</div>
                        <div class="track-content">
                          <div class="timeline-item text" style="left: 20%; width: 25%;" onclick="selectTimelineItem(this)">
                            <div class="item-content">
                              <i class="fas fa-font"></i>
                              <span>重要通知</span>
                            </div>
                            <div class="item-duration">1:45</div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <!-- <div class="timeline-playhead" style="left: 35%;"></div> -->
                  </div>
                </div>

                <!-- 属性面板 -->
                <div class="property-panel">
                  <h6>属性设置</h6>
                  <div class="property-content">
                    <div class="property-group">
                      <label>播放时长 (秒)</label>
                      <input type="number" class="form-control form-control-sm" value="90" min="1">
                    </div>
                    <div class="property-group">
                      <label>过渡效果</label>
                      <select class="form-control form-control-sm">
                        <option>无</option>
                        <option>淡入淡出</option>
                        <option>滑动</option>
                        <option>缩放</option>
                      </select>
                    </div>
                    <div class="property-group">
                      <label>位置调整</label>
                      <div class="position-controls">
                        <button class="btn btn-xs btn-outline-primary" onclick="moveItem('left')">
                          <i class="fas fa-arrow-left"></i>
                        </button>
                        <button class="btn btn-xs btn-outline-primary" onclick="moveItem('right')">
                          <i class="fas fa-arrow-right"></i>
                        </button>
                        <button class="btn btn-xs btn-outline-danger" onclick="deleteItem()">
                          <i class="fas fa-trash"></i>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 播放计划管理和终端设备管理 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-calendar-alt text-warning"></i>
                播放计划管理
              </h3>
              <button class="btn btn-primary btn-sm" onclick="showScheduleModal()">
                <i class="fas fa-plus"></i>
                新建计划
              </button>
            </div>
            <div class="card-body">
              <!-- 计划筛选 -->
              <div class="schedule-filters mb-md">
                <div class="filter-buttons">
                  <button class="btn btn-sm btn-outline-primary active" onclick="filterSchedules('all')">
                    全部计划
                  </button>
                  <button class="btn btn-sm btn-outline-success" onclick="filterSchedules('active')">
                    正在播放
                  </button>
                  <button class="btn btn-sm btn-outline-warning" onclick="filterSchedules('scheduled')">
                    已安排
                  </button>
                  <button class="btn btn-sm btn-outline-secondary" onclick="filterSchedules('completed')">
                    已完成
                  </button>
                </div>
                <div class="date-filter">
                  <input type="date" class="form-control form-control-sm" id="scheduleDate" value="2024-01-15">
                  <button class="btn btn-sm btn-outline-primary" onclick="filterByDate()">
                    <i class="fas fa-search"></i>
                  </button>
                </div>
              </div>

              <!-- 播放计划列表 -->
              <div class="schedule-list">
                <div class="schedule-item active">
                  <div class="schedule-header">
                    <div class="schedule-info">
                      <div class="schedule-title">企业文化宣传节目</div>
                      <div class="schedule-meta">
                        <span class="schedule-time">
                          <i class="fas fa-clock"></i>
                          09:00 - 17:00
                        </span>
                        <span class="schedule-terminals">
                          <i class="fas fa-tv"></i>
                          大厅显示屏、电梯显示屏
                        </span>
                      </div>
                    </div>
                    <div class="schedule-status">
                      <span class="status-badge success">正在播放</span>
                      <div class="schedule-progress">
                        <div class="progress-bar" style="width: 65%;"></div>
                      </div>
                      <div class="progress-text">65% 完成</div>
                    </div>
                  </div>
                  <div class="schedule-details">
                    <div class="schedule-content">
                      <div class="content-item">
                        <i class="fas fa-video"></i>
                        <span>企业宣传片 (2:15)</span>
                      </div>
                      <div class="content-item">
                        <i class="fas fa-image"></i>
                        <span>活动海报 (1:30)</span>
                      </div>
                      <div class="content-item">
                        <i class="fas fa-font"></i>
                        <span>重要通知 (1:45)</span>
                      </div>
                    </div>
                    <div class="schedule-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewScheduleDetail('SCH_001')">
                        <i class="fas fa-eye"></i>
                        详情
                      </button>
                      <button class="btn btn-sm btn-outline-warning" onclick="pauseSchedule('SCH_001')">
                        <i class="fas fa-pause"></i>
                        暂停
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="stopSchedule('SCH_001')">
                        <i class="fas fa-stop"></i>
                        停止
                      </button>
                    </div>
                  </div>
                </div>

                <div class="schedule-item scheduled">
                  <div class="schedule-header">
                    <div class="schedule-info">
                      <div class="schedule-title">安全培训宣传</div>
                      <div class="schedule-meta">
                        <span class="schedule-time">
                          <i class="fas fa-clock"></i>
                          14:00 - 16:00
                        </span>
                        <span class="schedule-terminals">
                          <i class="fas fa-tv"></i>
                          会议室显示屏
                        </span>
                      </div>
                    </div>
                    <div class="schedule-status">
                      <span class="status-badge warning">已安排</span>
                      <div class="schedule-countdown">
                        <i class="fas fa-hourglass-half"></i>
                        <span>2小时后开始</span>
                      </div>
                    </div>
                  </div>
                  <div class="schedule-details">
                    <div class="schedule-content">
                      <div class="content-item">
                        <i class="fas fa-video"></i>
                        <span>安全培训视频 (3:45)</span>
                      </div>
                      <div class="content-item">
                        <i class="fas fa-video"></i>
                        <span>消防演练记录 (8:20)</span>
                      </div>
                    </div>
                    <div class="schedule-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewScheduleDetail('SCH_002')">
                        <i class="fas fa-eye"></i>
                        详情
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="startScheduleNow('SCH_002')">
                        <i class="fas fa-play"></i>
                        立即播放
                      </button>
                      <button class="btn btn-sm btn-outline-secondary" onclick="editSchedule('SCH_002')">
                        <i class="fas fa-edit"></i>
                        编辑
                      </button>
                    </div>
                  </div>
                </div>

                <div class="schedule-item completed">
                  <div class="schedule-header">
                    <div class="schedule-info">
                      <div class="schedule-title">年会活动宣传</div>
                      <div class="schedule-meta">
                        <span class="schedule-time">
                          <i class="fas fa-clock"></i>
                          08:00 - 12:00
                        </span>
                        <span class="schedule-terminals">
                          <i class="fas fa-tv"></i>
                          全部终端
                        </span>
                      </div>
                    </div>
                    <div class="schedule-status">
                      <span class="status-badge secondary">已完成</span>
                      <div class="schedule-result">
                        <i class="fas fa-check-circle"></i>
                        <span>播放完成</span>
                      </div>
                    </div>
                  </div>
                  <div class="schedule-details">
                    <div class="schedule-content">
                      <div class="content-item">
                        <i class="fas fa-image"></i>
                        <span>年会活动海报 (4:00)</span>
                      </div>
                    </div>
                    <div class="schedule-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="viewScheduleDetail('SCH_003')">
                        <i class="fas fa-eye"></i>
                        详情
                      </button>
                      <button class="btn btn-sm btn-outline-info" onclick="viewPlaybackReport('SCH_003')">
                        <i class="fas fa-chart-bar"></i>
                        播放报告
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="repeatSchedule('SCH_003')">
                        <i class="fas fa-redo"></i>
                        重复播放
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-desktop text-primary"></i>
                终端设备管理
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="refreshTerminals()">
                <i class="fas fa-sync-alt"></i>
                刷新状态
              </button>
            </div>
            <div class="card-body">
              <!-- 终端状态概览 -->
              <div class="terminal-overview mb-md">
                <div class="terminal-stat online">
                  <div class="stat-icon">
                    <i class="fas fa-tv"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">9</div>
                    <div class="stat-label">在线</div>
                  </div>
                </div>
                <div class="terminal-stat offline">
                  <div class="stat-icon">
                    <i class="fas fa-tv"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">2</div>
                    <div class="stat-label">离线</div>
                  </div>
                </div>
                <div class="terminal-stat playing">
                  <div class="stat-icon">
                    <i class="fas fa-play-circle"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">7</div>
                    <div class="stat-label">播放中</div>
                  </div>
                </div>
                <div class="terminal-stat idle">
                  <div class="stat-icon">
                    <i class="fas fa-pause-circle"></i>
                  </div>
                  <div class="stat-info">
                    <div class="stat-value">2</div>
                    <div class="stat-label">空闲</div>
                  </div>
                </div>
              </div>

              <!-- 终端设备列表 -->
              <div class="terminal-list">
                <div class="terminal-item online playing">
                  <div class="terminal-info">
                    <div class="terminal-header">
                      <div class="terminal-name">大厅显示屏-01</div>
                      <div class="terminal-id">TERM_001</div>
                      <span class="status-badge success">在线</span>
                    </div>
                    <div class="terminal-details">
                      <div class="detail-row">
                        <span class="detail-label">位置:</span>
                        <span class="detail-value">1号楼-1层-大厅</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">分辨率:</span>
                        <span class="detail-value">1920x1080</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">当前播放:</span>
                        <span class="detail-value">企业文化宣传节目</span>
                      </div>
                    </div>
                  </div>
                  <div class="terminal-preview">
                    <div class="preview-screen">
                      <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=320&h=180&fit=crop&crop=center" alt="预览">
                      <div class="play-indicator">
                        <i class="fas fa-play"></i>
                      </div>
                    </div>
                  </div>
                  <div class="terminal-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="remoteControl('TERM_001')">
                      <i class="fas fa-gamepad"></i>
                      远程控制
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="pauseTerminal('TERM_001')">
                      <i class="fas fa-pause"></i>
                      暂停
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="terminalSettings('TERM_001')">
                      <i class="fas fa-cog"></i>
                      设置
                    </button>
                  </div>
                </div>

                <div class="terminal-item online idle">
                  <div class="terminal-info">
                    <div class="terminal-header">
                      <div class="terminal-name">电梯显示屏-A</div>
                      <div class="terminal-id">TERM_002</div>
                      <span class="status-badge success">在线</span>
                    </div>
                    <div class="terminal-details">
                      <div class="detail-row">
                        <span class="detail-label">位置:</span>
                        <span class="detail-value">1号楼-电梯A</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">分辨率:</span>
                        <span class="detail-value">1280x720</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">状态:</span>
                        <span class="detail-value">空闲待机</span>
                      </div>
                    </div>
                  </div>
                  <div class="terminal-preview">
                    <div class="preview-screen idle">
                      <div class="idle-message">
                        <i class="fas fa-moon"></i>
                        <span>待机中</span>
                      </div>
                    </div>
                  </div>
                  <div class="terminal-actions">
                    <button class="btn btn-sm btn-outline-success" onclick="pushToTerminal('TERM_002')">
                      <i class="fas fa-paper-plane"></i>
                      推送节目
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="remoteControl('TERM_002')">
                      <i class="fas fa-gamepad"></i>
                      远程控制
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="terminalSettings('TERM_002')">
                      <i class="fas fa-cog"></i>
                      设置
                    </button>
                  </div>
                </div>

                <div class="terminal-item offline">
                  <div class="terminal-info">
                    <div class="terminal-header">
                      <div class="terminal-name">会议室显示屏</div>
                      <div class="terminal-id">TERM_003</div>
                      <span class="status-badge danger">离线</span>
                    </div>
                    <div class="terminal-details">
                      <div class="detail-row">
                        <span class="detail-label">位置:</span>
                        <span class="detail-value">2号楼-3层-会议室</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">分辨率:</span>
                        <span class="detail-value">1920x1080</span>
                      </div>
                      <div class="detail-row">
                        <span class="detail-label">离线时间:</span>
                        <span class="detail-value">2小时前</span>
                      </div>
                    </div>
                  </div>
                  <div class="terminal-preview">
                    <div class="preview-screen offline">
                      <div class="offline-message">
                        <i class="fas fa-exclamation-triangle"></i>
                        <span>设备离线</span>
                      </div>
                    </div>
                  </div>
                  <div class="terminal-actions">
                    <button class="btn btn-sm btn-outline-warning" onclick="reconnectTerminal('TERM_003')">
                      <i class="fas fa-wifi"></i>
                      重新连接
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="reportIssue('TERM_003')">
                      <i class="fas fa-exclamation-circle"></i>
                      报告故障
                    </button>
                    <button class="btn btn-sm btn-outline-info" onclick="terminalSettings('TERM_003')">
                      <i class="fas fa-cog"></i>
                      设置
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 内容审核和播放监控 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-shield-check text-success"></i>
                内容审核
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshReviews()">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
                <button class="btn btn-outline-info btn-sm" onclick="showReviewSettings()">
                  <i class="fas fa-cog"></i>
                  审核设置
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 审核统计 -->
              <div class="review-stats mb-md">
                <div class="review-stat pending">
                  <i class="fas fa-clock"></i>
                  <div class="stat-info">
                    <div class="stat-value">3</div>
                    <div class="stat-label">待审核</div>
                  </div>
                </div>
                <div class="review-stat approved">
                  <i class="fas fa-check-circle"></i>
                  <div class="stat-info">
                    <div class="stat-value">15</div>
                    <div class="stat-label">已通过</div>
                  </div>
                </div>
                <div class="review-stat rejected">
                  <i class="fas fa-times-circle"></i>
                  <div class="stat-info">
                    <div class="stat-value">2</div>
                    <div class="stat-label">已拒绝</div>
                  </div>
                </div>
              </div>

              <!-- 待审核列表 -->
              <div class="review-list">
                <div class="review-item pending">
                  <div class="review-content">
                    <div class="content-preview">
                      <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=160&h=120&fit=crop&crop=center" alt="预览">
                    </div>
                    <div class="content-info">
                      <div class="content-title">春节活动宣传节目</div>
                      <div class="content-meta">
                        <span class="content-type">视频节目</span>
                        <span class="content-duration">时长: 4:30</span>
                        <span class="content-submitter">提交人: 张三</span>
                        <span class="content-time">提交时间: 2024-01-15 10:30</span>
                      </div>
                    </div>
                  </div>
                  <div class="review-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="previewContent('CONTENT_001')">
                      <i class="fas fa-eye"></i>
                      预览
                    </button>
                    <button class="btn btn-sm btn-success" onclick="approveContent('CONTENT_001')">
                      <i class="fas fa-check"></i>
                      通过
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="rejectContent('CONTENT_001')">
                      <i class="fas fa-times"></i>
                      拒绝
                    </button>
                  </div>
                </div>

                <div class="review-item pending">
                  <div class="review-content">
                    <div class="content-preview">
                      <div class="text-preview-small">
                        <i class="fas fa-font"></i>
                        <span>紧急通知</span>
                      </div>
                    </div>
                    <div class="content-info">
                      <div class="content-title">系统维护通知</div>
                      <div class="content-meta">
                        <span class="content-type">文本内容</span>
                        <span class="content-duration">显示: 持续</span>
                        <span class="content-submitter">提交人: 李四</span>
                        <span class="content-time">提交时间: 2024-01-15 11:15</span>
                      </div>
                    </div>
                  </div>
                  <div class="review-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="previewContent('CONTENT_002')">
                      <i class="fas fa-eye"></i>
                      预览
                    </button>
                    <button class="btn btn-sm btn-success" onclick="approveContent('CONTENT_002')">
                      <i class="fas fa-check"></i>
                      通过
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="rejectContent('CONTENT_002')">
                      <i class="fas fa-times"></i>
                      拒绝
                    </button>
                  </div>
                </div>

                <div class="review-item approved">
                  <div class="review-content">
                    <div class="content-preview">
                      <img src="https://images.unsplash.com/photo-1486406146926-c627a92ad1ab?w=160&h=120&fit=crop&crop=center" alt="预览">
                    </div>
                    <div class="content-info">
                      <div class="content-title">企业文化宣传节目</div>
                      <div class="content-meta">
                        <span class="content-type">混合节目</span>
                        <span class="content-duration">时长: 5:30</span>
                        <span class="content-reviewer">审核人: 王五</span>
                        <span class="content-time">审核时间: 2024-01-14 16:20</span>
                      </div>
                    </div>
                  </div>
                  <div class="review-status">
                    <span class="status-badge success">已通过</span>
                    <div class="review-note">内容符合规范，可以发布</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-chart-line text-info"></i>
                播放监控
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="refreshMonitoring()">
                <i class="fas fa-sync-alt"></i>
                刷新数据
              </button>
            </div>
            <div class="card-body">
              <!-- 播放统计 -->
              <div class="monitoring-stats mb-md">
                <div class="monitoring-chart">
                  <canvas id="playbackChart" width="300" height="200"></canvas>
                </div>
                <div class="monitoring-summary">
                  <div class="summary-item">
                    <div class="summary-label">今日播放次数</div>
                    <div class="summary-value">156</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">总播放时长</div>
                    <div class="summary-value">12.5小时</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">活跃终端</div>
                    <div class="summary-value">9/12</div>
                  </div>
                  <div class="summary-item">
                    <div class="summary-label">播放成功率</div>
                    <div class="summary-value">98.7%</div>
                  </div>
                </div>
              </div>

              <!-- 实时播放状态 -->
              <div class="realtime-status">
                <h6>实时播放状态</h6>
                <div class="status-list">
                  <div class="status-item playing">
                    <div class="status-indicator"></div>
                    <div class="status-info">
                      <div class="status-terminal">大厅显示屏-01</div>
                      <div class="status-content">企业文化宣传节目 - 进度 65%</div>
                      <div class="status-time">播放中 | 剩余 1:52</div>
                    </div>
                  </div>
                  <div class="status-item playing">
                    <div class="status-indicator"></div>
                    <div class="status-info">
                      <div class="status-terminal">电梯显示屏-A</div>
                      <div class="status-content">企业文化宣传节目 - 进度 65%</div>
                      <div class="status-time">播放中 | 剩余 1:52</div>
                    </div>
                  </div>
                  <div class="status-item idle">
                    <div class="status-indicator"></div>
                    <div class="status-info">
                      <div class="status-terminal">电梯显示屏-B</div>
                      <div class="status-content">空闲待机</div>
                      <div class="status-time">等待下一个播放计划</div>
                    </div>
                  </div>
                  <div class="status-item error">
                    <div class="status-indicator"></div>
                    <div class="status-info">
                      <div class="status-terminal">会议室显示屏</div>
                      <div class="status-content">连接异常</div>
                      <div class="status-time">离线 2小时</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('信息发布系统');
        }
      }, 100);

      // 初始化信息发布系统功能
      initInformationReleaseSystem();
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 初始化信息发布系统
    function initInformationReleaseSystem() {
      // 初始化播放监控图表
      initPlaybackChart();
      // 初始化素材筛选
      initMaterialFilters();
      // 初始化时间轴编辑器
      initTimelineEditor();
    }

    // 初始化播放监控图表
    function initPlaybackChart() {
      const canvas = document.getElementById('playbackChart');
      if (canvas) {
        const ctx = canvas.getContext('2d');
        drawPlaybackChart(ctx);
      }
    }

    // 绘制播放监控图表
    function drawPlaybackChart(ctx) {
      const width = ctx.canvas.width;
      const height = ctx.canvas.height;
      const padding = 40;

      ctx.clearRect(0, 0, width, height);

      // 绘制网格
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 1;

      // 垂直网格线
      for (let i = 0; i <= 6; i++) {
        const x = padding + (i / 6) * (width - 2 * padding);
        ctx.beginPath();
        ctx.moveTo(x, padding);
        ctx.lineTo(x, height - padding);
        ctx.stroke();
      }

      // 水平网格线
      for (let i = 0; i <= 4; i++) {
        const y = padding + (i / 4) * (height - 2 * padding);
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();
      }

      // 绘制播放数据线
      const data = [20, 35, 45, 30, 55, 40, 60];
      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 3;
      ctx.beginPath();

      data.forEach((value, index) => {
        const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
        const y = height - padding - (value / 70) * (height - 2 * padding);

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();

      // 绘制数据点
      ctx.fillStyle = '#3b82f6';
      data.forEach((value, index) => {
        const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
        const y = height - padding - (value / 70) * (height - 2 * padding);

        ctx.beginPath();
        ctx.arc(x, y, 4, 0, 2 * Math.PI);
        ctx.fill();
      });
    }

    // 初始化素材筛选
    function initMaterialFilters() {
      const filterTabs = document.querySelectorAll('.filter-tab');
      filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
          filterTabs.forEach(t => t.classList.remove('active'));
          this.classList.add('active');
        });
      });
    }

    // 初始化时间轴编辑器
    function initTimelineEditor() {
      const timelineItems = document.querySelectorAll('.timeline-item');
      timelineItems.forEach(item => {
        item.addEventListener('click', function() {
          timelineItems.forEach(i => i.classList.remove('selected'));
          this.classList.add('selected');
        });
      });
    }

    // 素材管理功能
    function refreshMaterials() {
      console.log('刷新素材');
      showNotification('素材列表已刷新', 'success');
    }

    function showUploadModal() {
      console.log('显示上传模态框');
      showNotification('上传素材功能开发中', 'info');
    }

    function filterMaterials(type) {
      console.log('筛选素材类型:', type);
      const materials = document.querySelectorAll('.material-item');

      materials.forEach(item => {
        if (type === 'all' || item.dataset.type === type) {
          item.style.display = 'block';
        } else {
          item.style.display = 'none';
        }
      });
    }

    function searchMaterials() {
      const keyword = document.getElementById('materialSearch').value;
      console.log('搜索素材:', keyword);
      showNotification('搜索完成', 'success');
    }

    function previewMaterial(materialId) {
      console.log('预览素材:', materialId);
      showNotification('素材预览功能开发中', 'info');
    }

    function addToProgram(materialId) {
      console.log('添加到节目:', materialId);
      showNotification('素材已添加到节目', 'success');
    }

    function deleteMaterial(materialId) {
      if (confirm('确定要删除这个素材吗？')) {
        console.log('删除素材:', materialId);
        showNotification('素材删除成功', 'success');
      }
    }

    function editMaterial(materialId) {
      console.log('编辑素材:', materialId);
      showNotification('素材编辑功能开发中', 'info');
    }

    // 节目编辑器功能
    function showCreateProgramModal() {
      console.log('显示新建节目模态框');
      showNotification('新建节目功能开发中', 'info');
    }

    function previewProgram() {
      console.log('预览节目');
      showNotification('节目预览功能开发中', 'info');
    }

    function saveProgram() {
      console.log('保存节目');
      showNotification('节目保存成功', 'success');
    }

    function submitForReview() {
      console.log('提交审核');
      showNotification('节目已提交审核', 'success');
    }

    function zoomTimeline(direction) {
      console.log('缩放时间轴:', direction);
      showNotification('时间轴缩放功能开发中', 'info');
    }

    function clearTimeline() {
      if (confirm('确定要清空时间轴吗？')) {
        console.log('清空时间轴');
        showNotification('时间轴已清空', 'warning');
      }
    }

    function selectTimelineItem(item) {
      const items = document.querySelectorAll('.timeline-item');
      items.forEach(i => i.classList.remove('selected'));
      item.classList.add('selected');
    }

    function moveItem(direction) {
      console.log('移动项目:', direction);
      showNotification('项目移动功能开发中', 'info');
    }

    function deleteItem() {
      console.log('删除项目');
      showNotification('项目删除成功', 'success');
    }

    // 播放计划管理功能
    function showScheduleModal() {
      console.log('显示新建计划模态框');
      showNotification('新建计划功能开发中', 'info');
    }

    function filterSchedules(type) {
      console.log('筛选播放计划:', type);
      const buttons = document.querySelectorAll('.filter-buttons .btn');
      buttons.forEach(btn => btn.classList.remove('active'));
      event.target.classList.add('active');
    }

    function filterByDate() {
      const date = document.getElementById('scheduleDate').value;
      console.log('按日期筛选:', date);
      showNotification('日期筛选完成', 'success');
    }

    function viewScheduleDetail(scheduleId) {
      console.log('查看计划详情:', scheduleId);
      showNotification('计划详情功能开发中', 'info');
    }

    function pauseSchedule(scheduleId) {
      console.log('暂停播放计划:', scheduleId);
      showNotification('播放计划已暂停', 'warning');
    }

    function stopSchedule(scheduleId) {
      console.log('停止播放计划:', scheduleId);
      showNotification('播放计划已停止', 'warning');
    }

    function startScheduleNow(scheduleId) {
      console.log('立即播放计划:', scheduleId);
      showNotification('播放计划已开始', 'success');
    }

    function editSchedule(scheduleId) {
      console.log('编辑播放计划:', scheduleId);
      showNotification('编辑计划功能开发中', 'info');
    }

    function viewPlaybackReport(scheduleId) {
      console.log('查看播放报告:', scheduleId);
      showNotification('播放报告功能开发中', 'info');
    }

    function repeatSchedule(scheduleId) {
      console.log('重复播放计划:', scheduleId);
      showNotification('计划重复播放已设置', 'success');
    }

    // 终端设备管理功能
    function refreshTerminals() {
      console.log('刷新终端状态');
      showNotification('终端状态已刷新', 'success');
    }

    function remoteControl(terminalId) {
      console.log('远程控制终端:', terminalId);
      showNotification('远程控制功能开发中', 'info');
    }

    function pauseTerminal(terminalId) {
      console.log('暂停终端播放:', terminalId);
      showNotification('终端播放已暂停', 'warning');
    }

    function terminalSettings(terminalId) {
      console.log('终端设置:', terminalId);
      showNotification('终端设置功能开发中', 'info');
    }

    function pushToTerminal(terminalId) {
      console.log('推送节目到终端:', terminalId);
      showNotification('节目推送功能开发中', 'info');
    }

    function reconnectTerminal(terminalId) {
      console.log('重新连接终端:', terminalId);
      showNotification('正在尝试重新连接...', 'info');
    }

    function reportIssue(terminalId) {
      console.log('报告终端故障:', terminalId);
      showNotification('故障报告功能开发中', 'info');
    }

    // 内容审核功能
    function refreshReviews() {
      console.log('刷新审核列表');
      showNotification('审核列表已刷新', 'success');
    }

    function showReviewSettings() {
      console.log('显示审核设置');
      showNotification('审核设置功能开发中', 'info');
    }

    function previewContent(contentId) {
      console.log('预览内容:', contentId);
      showNotification('内容预览功能开发中', 'info');
    }

    function approveContent(contentId) {
      console.log('通过审核:', contentId);
      showNotification('内容审核已通过', 'success');
    }

    function rejectContent(contentId) {
      const reason = prompt('请输入拒绝原因:');
      if (reason) {
        console.log('拒绝审核:', contentId, reason);
        showNotification('内容审核已拒绝', 'warning');
      }
    }

    // 播放监控功能
    function refreshMonitoring() {
      console.log('刷新监控数据');
      initPlaybackChart();
      showNotification('监控数据已刷新', 'success');
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);
      setTimeout(() => notification.classList.add('show'), 100);

      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
