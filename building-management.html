<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>楼宇与场所管理 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">楼宇与场所管理</h1>
          <p class="page-description">建筑结构建模、场所管理与二维码生成、场所资源绑定</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">3</div>
                <div class="stat-label">楼栋数量</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-building"></i>
              </div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">45</div>
                <div class="stat-label">楼层总数</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-layer-group"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">房间数量</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-door-open"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">89</div>
                <div class="stat-label">设备绑定</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-link"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 楼宇结构管理 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-building text-primary"></i>
                楼宇结构管理
              </h3>
              <button class="btn btn-primary" onclick="showAddBuildingModal()">
                <i class="fas fa-plus"></i>
                新增楼宇
              </button>
            </div>
            <div class="card-body">
              <!-- 楼宇树形结构 -->
              <div class="building-tree" id="buildingTree">
                <div class="tree-node building-node expanded">
                  <div class="tree-node-header" onclick="toggleNode(this)">
                    <i class="fas fa-chevron-down tree-toggle"></i>
                    <i class="fas fa-building tree-icon"></i>
                    <span class="tree-label">1号楼 (办公楼)</span>
                    <div class="tree-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editBuilding(1)" title="编辑">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="addFloor(1)" title="添加楼层">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteBuilding(1)" title="删除">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="tree-children">
                    <div class="tree-node floor-node expanded">
                      <div class="tree-node-header" onclick="toggleNode(this)">
                        <i class="fas fa-chevron-down tree-toggle"></i>
                        <i class="fas fa-layer-group tree-icon"></i>
                        <span class="tree-label">1层 (大厅)</span>
                        <div class="tree-actions">
                          <button class="btn btn-sm btn-outline-primary" onclick="editFloor(1, 1)" title="编辑">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-outline-success" onclick="addRoom(1, 1)" title="添加房间">
                            <i class="fas fa-plus"></i>
                          </button>
                          <button class="btn btn-sm btn-outline-danger" onclick="deleteFloor(1, 1)" title="删除">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                      <div class="tree-children">
                        <div class="tree-node room-node">
                          <div class="tree-node-header">
                            <i class="fas fa-door-open tree-icon"></i>
                            <span class="tree-label">101 (接待大厅)</span>
                            <span class="room-status active">使用中</span>
                            <div class="tree-actions">
                              <button class="btn btn-sm btn-outline-primary" onclick="editRoom(1, 1, 101)" title="编辑">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-info" onclick="generateQR(1, 1, 101)" title="生成二维码">
                                <i class="fas fa-qrcode"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-warning" onclick="bindDevice(1, 1, 101)" title="绑定设备">
                                <i class="fas fa-link"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-danger" onclick="deleteRoom(1, 1, 101)" title="删除">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                        <div class="tree-node room-node">
                          <div class="tree-node-header">
                            <i class="fas fa-door-open tree-icon"></i>
                            <span class="tree-label">102 (安保室)</span>
                            <span class="room-status active">使用中</span>
                            <div class="tree-actions">
                              <button class="btn btn-sm btn-outline-primary" onclick="editRoom(1, 1, 102)" title="编辑">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-info" onclick="generateQR(1, 1, 102)" title="生成二维码">
                                <i class="fas fa-qrcode"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-warning" onclick="bindDevice(1, 1, 102)" title="绑定设备">
                                <i class="fas fa-link"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-danger" onclick="deleteRoom(1, 1, 102)" title="删除">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="tree-node floor-node">
                      <div class="tree-node-header" onclick="toggleNode(this)">
                        <i class="fas fa-chevron-right tree-toggle"></i>
                        <i class="fas fa-layer-group tree-icon"></i>
                        <span class="tree-label">2层 (办公区)</span>
                        <div class="tree-actions">
                          <button class="btn btn-sm btn-outline-primary" onclick="editFloor(1, 2)" title="编辑">
                            <i class="fas fa-edit"></i>
                          </button>
                          <button class="btn btn-sm btn-outline-success" onclick="addRoom(1, 2)" title="添加房间">
                            <i class="fas fa-plus"></i>
                          </button>
                          <button class="btn btn-sm btn-outline-danger" onclick="deleteFloor(1, 2)" title="删除">
                            <i class="fas fa-trash"></i>
                          </button>
                        </div>
                      </div>
                      <div class="tree-children" style="display: none;">
                        <div class="tree-node room-node">
                          <div class="tree-node-header">
                            <i class="fas fa-door-open tree-icon"></i>
                            <span class="tree-label">201 (总经理办公室)</span>
                            <span class="room-status active">使用中</span>
                            <div class="tree-actions">
                              <button class="btn btn-sm btn-outline-primary" onclick="editRoom(1, 2, 201)" title="编辑">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-info" onclick="generateQR(1, 2, 201)" title="生成二维码">
                                <i class="fas fa-qrcode"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-warning" onclick="bindDevice(1, 2, 201)" title="绑定设备">
                                <i class="fas fa-link"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-danger" onclick="deleteRoom(1, 2, 201)" title="删除">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                        <div class="tree-node room-node">
                          <div class="tree-node-header">
                            <i class="fas fa-door-open tree-icon"></i>
                            <span class="tree-label">202 (会议室A)</span>
                            <span class="room-status maintenance">维护中</span>
                            <div class="tree-actions">
                              <button class="btn btn-sm btn-outline-primary" onclick="editRoom(1, 2, 202)" title="编辑">
                                <i class="fas fa-edit"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-info" onclick="generateQR(1, 2, 202)" title="生成二维码">
                                <i class="fas fa-qrcode"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-warning" onclick="bindDevice(1, 2, 202)" title="绑定设备">
                                <i class="fas fa-link"></i>
                              </button>
                              <button class="btn btn-sm btn-outline-danger" onclick="deleteRoom(1, 2, 202)" title="删除">
                                <i class="fas fa-trash"></i>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="tree-node building-node">
                  <div class="tree-node-header" onclick="toggleNode(this)">
                    <i class="fas fa-chevron-right tree-toggle"></i>
                    <i class="fas fa-building tree-icon"></i>
                    <span class="tree-label">2号楼 (研发楼)</span>
                    <div class="tree-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editBuilding(2)" title="编辑">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="addFloor(2)" title="添加楼层">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteBuilding(2)" title="删除">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="tree-children" style="display: none;">
                    <!-- 楼层内容 -->
                  </div>
                </div>

                <div class="tree-node building-node">
                  <div class="tree-node-header" onclick="toggleNode(this)">
                    <i class="fas fa-chevron-right tree-toggle"></i>
                    <i class="fas fa-building tree-icon"></i>
                    <span class="tree-label">3号楼 (生产楼)</span>
                    <div class="tree-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editBuilding(3)" title="编辑">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-success" onclick="addFloor(3)" title="添加楼层">
                        <i class="fas fa-plus"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteBuilding(3)" title="删除">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                  <div class="tree-children" style="display: none;">
                    <!-- 楼层内容 -->
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-qrcode text-info"></i>
                二维码管理
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="batchGenerateQR()">
                <i class="fas fa-layer-group"></i>
                批量生成
              </button>
            </div>
            <div class="card-body">
              <div class="qr-management">
                <div class="qr-stats mb-md">
                  <div class="qr-stat-item">
                    <div class="qr-stat-value">156</div>
                    <div class="qr-stat-label">已生成</div>
                  </div>
                  <div class="qr-stat-item">
                    <div class="qr-stat-value">142</div>
                    <div class="qr-stat-label">已使用</div>
                  </div>
                  <div class="qr-stat-item">
                    <div class="qr-stat-value">14</div>
                    <div class="qr-stat-label">未使用</div>
                  </div>
                </div>

                <div class="qr-list">
                  <div class="qr-item">
                    <div class="qr-code">
                      <img src="https://via.placeholder.com/60x60/4F46E5/FFFFFF?text=QR" alt="二维码">
                    </div>
                    <div class="qr-info">
                      <div class="qr-location">1号楼-1层-101</div>
                      <div class="qr-desc">接待大厅</div>
                      <div class="qr-time">生成时间: 2024-01-15</div>
                    </div>
                    <div class="qr-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="downloadQR('101')">
                        <i class="fas fa-download"></i>
                        下载
                      </button>
                      <button class="btn btn-sm btn-outline-info" onclick="previewQR('101')">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                    </div>
                  </div>

                  <div class="qr-item">
                    <div class="qr-code">
                      <img src="https://via.placeholder.com/60x60/059669/FFFFFF?text=QR" alt="二维码">
                    </div>
                    <div class="qr-info">
                      <div class="qr-location">1号楼-2层-201</div>
                      <div class="qr-desc">总经理办公室</div>
                      <div class="qr-time">生成时间: 2024-01-14</div>
                    </div>
                    <div class="qr-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="downloadQR('201')">
                        <i class="fas fa-download"></i>
                        下载
                      </button>
                      <button class="btn btn-sm btn-outline-info" onclick="previewQR('201')">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                    </div>
                  </div>

                  <div class="qr-item">
                    <div class="qr-code">
                      <img src="https://via.placeholder.com/60x60/DC2626/FFFFFF?text=QR" alt="二维码">
                    </div>
                    <div class="qr-info">
                      <div class="qr-location">1号楼-2层-202</div>
                      <div class="qr-desc">会议室A</div>
                      <div class="qr-time">生成时间: 2024-01-13</div>
                    </div>
                    <div class="qr-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="downloadQR('202')">
                        <i class="fas fa-download"></i>
                        下载
                      </button>
                      <button class="btn btn-sm btn-outline-info" onclick="previewQR('202')">
                        <i class="fas fa-eye"></i>
                        预览
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 设备绑定和场所资源管理 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-link text-warning"></i>
                设备绑定管理
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="refreshDeviceBinding()">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
            </div>
            <div class="card-body">
              <div class="device-binding-container">
                <!-- 绑定统计 -->
                <div class="binding-stats mb-md">
                  <div class="binding-stat-item success">
                    <i class="fas fa-check-circle"></i>
                    <div class="binding-stat-info">
                      <div class="binding-stat-value">89</div>
                      <div class="binding-stat-label">已绑定设备</div>
                    </div>
                  </div>
                  <div class="binding-stat-item warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    <div class="binding-stat-info">
                      <div class="binding-stat-value">12</div>
                      <div class="binding-stat-label">待绑定设备</div>
                    </div>
                  </div>
                  <div class="binding-stat-item danger">
                    <i class="fas fa-unlink"></i>
                    <div class="binding-stat-info">
                      <div class="binding-stat-value">5</div>
                      <div class="binding-stat-label">绑定异常</div>
                    </div>
                  </div>
                </div>

                <!-- 设备绑定列表 -->
                <div class="table-responsive">
                  <table class="table">
                    <thead>
                      <tr>
                        <th>房间位置</th>
                        <th>设备类型</th>
                        <th>设备名称</th>
                        <th>设备ID</th>
                        <th>绑定状态</th>
                        <th>绑定时间</th>
                        <th>操作</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td>1号楼-1层-101</td>
                        <td>
                          <i class="fas fa-video text-primary"></i>
                          监控摄像头
                        </td>
                        <td>大厅摄像头01</td>
                        <td>CAM_001</td>
                        <td><span class="status-badge success">已绑定</span></td>
                        <td>2024-01-15 09:30</td>
                        <td>
                          <button class="btn btn-sm btn-outline-warning" onclick="unbindDevice('CAM_001')">
                            <i class="fas fa-unlink"></i>
                            解绑
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td>1号楼-1层-102</td>
                        <td>
                          <i class="fas fa-door-open text-success"></i>
                          门禁设备
                        </td>
                        <td>安保室门禁</td>
                        <td>DOOR_001</td>
                        <td><span class="status-badge success">已绑定</span></td>
                        <td>2024-01-15 09:25</td>
                        <td>
                          <button class="btn btn-sm btn-outline-warning" onclick="unbindDevice('DOOR_001')">
                            <i class="fas fa-unlink"></i>
                            解绑
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td>1号楼-2层-201</td>
                        <td>
                          <i class="fas fa-thermometer-half text-info"></i>
                          环境传感器
                        </td>
                        <td>温湿度传感器</td>
                        <td>SENSOR_001</td>
                        <td><span class="status-badge warning">待绑定</span></td>
                        <td>-</td>
                        <td>
                          <button class="btn btn-sm btn-outline-success" onclick="bindDeviceToRoom('SENSOR_001')">
                            <i class="fas fa-link"></i>
                            绑定
                          </button>
                        </td>
                      </tr>
                      <tr>
                        <td>1号楼-2层-202</td>
                        <td>
                          <i class="fas fa-wifi text-primary"></i>
                          网络设备
                        </td>
                        <td>会议室路由器</td>
                        <td>WIFI_001</td>
                        <td><span class="status-badge danger">绑定异常</span></td>
                        <td>2024-01-14 16:20</td>
                        <td>
                          <button class="btn btn-sm btn-outline-primary" onclick="rebindDevice('WIFI_001')">
                            <i class="fas fa-redo"></i>
                            重新绑定
                          </button>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-map-marked-alt text-success"></i>
                场所资源管理
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="showResourceModal()">
                <i class="fas fa-plus"></i>
                添加资源
              </button>
            </div>
            <div class="card-body">
              <div class="resource-management">
                <!-- 资源分类 -->
                <div class="resource-categories mb-md">
                  <button class="btn btn-outline-primary btn-sm active" onclick="filterResources('all')">
                    全部资源
                  </button>
                  <button class="btn btn-outline-primary btn-sm" onclick="filterResources('furniture')">
                    <i class="fas fa-chair"></i>
                    家具设备
                  </button>
                  <button class="btn btn-outline-primary btn-sm" onclick="filterResources('electronics')">
                    <i class="fas fa-tv"></i>
                    电子设备
                  </button>
                  <button class="btn btn-outline-primary btn-sm" onclick="filterResources('safety')">
                    <i class="fas fa-shield-alt"></i>
                    安全设备
                  </button>
                  <button class="btn btn-outline-primary btn-sm" onclick="filterResources('other')">
                    <i class="fas fa-box"></i>
                    其他资源
                  </button>
                </div>

                <!-- 资源列表 -->
                <div class="resource-grid">
                  <div class="resource-item" data-category="furniture">
                    <div class="resource-icon">
                      <i class="fas fa-chair"></i>
                    </div>
                    <div class="resource-info">
                      <div class="resource-name">会议桌椅</div>
                      <div class="resource-location">1号楼-2层-202</div>
                      <div class="resource-status">
                        <span class="status-badge success">正常</span>
                        <span class="resource-count">数量: 12</span>
                      </div>
                    </div>
                    <div class="resource-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editResource('furniture_001')">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteResource('furniture_001')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div class="resource-item" data-category="electronics">
                    <div class="resource-icon">
                      <i class="fas fa-tv"></i>
                    </div>
                    <div class="resource-info">
                      <div class="resource-name">投影设备</div>
                      <div class="resource-location">1号楼-2层-202</div>
                      <div class="resource-status">
                        <span class="status-badge success">正常</span>
                        <span class="resource-count">数量: 1</span>
                      </div>
                    </div>
                    <div class="resource-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editResource('electronics_001')">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteResource('electronics_001')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div class="resource-item" data-category="safety">
                    <div class="resource-icon">
                      <i class="fas fa-fire-extinguisher"></i>
                    </div>
                    <div class="resource-info">
                      <div class="resource-name">灭火器</div>
                      <div class="resource-location">1号楼-1层-走廊</div>
                      <div class="resource-status">
                        <span class="status-badge warning">待检查</span>
                        <span class="resource-count">数量: 4</span>
                      </div>
                    </div>
                    <div class="resource-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editResource('safety_001')">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteResource('safety_001')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div class="resource-item" data-category="electronics">
                    <div class="resource-icon">
                      <i class="fas fa-desktop"></i>
                    </div>
                    <div class="resource-info">
                      <div class="resource-name">办公电脑</div>
                      <div class="resource-location">1号楼-2层-201</div>
                      <div class="resource-status">
                        <span class="status-badge success">正常</span>
                        <span class="resource-count">数量: 2</span>
                      </div>
                    </div>
                    <div class="resource-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editResource('electronics_002')">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteResource('electronics_002')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div class="resource-item" data-category="other">
                    <div class="resource-icon">
                      <i class="fas fa-box"></i>
                    </div>
                    <div class="resource-info">
                      <div class="resource-name">办公用品</div>
                      <div class="resource-location">1号楼-1层-储物间</div>
                      <div class="resource-status">
                        <span class="status-badge info">库存充足</span>
                        <span class="resource-count">数量: 50+</span>
                      </div>
                    </div>
                    <div class="resource-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editResource('other_001')">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteResource('other_001')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>

                  <div class="resource-item" data-category="furniture">
                    <div class="resource-icon">
                      <i class="fas fa-couch"></i>
                    </div>
                    <div class="resource-info">
                      <div class="resource-name">接待沙发</div>
                      <div class="resource-location">1号楼-1层-101</div>
                      <div class="resource-status">
                        <span class="status-badge success">正常</span>
                        <span class="resource-count">数量: 1套</span>
                      </div>
                    </div>
                    <div class="resource-actions">
                      <button class="btn btn-sm btn-outline-primary" onclick="editResource('furniture_002')">
                        <i class="fas fa-edit"></i>
                      </button>
                      <button class="btn btn-sm btn-outline-danger" onclick="deleteResource('furniture_002')">
                        <i class="fas fa-trash"></i>
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增楼宇模态框 -->
  <div class="modal" id="addBuildingModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>新增楼宇</h3>
        <button class="modal-close" onclick="closeModal('addBuildingModal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="addBuildingForm">
          <div class="form-row">
            <div class="form-group">
              <label>楼宇名称 *</label>
              <input type="text" class="form-control" name="buildingName" required placeholder="如：1号楼">
            </div>
            <div class="form-group">
              <label>楼宇类型 *</label>
              <select class="form-control" name="buildingType" required>
                <option value="">请选择</option>
                <option value="office">办公楼</option>
                <option value="residential">住宅楼</option>
                <option value="commercial">商业楼</option>
                <option value="industrial">工业楼</option>
                <option value="mixed">综合楼</option>
              </select>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>总楼层数 *</label>
              <input type="number" class="form-control" name="totalFloors" required min="1" max="100">
            </div>
            <div class="form-group">
              <label>建筑面积 (㎡)</label>
              <input type="number" class="form-control" name="buildingArea" min="0">
            </div>
          </div>
          <div class="form-group">
            <label>楼宇地址</label>
            <textarea class="form-control" name="address" rows="2" placeholder="详细地址"></textarea>
          </div>
          <div class="form-group">
            <label>楼宇描述</label>
            <textarea class="form-control" name="description" rows="3" placeholder="楼宇功能、特点等描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline-secondary" onclick="closeModal('addBuildingModal')">取消</button>
        <button class="btn btn-primary" onclick="submitAddBuilding()">确认添加</button>
      </div>
    </div>
  </div>

  <!-- 二维码预览模态框 -->
  <div class="modal" id="qrPreviewModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>二维码预览</h3>
        <button class="modal-close" onclick="closeModal('qrPreviewModal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body text-center">
        <div class="qr-preview-container">
          <div class="qr-preview-image">
            <img src="https://via.placeholder.com/200x200/4F46E5/FFFFFF?text=QR" alt="二维码" id="qrPreviewImage">
          </div>
          <div class="qr-preview-info mt-md">
            <h4 id="qrPreviewTitle">1号楼-1层-101</h4>
            <p id="qrPreviewDesc">接待大厅</p>
            <div class="qr-preview-details">
              <div class="detail-item">
                <label>生成时间:</label>
                <span id="qrPreviewTime">2024-01-15 09:30</span>
              </div>
              <div class="detail-item">
                <label>二维码内容:</label>
                <span id="qrPreviewContent">building:1,floor:1,room:101</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline-secondary" onclick="closeModal('qrPreviewModal')">关闭</button>
        <button class="btn btn-primary" onclick="downloadCurrentQR()">
          <i class="fas fa-download"></i>
          下载二维码
        </button>
      </div>
    </div>
  </div>

  <!-- 设备绑定模态框 -->
  <div class="modal" id="deviceBindingModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>设备绑定</h3>
        <button class="modal-close" onclick="closeModal('deviceBindingModal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="deviceBindingForm">
          <div class="form-group">
            <label>选择房间 *</label>
            <select class="form-control" name="roomId" required id="roomSelect">
              <option value="">请选择房间</option>
              <option value="1-1-101">1号楼-1层-101 (接待大厅)</option>
              <option value="1-1-102">1号楼-1层-102 (安保室)</option>
              <option value="1-2-201">1号楼-2层-201 (总经理办公室)</option>
              <option value="1-2-202">1号楼-2层-202 (会议室A)</option>
            </select>
          </div>
          <div class="form-group">
            <label>设备类型 *</label>
            <select class="form-control" name="deviceType" required>
              <option value="">请选择设备类型</option>
              <option value="camera">监控摄像头</option>
              <option value="door">门禁设备</option>
              <option value="sensor">环境传感器</option>
              <option value="network">网络设备</option>
              <option value="alarm">报警设备</option>
              <option value="other">其他设备</option>
            </select>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>设备名称 *</label>
              <input type="text" class="form-control" name="deviceName" required placeholder="设备名称">
            </div>
            <div class="form-group">
              <label>设备ID *</label>
              <input type="text" class="form-control" name="deviceId" required placeholder="设备唯一标识">
            </div>
          </div>
          <div class="form-group">
            <label>设备描述</label>
            <textarea class="form-control" name="deviceDesc" rows="3" placeholder="设备功能、位置等描述"></textarea>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline-secondary" onclick="closeModal('deviceBindingModal')">取消</button>
        <button class="btn btn-primary" onclick="submitDeviceBinding()">确认绑定</button>
      </div>
    </div>
  </div>

  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('楼宇与场所管理');
        }
      }, 100);

      // 初始化页面功能
      initBuildingManagement();
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 初始化楼宇管理功能
    function initBuildingManagement() {
      // 初始化树形结构
      initBuildingTree();
      // 初始化资源筛选
      initResourceFilter();
    }

    // 初始化楼宇树形结构
    function initBuildingTree() {
      // 为所有树节点添加事件监听
      const treeNodes = document.querySelectorAll('.tree-node-header');
      treeNodes.forEach(node => {
        const toggle = node.querySelector('.tree-toggle');
        if (toggle) {
          node.addEventListener('click', function(e) {
            if (!e.target.closest('.tree-actions')) {
              toggleNode(this);
            }
          });
        }
      });
    }

    // 切换树节点展开/收起
    function toggleNode(header) {
      const node = header.parentNode;
      const toggle = header.querySelector('.tree-toggle');
      const children = node.querySelector('.tree-children');

      if (children) {
        const isExpanded = children.style.display !== 'none';
        children.style.display = isExpanded ? 'none' : 'block';
        toggle.className = isExpanded ? 'fas fa-chevron-right tree-toggle' : 'fas fa-chevron-down tree-toggle';

        if (isExpanded) {
          node.classList.remove('expanded');
        } else {
          node.classList.add('expanded');
        }
      }
    }

    // 显示新增楼宇模态框
    function showAddBuildingModal() {
      document.getElementById('addBuildingModal').style.display = 'flex';
    }

    // 关闭模态框
    function closeModal(modalId) {
      document.getElementById(modalId).style.display = 'none';
    }

    // 提交新增楼宇
    function submitAddBuilding() {
      const form = document.getElementById('addBuildingForm');
      const formData = new FormData(form);

      const buildingName = formData.get('buildingName');
      const buildingType = formData.get('buildingType');
      const totalFloors = formData.get('totalFloors');

      if (!buildingName || !buildingType || !totalFloors) {
        showNotification('请填写所有必填字段', 'error');
        return;
      }

      console.log('新增楼宇数据:', Object.fromEntries(formData));
      showNotification('楼宇添加成功', 'success');
      closeModal('addBuildingModal');
      form.reset();
    }

    // 编辑楼宇
    function editBuilding(buildingId) {
      console.log('编辑楼宇:', buildingId);
      showNotification('编辑楼宇功能开发中', 'info');
    }

    // 删除楼宇
    function deleteBuilding(buildingId) {
      if (confirm('确定要删除这个楼宇吗？这将同时删除所有楼层和房间数据。')) {
        console.log('删除楼宇:', buildingId);
        showNotification('楼宇删除成功', 'success');
      }
    }

    // 添加楼层
    function addFloor(buildingId) {
      const floorNumber = prompt('请输入楼层号:');
      if (floorNumber) {
        console.log('添加楼层:', buildingId, floorNumber);
        showNotification('楼层添加成功', 'success');
      }
    }

    // 编辑楼层
    function editFloor(buildingId, floorId) {
      console.log('编辑楼层:', buildingId, floorId);
      showNotification('编辑楼层功能开发中', 'info');
    }

    // 删除楼层
    function deleteFloor(buildingId, floorId) {
      if (confirm('确定要删除这个楼层吗？这将同时删除所有房间数据。')) {
        console.log('删除楼层:', buildingId, floorId);
        showNotification('楼层删除成功', 'success');
      }
    }

    // 添加房间
    function addRoom(buildingId, floorId) {
      const roomNumber = prompt('请输入房间号:');
      if (roomNumber) {
        console.log('添加房间:', buildingId, floorId, roomNumber);
        showNotification('房间添加成功', 'success');
      }
    }

    // 编辑房间
    function editRoom(buildingId, floorId, roomId) {
      console.log('编辑房间:', buildingId, floorId, roomId);
      showNotification('编辑房间功能开发中', 'info');
    }

    // 删除房间
    function deleteRoom(buildingId, floorId, roomId) {
      if (confirm('确定要删除这个房间吗？')) {
        console.log('删除房间:', buildingId, floorId, roomId);
        showNotification('房间删除成功', 'success');
      }
    }

    // 生成二维码
    function generateQR(buildingId, floorId, roomId) {
      console.log('生成二维码:', buildingId, floorId, roomId);
      showNotification('二维码生成成功', 'success');
    }

    // 绑定设备
    function bindDevice(buildingId, floorId, roomId) {
      document.getElementById('deviceBindingModal').style.display = 'flex';
      // 设置默认选中的房间
      const roomSelect = document.getElementById('roomSelect');
      roomSelect.value = `${buildingId}-${floorId}-${roomId}`;
    }

    // 批量生成二维码
    function batchGenerateQR() {
      console.log('批量生成二维码');
      showNotification('批量生成功能已触发', 'info');
    }

    // 下载二维码
    function downloadQR(roomId) {
      console.log('下载二维码:', roomId);
      showNotification('二维码下载已开始', 'info');
    }

    // 预览二维码
    function previewQR(roomId) {
      console.log('预览二维码:', roomId);
      document.getElementById('qrPreviewModal').style.display = 'flex';
    }

    // 下载当前预览的二维码
    function downloadCurrentQR() {
      console.log('下载当前二维码');
      showNotification('二维码下载已开始', 'info');
      closeModal('qrPreviewModal');
    }

    // 刷新设备绑定
    function refreshDeviceBinding() {
      console.log('刷新设备绑定');
      showNotification('设备绑定列表已刷新', 'info');
    }

    // 解绑设备
    function unbindDevice(deviceId) {
      if (confirm('确定要解绑这个设备吗？')) {
        console.log('解绑设备:', deviceId);
        showNotification('设备解绑成功', 'success');
      }
    }

    // 绑定设备到房间
    function bindDeviceToRoom(deviceId) {
      document.getElementById('deviceBindingModal').style.display = 'flex';
    }

    // 重新绑定设备
    function rebindDevice(deviceId) {
      console.log('重新绑定设备:', deviceId);
      showNotification('设备重新绑定成功', 'success');
    }

    // 提交设备绑定
    function submitDeviceBinding() {
      const form = document.getElementById('deviceBindingForm');
      const formData = new FormData(form);

      const roomId = formData.get('roomId');
      const deviceType = formData.get('deviceType');
      const deviceName = formData.get('deviceName');
      const deviceId = formData.get('deviceId');

      if (!roomId || !deviceType || !deviceName || !deviceId) {
        showNotification('请填写所有必填字段', 'error');
        return;
      }

      console.log('设备绑定数据:', Object.fromEntries(formData));
      showNotification('设备绑定成功', 'success');
      closeModal('deviceBindingModal');
      form.reset();
    }

    // 显示资源模态框
    function showResourceModal() {
      console.log('显示资源模态框');
      showNotification('资源添加功能开发中', 'info');
    }

    // 初始化资源筛选
    function initResourceFilter() {
      const filterButtons = document.querySelectorAll('.resource-categories .btn');
      filterButtons.forEach(button => {
        button.addEventListener('click', function() {
          filterButtons.forEach(btn => btn.classList.remove('active'));
          this.classList.add('active');
        });
      });
    }

    // 筛选资源
    function filterResources(category) {
      const resourceItems = document.querySelectorAll('.resource-item');

      resourceItems.forEach(item => {
        if (category === 'all' || item.dataset.category === category) {
          item.style.display = 'flex';
        } else {
          item.style.display = 'none';
        }
      });
    }

    // 编辑资源
    function editResource(resourceId) {
      console.log('编辑资源:', resourceId);
      showNotification('编辑资源功能开发中', 'info');
    }

    // 删除资源
    function deleteResource(resourceId) {
      if (confirm('确定要删除这个资源吗？')) {
        console.log('删除资源:', resourceId);
        showNotification('资源删除成功', 'success');
      }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);
      setTimeout(() => notification.classList.add('show'), 100);

      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
