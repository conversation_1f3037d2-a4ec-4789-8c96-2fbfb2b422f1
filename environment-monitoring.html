<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>动环监控系统 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">动环监控系统</h1>
          <p class="page-description">设备数据监控、动环告警与联动配置、历史数据分析图表</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">24°C</div>
                <div class="stat-label">平均温度</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-thermometer-half"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">45%</div>
                <div class="stat-label">平均湿度</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-tint"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">2</div>
                <div class="stat-label">异常告警</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">36</div>
                <div class="stat-label">监控设备</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-microchip"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 实时环境监控面板 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-thermometer-half text-success"></i>
                实时环境数据
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="refreshEnvironmentData()">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
                <button class="btn btn-primary btn-sm" onclick="showReportModal()">
                  <i class="fas fa-chart-line"></i>
                  查看报表
                </button>
              </div>
            </div>
            <div class="card-body">
              <!-- 环境数据网格 -->
              <div class="environment-grid">
                <div class="env-item temperature">
                  <div class="env-icon">
                    <i class="fas fa-thermometer-half"></i>
                  </div>
                  <div class="env-info">
                    <div class="env-value" id="currentTemp">24.5°C</div>
                    <div class="env-label">当前温度</div>
                    <div class="env-status normal">正常</div>
                  </div>
                  <div class="env-chart">
                    <canvas id="tempChart" width="100" height="60"></canvas>
                  </div>
                </div>

                <div class="env-item humidity">
                  <div class="env-icon">
                    <i class="fas fa-tint"></i>
                  </div>
                  <div class="env-info">
                    <div class="env-value" id="currentHumidity">45.2%</div>
                    <div class="env-label">当前湿度</div>
                    <div class="env-status normal">正常</div>
                  </div>
                  <div class="env-chart">
                    <canvas id="humidityChart" width="100" height="60"></canvas>
                  </div>
                </div>

                <div class="env-item pressure">
                  <div class="env-icon">
                    <i class="fas fa-gauge"></i>
                  </div>
                  <div class="env-info">
                    <div class="env-value" id="currentPressure">1013.2hPa</div>
                    <div class="env-label">大气压力</div>
                    <div class="env-status normal">正常</div>
                  </div>
                  <div class="env-chart">
                    <canvas id="pressureChart" width="100" height="60"></canvas>
                  </div>
                </div>

                <div class="env-item air-quality">
                  <div class="env-icon">
                    <i class="fas fa-wind"></i>
                  </div>
                  <div class="env-info">
                    <div class="env-value" id="currentAirQuality">良好</div>
                    <div class="env-label">空气质量</div>
                    <div class="env-status good">良好</div>
                  </div>
                  <div class="env-chart">
                    <canvas id="airQualityChart" width="100" height="60"></canvas>
                  </div>
                </div>

                <div class="env-item noise">
                  <div class="env-icon">
                    <i class="fas fa-volume-up"></i>
                  </div>
                  <div class="env-info">
                    <div class="env-value" id="currentNoise">42dB</div>
                    <div class="env-label">噪音水平</div>
                    <div class="env-status normal">正常</div>
                  </div>
                  <div class="env-chart">
                    <canvas id="noiseChart" width="100" height="60"></canvas>
                  </div>
                </div>

                <div class="env-item light">
                  <div class="env-icon">
                    <i class="fas fa-lightbulb"></i>
                  </div>
                  <div class="env-info">
                    <div class="env-value" id="currentLight">320lux</div>
                    <div class="env-label">光照强度</div>
                    <div class="env-status normal">正常</div>
                  </div>
                  <div class="env-chart">
                    <canvas id="lightChart" width="100" height="60"></canvas>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-microchip text-info"></i>
                设备状态监控
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="refreshDeviceStatus()">
                <i class="fas fa-sync-alt"></i>
                刷新状态
              </button>
            </div>
            <div class="card-body">
              <div class="device-status-list">
                <div class="device-item online">
                  <div class="device-info">
                    <div class="device-name">温湿度传感器-01</div>
                    <div class="device-location">1号楼-1层-大厅</div>
                    <div class="device-id">TH_001</div>
                  </div>
                  <div class="device-status">
                    <span class="status-badge success">在线</span>
                    <div class="device-signal">
                      <i class="fas fa-signal"></i>
                      <span>信号强度: 85%</span>
                    </div>
                  </div>
                  <div class="device-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDeviceDetail('TH_001')">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="configDevice('TH_001')">
                      <i class="fas fa-cog"></i>
                    </button>
                  </div>
                </div>

                <div class="device-item online">
                  <div class="device-info">
                    <div class="device-name">空气质量传感器-01</div>
                    <div class="device-location">1号楼-2层-办公区</div>
                    <div class="device-id">AQ_001</div>
                  </div>
                  <div class="device-status">
                    <span class="status-badge success">在线</span>
                    <div class="device-signal">
                      <i class="fas fa-signal"></i>
                      <span>信号强度: 92%</span>
                    </div>
                  </div>
                  <div class="device-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDeviceDetail('AQ_001')">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-warning" onclick="configDevice('AQ_001')">
                      <i class="fas fa-cog"></i>
                    </button>
                  </div>
                </div>

                <div class="device-item warning">
                  <div class="device-info">
                    <div class="device-name">噪音监测器-01</div>
                    <div class="device-location">1号楼-3层-会议室</div>
                    <div class="device-id">NOISE_001</div>
                  </div>
                  <div class="device-status">
                    <span class="status-badge warning">异常</span>
                    <div class="device-signal">
                      <i class="fas fa-signal"></i>
                      <span>信号强度: 45%</span>
                    </div>
                  </div>
                  <div class="device-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDeviceDetail('NOISE_001')">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="repairDevice('NOISE_001')">
                      <i class="fas fa-wrench"></i>
                    </button>
                  </div>
                </div>

                <div class="device-item offline">
                  <div class="device-info">
                    <div class="device-name">光照传感器-02</div>
                    <div class="device-location">2号楼-1层-走廊</div>
                    <div class="device-id">LIGHT_002</div>
                  </div>
                  <div class="device-status">
                    <span class="status-badge danger">离线</span>
                    <div class="device-signal">
                      <i class="fas fa-signal-slash"></i>
                      <span>无信号</span>
                    </div>
                  </div>
                  <div class="device-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="viewDeviceDetail('LIGHT_002')">
                      <i class="fas fa-eye"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="repairDevice('LIGHT_002')">
                      <i class="fas fa-wrench"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 告警管理和历史数据 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-exclamation-triangle text-warning"></i>
                环境告警管理
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="showAlarmConfig()">
                <i class="fas fa-cog"></i>
                告警配置
              </button>
            </div>
            <div class="card-body">
              <div class="alarm-list">
                <div class="alarm-item high">
                  <div class="alarm-icon">
                    <i class="fas fa-thermometer-full"></i>
                  </div>
                  <div class="alarm-content">
                    <div class="alarm-title">温度异常告警</div>
                    <div class="alarm-desc">1号楼-3层-机房 温度超过阈值 (28.5°C > 28°C)</div>
                    <div class="alarm-time">2024-01-15 14:32:15</div>
                  </div>
                  <div class="alarm-level high">高</div>
                  <div class="alarm-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="handleAlarm('TEMP_001')">
                      <i class="fas fa-check"></i>
                      处理
                    </button>
                  </div>
                </div>

                <div class="alarm-item medium">
                  <div class="alarm-icon">
                    <i class="fas fa-volume-up"></i>
                  </div>
                  <div class="alarm-content">
                    <div class="alarm-title">噪音水平告警</div>
                    <div class="alarm-desc">1号楼-1层-大厅 噪音水平偏高 (65dB > 60dB)</div>
                    <div class="alarm-time">2024-01-15 14:28:42</div>
                  </div>
                  <div class="alarm-level medium">中</div>
                  <div class="alarm-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="handleAlarm('NOISE_002')">
                      <i class="fas fa-check"></i>
                      处理
                    </button>
                  </div>
                </div>

                <div class="alarm-item low resolved">
                  <div class="alarm-icon">
                    <i class="fas fa-tint"></i>
                  </div>
                  <div class="alarm-content">
                    <div class="alarm-title">湿度异常告警</div>
                    <div class="alarm-desc">2号楼-2层-办公区 湿度偏低 (35% < 40%)</div>
                    <div class="alarm-time">2024-01-15 14:15:20</div>
                  </div>
                  <div class="alarm-level low">低</div>
                  <div class="alarm-status resolved">已处理</div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-chart-area text-primary"></i>
                历史数据趋势
              </h3>
              <div class="d-flex gap-sm">
                <select class="form-control form-control-sm" style="width: auto;" id="timeRangeSelect">
                  <option value="1h">最近1小时</option>
                  <option value="6h">最近6小时</option>
                  <option value="24h" selected>最近24小时</option>
                  <option value="7d">最近7天</option>
                </select>
                <button class="btn btn-outline-primary btn-sm" onclick="exportHistoryData()">
                  <i class="fas fa-download"></i>
                  导出
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="chart-container">
                <canvas id="historyChart" width="400" height="200"></canvas>
              </div>
              <div class="chart-legend">
                <div class="legend-item">
                  <span class="legend-color temperature"></span>
                  <span>温度 (°C)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color humidity"></span>
                  <span>湿度 (%)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color pressure"></span>
                  <span>气压 (hPa)</span>
                </div>
                <div class="legend-item">
                  <span class="legend-color noise"></span>
                  <span>噪音 (dB)</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 环境参数配置和设备控制 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-sliders-h text-success"></i>
                环境参数配置
              </h3>
              <button class="btn btn-primary btn-sm" onclick="saveEnvironmentConfig()">
                <i class="fas fa-save"></i>
                保存配置
              </button>
            </div>
            <div class="card-body">
              <div class="config-grid">
                <div class="config-section">
                  <h5>温度监控</h5>
                  <div class="config-item">
                    <label>正常范围</label>
                    <div class="range-input">
                      <input type="number" class="form-control" value="18" min="0" max="50">
                      <span>-</span>
                      <input type="number" class="form-control" value="28" min="0" max="50">
                      <span>°C</span>
                    </div>
                  </div>
                  <div class="config-item">
                    <label>告警阈值</label>
                    <div class="range-input">
                      <input type="number" class="form-control" value="15" min="0" max="50">
                      <span>-</span>
                      <input type="number" class="form-control" value="32" min="0" max="50">
                      <span>°C</span>
                    </div>
                  </div>
                </div>

                <div class="config-section">
                  <h5>湿度监控</h5>
                  <div class="config-item">
                    <label>正常范围</label>
                    <div class="range-input">
                      <input type="number" class="form-control" value="40" min="0" max="100">
                      <span>-</span>
                      <input type="number" class="form-control" value="70" min="0" max="100">
                      <span>%</span>
                    </div>
                  </div>
                  <div class="config-item">
                    <label>告警阈值</label>
                    <div class="range-input">
                      <input type="number" class="form-control" value="30" min="0" max="100">
                      <span>-</span>
                      <input type="number" class="form-control" value="80" min="0" max="100">
                      <span>%</span>
                    </div>
                  </div>
                </div>

                <div class="config-section">
                  <h5>噪音监控</h5>
                  <div class="config-item">
                    <label>正常范围</label>
                    <div class="range-input">
                      <input type="number" class="form-control" value="30" min="0" max="120">
                      <span>-</span>
                      <input type="number" class="form-control" value="60" min="0" max="120">
                      <span>dB</span>
                    </div>
                  </div>
                  <div class="config-item">
                    <label>告警阈值</label>
                    <div class="range-input">
                      <input type="number" class="form-control" value="70" min="0" max="120">
                      <span>dB</span>
                    </div>
                  </div>
                </div>

                <div class="config-section">
                  <h5>监控设置</h5>
                  <div class="config-item">
                    <label class="checkbox-label">
                      <input type="checkbox" checked>
                      <span class="checkmark"></span>
                      启用实时监控
                    </label>
                  </div>
                  <div class="config-item">
                    <label class="checkbox-label">
                      <input type="checkbox" checked>
                      <span class="checkmark"></span>
                      启用告警通知
                    </label>
                  </div>
                  <div class="config-item">
                    <label>数据采集间隔</label>
                    <select class="form-control">
                      <option value="30">30秒</option>
                      <option value="60" selected>1分钟</option>
                      <option value="300">5分钟</option>
                      <option value="600">10分钟</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-tools text-warning"></i>
                设备控制中心
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="refreshControlStatus()">
                <i class="fas fa-sync-alt"></i>
                刷新状态
              </button>
            </div>
            <div class="card-body">
              <div class="control-grid">
                <div class="control-item">
                  <div class="control-header">
                    <div class="control-name">空调系统</div>
                    <div class="control-status online">在线</div>
                  </div>
                  <div class="control-info">
                    <div class="control-value">24°C</div>
                    <div class="control-label">目标温度</div>
                  </div>
                  <div class="control-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="adjustTemperature(-1)">
                      <i class="fas fa-minus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="adjustTemperature(1)">
                      <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="toggleAircon()">
                      <i class="fas fa-power-off"></i>
                    </button>
                  </div>
                </div>

                <div class="control-item">
                  <div class="control-header">
                    <div class="control-name">新风系统</div>
                    <div class="control-status online">在线</div>
                  </div>
                  <div class="control-info">
                    <div class="control-value">自动</div>
                    <div class="control-label">运行模式</div>
                  </div>
                  <div class="control-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="changeVentilationMode()">
                      <i class="fas fa-cog"></i>
                      模式
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="toggleVentilation()">
                      <i class="fas fa-power-off"></i>
                    </button>
                  </div>
                </div>

                <div class="control-item">
                  <div class="control-header">
                    <div class="control-name">照明系统</div>
                    <div class="control-status online">在线</div>
                  </div>
                  <div class="control-info">
                    <div class="control-value">75%</div>
                    <div class="control-label">亮度</div>
                  </div>
                  <div class="control-actions">
                    <button class="btn btn-sm btn-outline-primary" onclick="adjustBrightness(-10)">
                      <i class="fas fa-minus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-primary" onclick="adjustBrightness(10)">
                      <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-success" onclick="toggleLighting()">
                      <i class="fas fa-power-off"></i>
                    </button>
                  </div>
                </div>

                <div class="control-item">
                  <div class="control-header">
                    <div class="control-name">加湿器</div>
                    <div class="control-status offline">离线</div>
                  </div>
                  <div class="control-info">
                    <div class="control-value">--</div>
                    <div class="control-label">目标湿度</div>
                  </div>
                  <div class="control-actions">
                    <button class="btn btn-sm btn-outline-secondary" disabled>
                      <i class="fas fa-minus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-secondary" disabled>
                      <i class="fas fa-plus"></i>
                    </button>
                    <button class="btn btn-sm btn-outline-danger" onclick="repairDevice('HUMIDIFIER_001')">
                      <i class="fas fa-wrench"></i>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('动环监控系统');
        }
      }, 100);

      // 初始化环境监控功能
      initEnvironmentMonitoring();
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 初始化环境监控系统
    function initEnvironmentMonitoring() {
      // 初始化图表
      initEnvironmentCharts();
      // 启动实时数据更新
      startRealTimeUpdate();
      // 初始化历史数据图表
      initHistoryChart();
    }

    // 初始化环境数据小图表
    function initEnvironmentCharts() {
      const chartIds = ['tempChart', 'humidityChart', 'pressureChart', 'airQualityChart', 'noiseChart', 'lightChart'];

      chartIds.forEach(chartId => {
        const canvas = document.getElementById(chartId);
        if (canvas) {
          const ctx = canvas.getContext('2d');
          drawMiniChart(ctx, generateMockData(10));
        }
      });
    }

    // 绘制迷你图表
    function drawMiniChart(ctx, data) {
      const width = ctx.canvas.width;
      const height = ctx.canvas.height;
      const padding = 5;

      ctx.clearRect(0, 0, width, height);

      // 计算数据点位置
      const maxValue = Math.max(...data);
      const minValue = Math.min(...data);
      const range = maxValue - minValue || 1;

      ctx.strokeStyle = '#3b82f6';
      ctx.lineWidth = 2;
      ctx.beginPath();

      data.forEach((value, index) => {
        const x = padding + (index / (data.length - 1)) * (width - 2 * padding);
        const y = height - padding - ((value - minValue) / range) * (height - 2 * padding);

        if (index === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      });

      ctx.stroke();
    }

    // 生成模拟数据
    function generateMockData(count) {
      const data = [];
      for (let i = 0; i < count; i++) {
        data.push(Math.random() * 100);
      }
      return data;
    }

    // 初始化历史数据图表
    function initHistoryChart() {
      const canvas = document.getElementById('historyChart');
      if (canvas) {
        const ctx = canvas.getContext('2d');
        drawHistoryChart(ctx);
      }
    }

    // 绘制历史数据图表
    function drawHistoryChart(ctx) {
      const width = ctx.canvas.width;
      const height = ctx.canvas.height;
      const padding = 40;

      ctx.clearRect(0, 0, width, height);

      // 绘制网格
      ctx.strokeStyle = '#e5e7eb';
      ctx.lineWidth = 1;

      // 垂直网格线
      for (let i = 0; i <= 10; i++) {
        const x = padding + (i / 10) * (width - 2 * padding);
        ctx.beginPath();
        ctx.moveTo(x, padding);
        ctx.lineTo(x, height - padding);
        ctx.stroke();
      }

      // 水平网格线
      for (let i = 0; i <= 5; i++) {
        const y = padding + (i / 5) * (height - 2 * padding);
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();
      }

      // 绘制数据线
      const datasets = [
        { data: generateMockData(24), color: '#ef4444', label: '温度' },
        { data: generateMockData(24), color: '#3b82f6', label: '湿度' },
        { data: generateMockData(24), color: '#10b981', label: '气压' },
        { data: generateMockData(24), color: '#f59e0b', label: '噪音' }
      ];

      datasets.forEach(dataset => {
        ctx.strokeStyle = dataset.color;
        ctx.lineWidth = 2;
        ctx.beginPath();

        dataset.data.forEach((value, index) => {
          const x = padding + (index / (dataset.data.length - 1)) * (width - 2 * padding);
          const y = height - padding - (value / 100) * (height - 2 * padding);

          if (index === 0) {
            ctx.moveTo(x, y);
          } else {
            ctx.lineTo(x, y);
          }
        });

        ctx.stroke();
      });
    }

    // 启动实时数据更新
    function startRealTimeUpdate() {
      setInterval(() => {
        updateEnvironmentData();
      }, 30000); // 每30秒更新一次
    }

    // 更新环境数据
    function updateEnvironmentData() {
      // 模拟实时数据更新
      const temp = (20 + Math.random() * 10).toFixed(1);
      const humidity = (40 + Math.random() * 20).toFixed(1);
      const pressure = (1010 + Math.random() * 10).toFixed(1);
      const noise = (35 + Math.random() * 20).toFixed(0);
      const light = (250 + Math.random() * 100).toFixed(0);

      document.getElementById('currentTemp').textContent = temp + '°C';
      document.getElementById('currentHumidity').textContent = humidity + '%';
      document.getElementById('currentPressure').textContent = pressure + 'hPa';
      document.getElementById('currentNoise').textContent = noise + 'dB';
      document.getElementById('currentLight').textContent = light + 'lux';

      // 更新小图表
      initEnvironmentCharts();
    }

    // 刷新环境数据
    function refreshEnvironmentData() {
      console.log('刷新环境数据');
      updateEnvironmentData();
      showNotification('环境数据已刷新', 'success');
    }

    // 显示报表模态框
    function showReportModal() {
      console.log('显示报表模态框');
      showNotification('报表功能开发中', 'info');
    }

    // 刷新设备状态
    function refreshDeviceStatus() {
      console.log('刷新设备状态');
      showNotification('设备状态已刷新', 'success');
    }

    // 查看设备详情
    function viewDeviceDetail(deviceId) {
      console.log('查看设备详情:', deviceId);
      showNotification('设备详情功能开发中', 'info');
    }

    // 配置设备
    function configDevice(deviceId) {
      console.log('配置设备:', deviceId);
      showNotification('设备配置功能开发中', 'info');
    }

    // 维修设备
    function repairDevice(deviceId) {
      console.log('维修设备:', deviceId);
      showNotification('已提交维修申请', 'success');
    }

    // 显示告警配置
    function showAlarmConfig() {
      console.log('显示告警配置');
      showNotification('告警配置功能开发中', 'info');
    }

    // 处理告警
    function handleAlarm(alarmId) {
      console.log('处理告警:', alarmId);
      showNotification('告警已处理', 'success');
    }

    // 导出历史数据
    function exportHistoryData() {
      console.log('导出历史数据');
      showNotification('数据导出已开始', 'info');
    }

    // 保存环境配置
    function saveEnvironmentConfig() {
      console.log('保存环境配置');
      showNotification('配置保存成功', 'success');
    }

    // 刷新控制状态
    function refreshControlStatus() {
      console.log('刷新控制状态');
      showNotification('控制状态已刷新', 'success');
    }

    // 调节温度
    function adjustTemperature(delta) {
      console.log('调节温度:', delta);
      showNotification(`温度已调节 ${delta > 0 ? '+' : ''}${delta}°C`, 'success');
    }

    // 切换空调
    function toggleAircon() {
      console.log('切换空调状态');
      showNotification('空调状态已切换', 'success');
    }

    // 改变通风模式
    function changeVentilationMode() {
      console.log('改变通风模式');
      showNotification('通风模式已切换', 'success');
    }

    // 切换通风系统
    function toggleVentilation() {
      console.log('切换通风系统');
      showNotification('通风系统状态已切换', 'success');
    }

    // 调节亮度
    function adjustBrightness(delta) {
      console.log('调节亮度:', delta);
      showNotification(`亮度已调节 ${delta > 0 ? '+' : ''}${delta}%`, 'success');
    }

    // 切换照明
    function toggleLighting() {
      console.log('切换照明状态');
      showNotification('照明状态已切换', 'success');
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      document.body.appendChild(notification);
      setTimeout(() => notification.classList.add('show'), 100);

      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
