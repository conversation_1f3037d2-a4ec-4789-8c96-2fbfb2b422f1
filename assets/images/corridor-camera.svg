<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 480">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="corridorFloor" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f1f5f9;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#cbd5e1;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="corridorWall" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="doorGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#92400e;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#78350f;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="640" height="480" fill="#1e293b"/>
  
  <!-- 走廊透视 -->
  <!-- 地板 -->
  <polygon points="0,480 640,480 400,240 240,240" fill="url(#corridorFloor)"/>
  
  <!-- 左墙 -->
  <polygon points="0,0 240,240 240,480 0,480" fill="url(#corridorWall)"/>
  
  <!-- 右墙 -->
  <polygon points="640,0 400,240 400,480 640,480" fill="url(#corridorWall)"/>
  
  <!-- 天花板 -->
  <polygon points="0,0 640,0 400,240 240,240" fill="#f8fafc"/>
  
  <!-- 门 -->
  <rect x="260" y="260" width="40" height="80" fill="url(#doorGrad)"/>
  <rect x="340" y="260" width="40" height="80" fill="url(#doorGrad)"/>
  
  <!-- 门把手 -->
  <circle cx="295" cy="300" r="3" fill="#fbbf24"/>
  <circle cx="345" cy="300" r="3" fill="#fbbf24"/>
  
  <!-- 天花板灯光 -->
  <ellipse cx="320" cy="200" rx="30" ry="10" fill="#fef3c7" opacity="0.8"/>
  <rect x="310" y="195" width="20" height="10" fill="#f3f4f6" rx="2"/>
  
  <!-- 地板瓷砖线条 -->
  <line x1="0" y1="400" x2="640" y2="400" stroke="#94a3b8" stroke-width="1" opacity="0.5"/>
  <line x1="0" y1="440" x2="640" y2="440" stroke="#94a3b8" stroke-width="1" opacity="0.5"/>
  
  <!-- 人物剪影 -->
  <ellipse cx="320" cy="350" rx="10" ry="18" fill="#374151"/>
  <circle cx="320" cy="325" r="7" fill="#374151"/>
  
  <!-- 时间戳 -->
  <rect x="10" y="10" width="150" height="25" fill="rgba(0,0,0,0.8)" rx="3"/>
  <text x="15" y="27" fill="white" font-family="monospace" font-size="12">2024-07-29 14:30:25</text>
  
  <!-- 摄像头信息 -->
  <rect x="10" y="440" width="120" height="25" fill="rgba(0,0,0,0.8)" rx="3"/>
  <text x="15" y="457" fill="white" font-family="monospace" font-size="12">走廊摄像头01</text>
  
  <!-- 录制指示器 -->
  <circle cx="600" cy="25" r="8" fill="#ef4444"/>
  <text x="580" y="30" fill="white" font-family="monospace" font-size="10">REC</text>
</svg>
