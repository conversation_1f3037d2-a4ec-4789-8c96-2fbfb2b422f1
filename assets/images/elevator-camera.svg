<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 480">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="elevatorWall" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f8fafc;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e2e8f0;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="elevatorFloor" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#cbd5e1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#94a3b8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="buttonPanel" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#475569;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#334155;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="640" height="480" fill="#0f172a"/>
  
  <!-- 电梯内部透视 -->
  <!-- 左墙 -->
  <polygon points="0,0 200,100 200,380 0,480" fill="url(#elevatorWall)"/>
  <!-- 右墙 -->
  <polygon points="640,0 440,100 440,380 640,480" fill="url(#elevatorWall)"/>
  <!-- 后墙 -->
  <rect x="200" y="100" width="240" height="280" fill="#f1f5f9"/>
  <!-- 地板 -->
  <polygon points="0,480 200,380 440,380 640,480" fill="url(#elevatorFloor)"/>
  
  <!-- 按钮面板 -->
  <rect x="350" y="150" width="60" height="120" fill="url(#buttonPanel)" rx="5"/>
  
  <!-- 按钮 -->
  <circle cx="370" cy="170" r="6" fill="#64748b"/>
  <circle cx="390" cy="170" r="6" fill="#64748b"/>
  <circle cx="370" cy="190" r="6" fill="#64748b"/>
  <circle cx="390" cy="190" r="6" fill="#64748b"/>
  <circle cx="370" cy="210" r="6" fill="#64748b"/>
  <circle cx="390" cy="210" r="6" fill="#64748b"/>
  
  <!-- 楼层显示 -->
  <rect x="360" y="130" width="40" height="15" fill="#000" rx="2"/>
  <text x="375" y="142" fill="#00ff00" font-family="monospace" font-size="12" text-anchor="middle">12</text>
  
  <!-- 扶手 -->
  <rect x="210" y="200" width="220" height="8" fill="#94a3b8" rx="4"/>
  
  <!-- 人物剪影 -->
  <ellipse cx="280" cy="340" rx="12" ry="20" fill="#1e293b"/>
  <circle cx="280" cy="315" r="8" fill="#1e293b"/>
  
  <!-- 时间戳 -->
  <rect x="10" y="10" width="150" height="25" fill="rgba(0,0,0,0.8)" rx="3"/>
  <text x="15" y="27" fill="white" font-family="monospace" font-size="12">2024-07-29 14:30:25</text>
  
  <!-- 摄像头信息 -->
  <rect x="10" y="440" width="120" height="25" fill="rgba(0,0,0,0.8)" rx="3"/>
  <text x="15" y="457" fill="white" font-family="monospace" font-size="12">电梯摄像头01</text>
  
  <!-- 录制指示器 -->
  <circle cx="600" cy="25" r="8" fill="#ef4444"/>
  <text x="580" y="30" fill="white" font-family="monospace" font-size="10">REC</text>
</svg>
