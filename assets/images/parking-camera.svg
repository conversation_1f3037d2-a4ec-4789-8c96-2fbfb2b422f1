<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 480">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="asphalt" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4b5563;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#374151;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="carBody" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#1e40af;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1e3a8a;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="carBody2" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#dc2626;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#b91c1c;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="building" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#e5e7eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9ca3af;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="640" height="480" fill="#111827"/>
  
  <!-- 停车场地面 -->
  <rect x="0" y="200" width="640" height="280" fill="url(#asphalt)"/>
  
  <!-- 远处建筑 -->
  <rect x="0" y="50" width="640" height="150" fill="url(#building)"/>
  
  <!-- 停车位线条 -->
  <line x1="0" y1="300" x2="640" y2="300" stroke="white" stroke-width="2"/>
  <line x1="0" y1="400" x2="640" y2="400" stroke="white" stroke-width="2"/>
  
  <!-- 垂直停车位线 -->
  <line x1="120" y1="200" x2="120" y2="480" stroke="white" stroke-width="2"/>
  <line x1="240" y1="200" x2="240" y2="480" stroke="white" stroke-width="2"/>
  <line x1="360" y1="200" x2="360" y2="480" stroke="white" stroke-width="2"/>
  <line x1="480" y1="200" x2="480" y2="480" stroke="white" stroke-width="2"/>
  
  <!-- 汽车1 -->
  <rect x="130" y="320" width="100" height="60" fill="url(#carBody)" rx="8"/>
  <rect x="135" y="325" width="90" height="50" fill="#1e3a8a" rx="5"/>
  <!-- 车窗 -->
  <rect x="145" y="335" width="70" height="30" fill="#1f2937" rx="3"/>
  <!-- 车轮 -->
  <circle cx="150" cy="390" r="12" fill="#111827"/>
  <circle cx="210" cy="390" r="12" fill="#111827"/>
  
  <!-- 汽车2 -->
  <rect x="370" y="220" width="100" height="60" fill="url(#carBody2)" rx="8"/>
  <rect x="375" y="225" width="90" height="50" fill="#b91c1c" rx="5"/>
  <!-- 车窗 -->
  <rect x="385" y="235" width="70" height="30" fill="#1f2937" rx="3"/>
  <!-- 车轮 -->
  <circle cx="390" cy="290" r="12" fill="#111827"/>
  <circle cx="450" cy="290" r="12" fill="#111827"/>
  
  <!-- 路灯 -->
  <rect x="295" y="180" width="8" height="120" fill="#6b7280"/>
  <ellipse cx="299" cy="175" rx="15" ry="8" fill="#fbbf24" opacity="0.7"/>
  
  <!-- 建筑窗户 -->
  <rect x="50" y="80" width="20" height="30" fill="#fef3c7"/>
  <rect x="100" y="80" width="20" height="30" fill="#fef3c7"/>
  <rect x="150" y="80" width="20" height="30" fill="#fef3c7"/>
  <rect x="450" y="80" width="20" height="30" fill="#fef3c7"/>
  <rect x="500" y="80" width="20" height="30" fill="#fef3c7"/>
  
  <!-- 时间戳 -->
  <rect x="10" y="10" width="150" height="25" fill="rgba(0,0,0,0.8)" rx="3"/>
  <text x="15" y="27" fill="white" font-family="monospace" font-size="12">2024-07-29 14:30:25</text>
  
  <!-- 摄像头信息 -->
  <rect x="10" y="440" width="120" height="25" fill="rgba(0,0,0,0.8)" rx="3"/>
  <text x="15" y="457" fill="white" font-family="monospace" font-size="12">停车场摄像头</text>
  
  <!-- 录制指示器 -->
  <circle cx="600" cy="25" r="8" fill="#ef4444"/>
  <text x="580" y="30" fill="white" font-family="monospace" font-size="10">REC</text>
</svg>
