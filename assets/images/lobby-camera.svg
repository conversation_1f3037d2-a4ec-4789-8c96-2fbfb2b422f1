<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 480">
  <defs>
    <!-- 渐变定义 -->
    <linearGradient id="floorGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#e5e7eb;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#9ca3af;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="wallGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#f3f4f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#d1d5db;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="counterGrad" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#8b5cf6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#6366f1;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景 -->
  <rect width="640" height="480" fill="#1f2937"/>
  
  <!-- 地板 -->
  <polygon points="0,480 640,480 540,320 100,320" fill="url(#floorGrad)"/>
  
  <!-- 后墙 -->
  <rect x="100" y="80" width="440" height="240" fill="url(#wallGrad)"/>
  
  <!-- 服务台 -->
  <rect x="250" y="280" width="140" height="80" fill="url(#counterGrad)"/>
  <rect x="250" y="270" width="140" height="10" fill="#a855f7"/>
  
  <!-- 柱子 -->
  <rect x="150" y="120" width="30" height="200" fill="#6b7280"/>
  <rect x="460" y="120" width="30" height="200" fill="#6b7280"/>
  
  <!-- 人物剪影 -->
  <ellipse cx="200" cy="300" rx="8" ry="15" fill="#374151"/>
  <circle cx="200" cy="285" r="6" fill="#374151"/>
  
  <ellipse cx="450" cy="310" rx="8" ry="15" fill="#374151"/>
  <circle cx="450" cy="295" r="6" fill="#374151"/>
  
  <!-- 时间戳 -->
  <rect x="10" y="10" width="150" height="25" fill="rgba(0,0,0,0.7)" rx="3"/>
  <text x="15" y="27" fill="white" font-family="monospace" font-size="12">2024-07-29 14:30:25</text>
  
  <!-- 摄像头信息 -->
  <rect x="10" y="440" width="120" height="25" fill="rgba(0,0,0,0.7)" rx="3"/>
  <text x="15" y="457" fill="white" font-family="monospace" font-size="12">大厅摄像头01</text>
  
  <!-- 录制指示器 -->
  <circle cx="600" cy="25" r="8" fill="#ef4444"/>
  <text x="580" y="30" fill="white" font-family="monospace" font-size="10">REC</text>
</svg>
