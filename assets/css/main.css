/* 智慧楼宇管理平台 - 主样式文件 */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #1e3a8a;
  --primary-light: #3b82f6;
  --primary-dark: #1e40af;
  
  /* 辅助色 */
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --info-color: #06b6d4;
  
  /* 中性色 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 字体 */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  --spacing-2xl: 48px;
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  
  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  
  /* 侧边栏宽度 */
  --sidebar-width: 280px;
  --sidebar-collapsed-width: 80px;
  
  /* 头部高度 */
  --header-height: 64px;
}

/* 基础重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 1.5;
  color: var(--gray-700);
  background-color: var(--gray-50);
  overflow-x: hidden;
}

/* 布局容器 */
.app-container {
  display: flex;
  min-height: 100vh;
}

/* 侧边栏样式 */
.sidebar {
  width: var(--sidebar-width);
  background: linear-gradient(180deg, var(--primary-color) 0%, var(--primary-dark) 100%);
  color: white;
  position: fixed;
  left: 0;
  top: 0;
  height: 100vh;
  z-index: 1000;
  transition: all 0.3s ease;
  overflow-y: auto;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.sidebar-logo {
  width: 32px;
  height: 32px;
  background: white;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--primary-color);
  font-weight: bold;
  font-size: 18px;
}

.sidebar-title {
  font-size: 18px;
  font-weight: 600;
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .sidebar-title {
  opacity: 0;
}

/* 导航菜单 */
.nav-menu {
  padding: var(--spacing-md) 0;
}

.nav-group {
  margin-bottom: var(--spacing-lg);
}

.nav-group-title {
  padding: var(--spacing-sm) var(--spacing-lg);
  font-size: 12px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: var(--spacing-sm);
}

.nav-item {
  display: block;
  padding: var(--spacing-md) var(--spacing-lg);
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  position: relative;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
}

.nav-item.active {
  background: rgba(255, 255, 255, 0.15);
  color: white;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: white;
}

.nav-icon {
  width: 20px;
  text-align: center;
  flex-shrink: 0;
}

.nav-text {
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.sidebar.collapsed .nav-text {
  opacity: 0;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  margin-left: var(--sidebar-width);
  transition: margin-left 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed + .main-content {
  margin-left: var(--sidebar-collapsed-width);
}

/* 头部导航 */
.header {
  height: var(--header-height);
  background: white;
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.sidebar-toggle {
  background: none;
  border: none;
  color: var(--gray-600);
  font-size: 18px;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.sidebar-toggle:hover {
  background: var(--gray-100);
  color: var(--gray-800);
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  color: var(--gray-500);
  font-size: 14px;
}

.breadcrumb-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.breadcrumb-separator {
  color: var(--gray-400);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-action {
  background: none;
  border: none;
  color: var(--gray-600);
  font-size: 18px;
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all 0.3s ease;
}

.header-action:hover {
  background: var(--gray-100);
  color: var(--gray-800);
}

.header-action .badge {
  position: absolute;
  top: 2px;
  right: 2px;
  background: var(--danger-color);
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.user-menu:hover {
  background: var(--gray-100);
}

.user-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 500;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-800);
}

.user-role {
  font-size: 12px;
  color: var(--gray-500);
}

/* 页面内容 */
.page-content {
  flex: 1;
  padding: var(--spacing-lg);
  overflow-y: auto;
}

.page-header {
  margin-bottom: var(--spacing-lg);
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
}

.page-description {
  color: var(--gray-600);
  font-size: 14px;
}

/* 卡片组件 */
.card {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  overflow: hidden;
}

.card-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-900);
}

.card-body {
  padding: var(--spacing-lg);
}

.card-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* 按钮组件 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid transparent;
  border-radius: var(--radius-md);
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.btn-primary {
  background: var(--primary-color);
  color: white;
}

.btn-primary:hover {
  background: var(--primary-dark);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border-color: var(--gray-300);
}

.btn-secondary:hover {
  background: var(--gray-200);
}

.btn-success {
  background: var(--success-color);
  color: white;
}

.btn-warning {
  background: var(--warning-color);
  color: white;
}

.btn-danger {
  background: var(--danger-color);
  color: white;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

.btn-lg {
  padding: 12px 24px;
  font-size: 16px;
}

/* 表格组件 */
.table-container {
  overflow-x: auto;
}

.table {
  width: 100%;
  border-collapse: collapse;
  background: white;
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
}

.table th {
  background: var(--gray-50);
  font-weight: 600;
  color: var(--gray-900);
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.table tbody tr:hover {
  background: var(--gray-50);
}

/* 状态标签 */
.status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-badge.danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.status-badge.info {
  background: rgba(6, 182, 212, 0.1);
  color: var(--info-color);
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
  }

  .dashboard-grid {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  }
}

@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
  }

  .page-header {
    text-align: center;
    margin-bottom: var(--spacing-lg);
  }

  .card-header {
    flex-direction: column;
    gap: var(--spacing-sm);
    align-items: stretch;
  }

  .card-header .d-flex {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .sidebar {
    transform: translateX(-100%);
  }

  .sidebar.mobile-open {
    transform: translateX(0);
  }

  .main-content {
    margin-left: 0;
  }

  .page-content {
    padding: var(--spacing-md);
  }

  .stats-grid {
    grid-template-columns: 1fr;
    gap: var(--spacing-md);
  }

  .stat-card {
    padding: var(--spacing-lg);
  }

  .stat-value {
    font-size: 28px;
  }

  .stat-icon {
    width: 48px;
    height: 48px;
    font-size: 20px;
  }

  .page-title {
    font-size: 24px;
  }

  .page-description {
    font-size: 14px;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .card-header .btn {
    width: auto;
  }
}

@media (max-width: 480px) {
  .page-content {
    padding: var(--spacing-sm);
  }

  .stat-card {
    padding: var(--spacing-md);
  }

  .stat-header {
    gap: var(--spacing-md);
  }

  .stat-value {
    font-size: 24px;
  }

  .stat-icon {
    width: 40px;
    height: 40px;
    font-size: 18px;
  }

  .card {
    margin-bottom: var(--spacing-md);
  }

  .page-title {
    font-size: 20px;
  }
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.gap-sm { gap: var(--spacing-sm); }
.gap-md { gap: var(--spacing-md); }
.gap-lg { gap: var(--spacing-lg); }

.mb-0 { margin-bottom: 0; }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }

.mt-0 { margin-top: 0; }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }

.p-0 { padding: 0; }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }

.w-full { width: 100%; }
.h-full { height: 100%; }

.text-sm { font-size: 12px; }
.text-base { font-size: 14px; }
.text-lg { font-size: 16px; }
.text-xl { font-size: 18px; }
.text-2xl { font-size: 24px; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-gray-500 { color: var(--gray-500); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-800 { color: var(--gray-800); }
.text-gray-900 { color: var(--gray-900); }

.text-primary { color: var(--primary-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-danger { color: var(--danger-color); }

/* 统计卡片组件 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
}

.stat-card {
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-200);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--primary-color);
}

.stat-card.primary::before { background: var(--primary-color); }
.stat-card.success::before { background: var(--success-color); }
.stat-card.warning::before { background: var(--warning-color); }
.stat-card.danger::before { background: var(--danger-color); }
.stat-card.info::before { background: var(--info-color); }

.stat-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--spacing-lg);
}

.stat-content {
  flex: 1;
  min-width: 0;
}

.stat-value {
  font-size: 36px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-600);
  margin-bottom: var(--spacing-sm);
}

.stat-change {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 500;
}

.stat-change.positive {
  color: var(--success-color);
}

.stat-change.negative {
  color: var(--danger-color);
}

.stat-change.neutral {
  color: var(--gray-500);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
  flex-shrink: 0;
}

.stat-icon.primary { background: var(--primary-color); }
.stat-icon.success { background: var(--success-color); }
.stat-icon.warning { background: var(--warning-color); }
.stat-icon.danger { background: var(--danger-color); }
.stat-icon.info { background: var(--info-color); }

/* 状态徽章统一样式 */
.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  display: inline-block;
}

.status-badge.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-badge.danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.status-badge.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

.status-badge.primary {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-color);
}

/* 表格样式优化 */
.table {
  width: 100%;
  border-collapse: collapse;
  margin-top: var(--spacing-md);
}

.table th,
.table td {
  padding: var(--spacing-md);
  text-align: left;
  border-bottom: 1px solid var(--gray-200);
  font-size: 14px;
}

.table th {
  background: var(--gray-50);
  font-weight: 600;
  color: var(--gray-700);
}

.table tbody tr:hover {
  background: var(--gray-50);
}

/* 工具提示样式 */
.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip:hover::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gray-900);
  color: white;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  font-size: 12px;
  white-space: nowrap;
  z-index: 1000;
}

/* 人员布控系统专用样式 */
.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-200);
}

.avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* 告警列表样式 */
.alert-list {
  max-height: 400px;
  overflow-y: auto;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  transition: background-color 0.2s ease;
}

.alert-item:hover {
  background: var(--gray-50);
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  flex-shrink: 0;
}

.alert-icon.danger {
  background: var(--danger-color);
}

.alert-icon.warning {
  background: var(--warning-color);
}

.alert-icon.info {
  background: var(--info-color);
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.alert-desc {
  color: var(--gray-600);
  font-size: 14px;
  margin-bottom: var(--spacing-xs);
}

.alert-time {
  color: var(--gray-500);
  font-size: 12px;
}

.alert-actions {
  flex-shrink: 0;
}

/* 抓拍记录网格 */
.capture-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.capture-item {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: all 0.3s ease;
}

.capture-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.capture-image {
  position: relative;
  width: 100%;
  height: 120px;
  overflow: hidden;
}

.capture-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.capture-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.capture-item:hover .capture-overlay {
  opacity: 1;
}

.capture-info {
  padding: var(--spacing-md);
}

.capture-person {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.capture-location {
  color: var(--gray-600);
  font-size: 14px;
  margin-bottom: var(--spacing-xs);
}

.capture-time {
  color: var(--gray-500);
  font-size: 12px;
  margin-bottom: var(--spacing-xs);
}

.capture-confidence {
  color: var(--primary-color);
  font-size: 12px;
  font-weight: 500;
}

/* 轨迹时间线样式 */
.track-timeline {
  position: relative;
  padding-left: var(--spacing-lg);
}

.track-timeline::before {
  content: '';
  position: absolute;
  left: 15px;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--gray-300);
}

.timeline-item {
  position: relative;
  margin-bottom: var(--spacing-lg);
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  left: -23px;
  top: 4px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 3px solid white;
  box-shadow: 0 0 0 1px var(--gray-300);
}

.timeline-marker.success {
  background: var(--success-color);
}

.timeline-marker.warning {
  background: var(--warning-color);
}

.timeline-marker.info {
  background: var(--info-color);
}

.timeline-marker.danger {
  background: var(--danger-color);
}

.timeline-content {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.timeline-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.timeline-location {
  color: var(--gray-600);
  font-size: 14px;
  margin-bottom: var(--spacing-xs);
}

.timeline-time {
  color: var(--gray-500);
  font-size: 12px;
}

/* 人脸检索上传区域 */
.face-search-container {
  max-width: 100%;
}

.upload-area {
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.upload-content i {
  font-size: 48px;
  color: var(--gray-400);
  margin-bottom: var(--spacing-md);
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
}

.upload-hint {
  font-size: 14px;
  color: var(--gray-500);
}

.upload-area-small {
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--gray-50);
}

.upload-area-small:hover {
  border-color: var(--primary-color);
  background: rgba(59, 130, 246, 0.05);
}

.upload-area-small i {
  font-size: 24px;
  color: var(--gray-400);
  margin-bottom: var(--spacing-sm);
  display: block;
}

/* 策略配置样式 */
.strategy-config {
  display: grid;
  gap: var(--spacing-lg);
}

.config-section {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
}

.config-section h5 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
}

/* 复选框样式 */
.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  font-size: 14px;
  color: var(--gray-700);
  margin-bottom: var(--spacing-sm);
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  margin-right: var(--spacing-sm);
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

/* 范围滑块样式 */
.form-range {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--gray-200);
  outline: none;
  -webkit-appearance: none;
  margin-bottom: var(--spacing-sm);
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.form-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.range-value {
  text-align: center;
  font-weight: 600;
  color: var(--primary-color);
}

/* 模态框样式 */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg);
}

.modal-content {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-width: 600px;
  width: 100%;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease;
}

.modal-content.modal-lg {
  max-width: 800px;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
  margin: 0;
  color: var(--gray-900);
}

.modal-close {
  background: none;
  border: none;
  font-size: 20px;
  color: var(--gray-500);
  cursor: pointer;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: var(--spacing-md);
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

/* 人员详情样式 */
.person-detail-container {
  display: grid;
  gap: var(--spacing-lg);
}

.person-basic-info {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.person-photo {
  flex-shrink: 0;
}

.person-photo img {
  width: 120px;
  height: 150px;
  object-fit: cover;
  border-radius: var(--radius-md);
  border: 3px solid white;
  box-shadow: var(--shadow-sm);
}

.person-info {
  flex: 1;
}

.person-info h4 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.info-item label {
  min-width: 80px;
  font-weight: 500;
  color: var(--gray-600);
  margin-right: var(--spacing-sm);
}

.info-item span {
  color: var(--gray-900);
}

.person-records h5 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
}

.record-list {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.record-item {
  display: grid;
  grid-template-columns: 100px 1fr 80px 80px;
  gap: var(--spacing-md);
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  transition: background-color 0.2s ease;
}

.record-item:hover {
  background: var(--gray-50);
}

.record-item:last-child {
  border-bottom: none;
}

.record-time {
  font-weight: 500;
  color: var(--gray-900);
}

.record-location {
  color: var(--gray-600);
}

.record-confidence {
  color: var(--primary-color);
  font-weight: 500;
}

/* 分页样式 */
.pagination {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.pagination .btn {
  min-width: 36px;
  height: 36px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  z-index: 1100;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  border-left: 4px solid var(--primary-color);
}

.notification.show {
  transform: translateX(0);
}

.notification-success {
  border-left-color: var(--success-color);
}

.notification-error {
  border-left-color: var(--danger-color);
}

.notification-warning {
  border-left-color: var(--warning-color);
}

.notification i {
  font-size: 16px;
}

.notification-success i {
  color: var(--success-color);
}

.notification-error i {
  color: var(--danger-color);
}

.notification-warning i {
  color: var(--warning-color);
}

.notification-info i {
  color: var(--info-color);
}

/* 楼宇管理系统专用样式 */
.building-tree {
  max-height: 600px;
  overflow-y: auto;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.tree-node {
  margin-bottom: var(--spacing-sm);
}

.tree-node:last-child {
  margin-bottom: 0;
}

.tree-node-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.tree-node-header:hover {
  background: var(--gray-50);
}

.building-node > .tree-node-header {
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
  font-weight: 600;
}

.floor-node > .tree-node-header {
  background: rgba(16, 185, 129, 0.05);
  border: 1px solid rgba(16, 185, 129, 0.2);
  margin-left: var(--spacing-lg);
}

.room-node > .tree-node-header {
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.2);
  margin-left: calc(var(--spacing-lg) * 2);
}

.tree-toggle {
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  transition: transform 0.3s ease;
}

.tree-icon {
  color: var(--primary-color);
  font-size: 16px;
}

.floor-node .tree-icon {
  color: var(--success-color);
}

.room-node .tree-icon {
  color: var(--warning-color);
}

.tree-label {
  flex: 1;
  font-size: 14px;
  color: var(--gray-900);
}

.tree-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tree-node-header:hover .tree-actions {
  opacity: 1;
}

.tree-children {
  margin-left: var(--spacing-md);
  margin-top: var(--spacing-sm);
}

.room-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  margin-left: auto;
  margin-right: var(--spacing-sm);
}

.room-status.active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.room-status.maintenance {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.room-status.inactive {
  background: rgba(107, 114, 128, 0.1);
  color: var(--gray-500);
}

/* 二维码管理样式 */
.qr-management {
  max-height: 600px;
  overflow-y: auto;
}

.qr-stats {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.qr-stat-item {
  text-align: center;
  flex: 1;
}

.qr-stat-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.qr-stat-label {
  font-size: 12px;
  color: var(--gray-600);
}

.qr-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.qr-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.qr-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.qr-code {
  flex-shrink: 0;
}

.qr-code img {
  width: 60px;
  height: 60px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

.qr-info {
  flex: 1;
}

.qr-location {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.qr-desc {
  color: var(--gray-600);
  font-size: 14px;
  margin-bottom: var(--spacing-xs);
}

.qr-time {
  color: var(--gray-500);
  font-size: 12px;
}

.qr-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 二维码预览样式 */
.qr-preview-container {
  text-align: center;
}

.qr-preview-image img {
  width: 200px;
  height: 200px;
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
}

.qr-preview-info h4 {
  margin: 0 0 var(--spacing-sm) 0;
  color: var(--gray-900);
}

.qr-preview-info p {
  color: var(--gray-600);
  margin-bottom: var(--spacing-md);
}

.qr-preview-details {
  text-align: left;
  background: var(--gray-50);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item label {
  font-weight: 500;
  color: var(--gray-700);
}

.detail-item span {
  color: var(--gray-900);
}

/* 设备绑定管理样式 */
.device-binding-container {
  max-height: 600px;
  overflow-y: auto;
}

.binding-stats {
  display: flex;
  gap: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.binding-stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  flex: 1;
  padding: var(--spacing-md);
  background: white;
  border-radius: var(--radius-md);
  border-left: 4px solid var(--primary-color);
}

.binding-stat-item.success {
  border-left-color: var(--success-color);
}

.binding-stat-item.warning {
  border-left-color: var(--warning-color);
}

.binding-stat-item.danger {
  border-left-color: var(--danger-color);
}

.binding-stat-item i {
  font-size: 24px;
  color: var(--primary-color);
}

.binding-stat-item.success i {
  color: var(--success-color);
}

.binding-stat-item.warning i {
  color: var(--warning-color);
}

.binding-stat-item.danger i {
  color: var(--danger-color);
}

.binding-stat-info {
  flex: 1;
}

.binding-stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.binding-stat-label {
  font-size: 12px;
  color: var(--gray-600);
}

/* 资源管理样式 */
.resource-management {
  max-height: 600px;
  overflow-y: auto;
}

.resource-categories {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-lg);
  flex-wrap: wrap;
}

.resource-categories .btn {
  white-space: nowrap;
}

.resource-categories .btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.resource-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-md);
}

.resource-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  background: white;
}

.resource-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
  transform: translateY(-2px);
}

.resource-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  background: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--primary-color);
  flex-shrink: 0;
}

.resource-info {
  flex: 1;
}

.resource-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.resource-location {
  color: var(--gray-600);
  font-size: 14px;
  margin-bottom: var(--spacing-xs);
}

.resource-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.resource-count {
  font-size: 12px;
  color: var(--gray-500);
}

.resource-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.resource-item:hover .resource-actions {
  opacity: 1;
}

/* 维保工单系统专用样式 - 紧凑查询布局 */
.workorder-filters-compact {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
}

.filter-row-compact {
  display: flex;
  align-items: end;
  gap: var(--spacing-sm);
  flex-wrap: nowrap;
}

.filter-item {
  display: flex;
  flex-direction: column;
  min-width: 120px;
  flex: 1;
}

.filter-item label {
  font-size: 13px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: 4px;
  white-space: nowrap;
}

.form-select-compact {
  height: 36px;
  padding: 6px 12px;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  font-size: 14px;
  color: var(--gray-900);
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-select-compact:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.form-select-compact:hover {
  border-color: var(--gray-400);
}

.filter-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  margin-left: var(--spacing-sm);
}

.btn-search,
.btn-reset {
  height: 36px;
  padding: 6px 16px;
  border: none;
  border-radius: var(--radius-sm);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 6px;
  white-space: nowrap;
}

.btn-search {
  background: var(--primary-color);
  color: white;
}

.btn-search:hover {
  background: #4338ca;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

.btn-reset {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-300);
}

.btn-reset:hover {
  background: var(--gray-200);
  border-color: var(--gray-400);
}

/* 工单列表样式优化 */
.workorder-item {
  transition: all 0.3s ease;
}

.workorder-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.workorder-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.workorder-title {
  font-weight: 600;
  color: var(--gray-900);
  margin: 0;
}

.workorder-status {
  font-size: 12px;
  padding: 4px 12px;
  border-radius: 12px;
  font-weight: 500;
}

.workorder-content {
  color: var(--gray-600);
  font-size: 14px;
  line-height: 1.5;
  margin-bottom: var(--spacing-md);
}

.workorder-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.workorder-meta-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--gray-500);
}

.workorder-meta-item i {
  width: 14px;
  text-align: center;
}

.workorder-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
}

/* 响应式优化 - 维保工单紧凑布局 */
@media (max-width: 1200px) {
  .filter-row-compact {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .filter-item {
    min-width: 140px;
  }

  .filter-actions {
    margin-left: 0;
    margin-top: var(--spacing-sm);
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .workorder-filters-compact {
    padding: var(--spacing-sm);
  }

  .filter-row-compact {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-md);
  }

  .filter-item {
    min-width: auto;
    flex: none;
  }

  .filter-actions {
    margin-left: 0;
    margin-top: 0;
    justify-content: center;
  }

  .btn-search,
  .btn-reset {
    flex: 1;
    justify-content: center;
  }

  .workorder-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .workorder-actions {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .filter-actions {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .btn-search,
  .btn-reset {
    width: 100%;
  }
}

/* 动环监控系统专用样式 */
.environment-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-lg);
}

.env-item {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  padding: var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.env-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.env-item.temperature {
  border-left: 4px solid #ef4444;
}

.env-item.humidity {
  border-left: 4px solid #3b82f6;
}

.env-item.pressure {
  border-left: 4px solid #10b981;
}

.env-item.air-quality {
  border-left: 4px solid #8b5cf6;
}

.env-item.noise {
  border-left: 4px solid #f59e0b;
}

.env-item.light {
  border-left: 4px solid #06b6d4;
}

.env-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  background: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: var(--primary-color);
  flex-shrink: 0;
}

.temperature .env-icon {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
}

.humidity .env-icon {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.pressure .env-icon {
  background: rgba(16, 185, 129, 0.1);
  color: #10b981;
}

.air-quality .env-icon {
  background: rgba(139, 92, 246, 0.1);
  color: #8b5cf6;
}

.noise .env-icon {
  background: rgba(245, 158, 11, 0.1);
  color: #f59e0b;
}

.light .env-icon {
  background: rgba(6, 182, 212, 0.1);
  color: #06b6d4;
}

.env-info {
  flex: 1;
}

.env-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.env-label {
  font-size: 14px;
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.env-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  display: inline-block;
}

.env-status.normal {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.env-status.good {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
}

.env-status.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.env-status.danger {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.env-chart {
  width: 100px;
  height: 60px;
  flex-shrink: 0;
}

.env-chart canvas {
  width: 100%;
  height: 100%;
}

/* 设备状态监控样式 */
.device-status-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.device-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.device-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.device-item.online {
  border-left: 4px solid var(--success-color);
}

.device-item.warning {
  border-left: 4px solid var(--warning-color);
}

.device-item.offline {
  border-left: 4px solid var(--danger-color);
}

.device-info {
  flex: 1;
}

.device-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.device-location {
  font-size: 14px;
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.device-id {
  font-size: 12px;
  color: var(--gray-500);
  font-family: monospace;
}

.device-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.device-signal {
  font-size: 12px;
  color: var(--gray-500);
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.device-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 告警管理样式 */
.alarm-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.alarm-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  position: relative;
}

.alarm-item:hover {
  box-shadow: var(--shadow-sm);
}

.alarm-item.high {
  border-left: 4px solid var(--danger-color);
  background: rgba(239, 68, 68, 0.02);
}

.alarm-item.medium {
  border-left: 4px solid var(--warning-color);
  background: rgba(245, 158, 11, 0.02);
}

.alarm-item.low {
  border-left: 4px solid var(--info-color);
  background: rgba(59, 130, 246, 0.02);
}

.alarm-item.resolved {
  opacity: 0.6;
  border-left-color: var(--gray-400);
}

.alarm-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  background: var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  color: var(--gray-600);
  flex-shrink: 0;
}

.alarm-item.high .alarm-icon {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.alarm-item.medium .alarm-icon {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.alarm-item.low .alarm-icon {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.alarm-content {
  flex: 1;
}

.alarm-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.alarm-desc {
  font-size: 14px;
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.alarm-time {
  font-size: 12px;
  color: var(--gray-500);
}

.alarm-level {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  text-align: center;
  min-width: 40px;
}

.alarm-level.high {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.alarm-level.medium {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.alarm-level.low {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.alarm-status {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 12px;
  font-weight: 500;
  text-align: center;
}

.alarm-status.resolved {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.alarm-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 历史数据图表样式 */
.chart-container {
  position: relative;
  height: 200px;
  margin-bottom: var(--spacing-md);
}

.chart-container canvas {
  width: 100%;
  height: 100%;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 14px;
  color: var(--gray-700);
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
}

.legend-color.temperature {
  background: #ef4444;
}

.legend-color.humidity {
  background: #3b82f6;
}

.legend-color.pressure {
  background: #10b981;
}

.legend-color.noise {
  background: #f59e0b;
}

/* 环境参数配置样式 */
.config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: var(--spacing-lg);
}

.config-section {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.config-section h5 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: var(--spacing-sm);
}

.config-item {
  margin-bottom: var(--spacing-md);
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-item label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--spacing-xs);
}

.range-input {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.range-input input {
  flex: 1;
  min-width: 60px;
}

.range-input span {
  font-size: 14px;
  color: var(--gray-600);
  white-space: nowrap;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: 14px;
  color: var(--gray-700);
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
}

.checkmark {
  position: relative;
}

/* 设备控制中心样式 */
.control-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.control-item {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all 0.3s ease;
}

.control-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.control-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.control-name {
  font-weight: 600;
  color: var(--gray-900);
}

.control-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.control-status.online {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.control-status.offline {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.control-info {
  text-align: center;
  margin-bottom: var(--spacing-md);
}

.control-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.control-label {
  font-size: 12px;
  color: var(--gray-600);
}

.control-actions {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xs);
}

/* 响应式优化 - 动环监控系统 */
@media (max-width: 1200px) {
  .environment-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--spacing-md);
  }

  .config-grid {
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  }

  .control-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .environment-grid {
    grid-template-columns: 1fr;
  }

  .env-item {
    flex-direction: column;
    text-align: center;
    padding: var(--spacing-md);
  }

  .env-chart {
    width: 100%;
    max-width: 200px;
  }

  .device-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .device-actions {
    justify-content: center;
  }

  .alarm-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .alarm-actions {
    justify-content: center;
  }

  .chart-legend {
    gap: var(--spacing-md);
  }

  .config-grid {
    grid-template-columns: 1fr;
  }

  .range-input {
    flex-wrap: wrap;
    justify-content: center;
  }

  .control-grid {
    grid-template-columns: 1fr;
  }

  /* 楼宇管理响应式 */
  .building-tree {
    max-height: 400px;
  }

  .tree-node-header {
    padding: var(--spacing-sm);
    font-size: 14px;
  }

  .tree-actions {
    opacity: 1;
  }

  .tree-actions .btn {
    padding: 4px 8px;
    font-size: 12px;
  }

  .floor-node > .tree-node-header {
    margin-left: var(--spacing-md);
  }

  .room-node > .tree-node-header {
    margin-left: var(--spacing-lg);
  }

  .qr-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .qr-item {
    flex-direction: column;
    text-align: center;
  }

  .qr-actions {
    justify-content: center;
  }

  .binding-stats {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .resource-categories {
    justify-content: center;
  }

  .resource-grid {
    grid-template-columns: 1fr;
  }

  .resource-item {
    flex-direction: column;
    text-align: center;
  }

  .resource-actions {
    opacity: 1;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .tree-node-header {
    gap: var(--spacing-xs);
  }

  .tree-label {
    font-size: 13px;
  }

  .room-status {
    font-size: 10px;
    padding: 1px 6px;
  }

  .qr-preview-image img {
    width: 150px;
    height: 150px;
  }

  .resource-categories .btn {
    font-size: 12px;
    padding: var(--spacing-xs) var(--spacing-sm);
  }
}

/* 资产设备管理系统专用样式 */
.equipment-filters-compact {
  background: var(--gray-50);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.equipment-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 600px;
  overflow-y: auto;
}

.equipment-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  background: white;
}

.equipment-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
  transform: translateY(-1px);
}

.equipment-item.normal {
  border-left: 4px solid var(--success-color);
}

.equipment-item.maintenance {
  border-left: 4px solid var(--warning-color);
}

.equipment-item.standby {
  border-left: 4px solid var(--info-color);
}

.equipment-item.scrap {
  border-left: 4px solid var(--danger-color);
}

.equipment-info {
  flex: 1;
}

.equipment-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.equipment-name {
  font-weight: 600;
  color: var(--gray-900);
  font-size: 16px;
}

.equipment-code {
  font-family: monospace;
  font-size: 12px;
  color: var(--gray-500);
  background: var(--gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.equipment-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-sm);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 14px;
  color: var(--gray-600);
}

.detail-item i {
  width: 14px;
  text-align: center;
  color: var(--gray-500);
}

.equipment-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

/* 分页样式 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-md);
  border-top: 1px solid var(--gray-200);
}

.pagination-info {
  font-size: 14px;
  color: var(--gray-600);
}

.pagination {
  display: flex;
  gap: var(--spacing-xs);
  align-items: center;
}

.pagination span {
  color: var(--gray-500);
  font-size: 14px;
}

/* 设备分类统计样式 */
.statistics-section {
  margin-bottom: var(--spacing-lg);
}

.statistics-section:last-child {
  margin-bottom: 0;
}

.statistics-section h5 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: var(--spacing-sm);
}

.category-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.category-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.category-item:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.category-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.category-icon.hvac {
  background: linear-gradient(135deg, #06b6d4, #0891b2);
}

.category-icon.electrical {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.category-icon.security {
  background: linear-gradient(135deg, #10b981, #059669);
}

.category-icon.network {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.category-icon.elevator {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.category-icon.fire {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.category-info {
  flex: 1;
}

.category-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.category-count {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.category-percent {
  font-size: 12px;
  color: var(--gray-500);
}

/* 状态图表样式 */
.status-chart {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.status-chart canvas {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
}

.status-legend {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  flex-wrap: wrap;
}

.legend-color.normal {
  background: #10b981;
}

.legend-color.maintenance {
  background: #f59e0b;
}

.legend-color.standby {
  background: #3b82f6;
}

.legend-color.scrap {
  background: #ef4444;
}

/* 维保提醒样式 */
.maintenance-alerts {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.alert-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.alert-item.urgent {
  background: rgba(239, 68, 68, 0.05);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.alert-item.warning {
  background: rgba(245, 158, 11, 0.05);
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.alert-item.info {
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.alert-item i {
  font-size: 20px;
}

.alert-item.urgent i {
  color: var(--danger-color);
}

.alert-item.warning i {
  color: var(--warning-color);
}

.alert-item.info i {
  color: var(--info-color);
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.alert-desc {
  font-size: 14px;
  color: var(--gray-600);
}

/* 维修保养记录样式 */
.maintenance-records {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 500px;
  overflow-y: auto;
}

.record-item {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all 0.3s ease;
}

.record-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.record-item.completed {
  border-left: 4px solid var(--success-color);
}

.record-item.in-progress {
  border-left: 4px solid var(--warning-color);
}

.record-item.scheduled {
  border-left: 4px solid var(--info-color);
}

.record-item.overdue {
  border-left: 4px solid var(--danger-color);
}

.record-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.record-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 14px;
  font-weight: 500;
}

.record-type.maintenance {
  color: var(--success-color);
}

.record-type.repair {
  color: var(--warning-color);
}

.record-type.inspection {
  color: var(--info-color);
}

.record-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.record-status.completed {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.record-status.in-progress {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.record-status.scheduled {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.record-status.overdue {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.record-date {
  font-size: 12px;
  color: var(--gray-500);
  margin-left: auto;
}

.record-content {
  margin-bottom: var(--spacing-md);
}

.record-equipment {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.record-description {
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.record-technician {
  font-size: 14px;
  color: var(--gray-500);
}

.record-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
}

/* 报废流程样式 */
.scrap-process {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 500px;
  overflow-y: auto;
}

.process-item {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all 0.3s ease;
  position: relative;
}

.process-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.process-item.pending {
  border-left: 4px solid var(--warning-color);
}

.process-item.approved {
  border-left: 4px solid var(--success-color);
}

.process-item.in-disposal {
  border-left: 4px solid var(--info-color);
}

.process-item.completed {
  border-left: 4px solid var(--gray-400);
  opacity: 0.8;
}

.process-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.process-step {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--primary-color);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
}

.process-title {
  font-weight: 600;
  color: var(--gray-900);
}

.process-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
  margin-left: auto;
}

.process-status.pending {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.process-status.approved {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.process-status.in-disposal {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.process-status.completed {
  background: rgba(107, 114, 128, 0.1);
  color: var(--gray-500);
}

.process-content {
  margin-bottom: var(--spacing-md);
}

.process-equipment {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.process-reason {
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.process-info {
  font-size: 14px;
  color: var(--gray-500);
}

.process-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
}

/* 设备分类管理样式 */
.category-management {
  max-height: 500px;
  overflow-y: auto;
}

.category-tree {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.category-node {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
}

.category-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  background: var(--gray-50);
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-header:hover {
  background: var(--gray-100);
}

.category-toggle {
  width: 16px;
  height: 16px;
  color: var(--gray-500);
  transition: transform 0.3s ease;
}

.category-icon {
  color: var(--primary-color);
  font-size: 16px;
}

.category-name {
  flex: 1;
  font-weight: 600;
  color: var(--gray-900);
}

.category-count {
  font-size: 12px;
  color: var(--gray-500);
}

.category-actions {
  display: flex;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.category-header:hover .category-actions {
  opacity: 1;
}

.category-children {
  padding: var(--spacing-md);
  background: white;
  border-top: 1px solid var(--gray-200);
}

.subcategory-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  margin-bottom: var(--spacing-xs);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.subcategory-item:hover {
  background: var(--gray-50);
}

.subcategory-item:last-child {
  margin-bottom: 0;
}

.subcategory-item i {
  color: var(--gray-500);
  font-size: 14px;
}

.subcategory-item span {
  color: var(--gray-700);
  font-size: 14px;
}

/* 二维码管理样式 */
.qr-management {
  max-height: 500px;
  overflow-y: auto;
}

.qr-stats {
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.qr-stat-item {
  text-align: center;
}

.qr-stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--primary-color);
  margin-bottom: var(--spacing-xs);
}

.qr-stat-label {
  font-size: 12px;
  color: var(--gray-600);
}

.qr-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.qr-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.qr-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.qr-item.pending {
  border-left: 4px solid var(--warning-color);
}

.qr-code {
  width: 60px;
  height: 60px;
  flex-shrink: 0;
}

.qr-code img {
  width: 100%;
  height: 100%;
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

.qr-placeholder {
  width: 100%;
  height: 100%;
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: var(--gray-500);
  font-size: 12px;
}

.qr-placeholder i {
  font-size: 20px;
  margin-bottom: var(--spacing-xs);
}

.qr-info {
  flex: 1;
}

.qr-equipment {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.qr-code-id {
  font-family: monospace;
  font-size: 12px;
  color: var(--gray-500);
  margin-bottom: var(--spacing-xs);
}

.qr-generate-time {
  font-size: 12px;
  color: var(--gray-500);
}

.qr-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 响应式优化 - 资产设备管理 */
@media (max-width: 1200px) {
  .equipment-details {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }

  .category-stats {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .equipment-item {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .equipment-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .equipment-details {
    grid-template-columns: 1fr;
  }

  .equipment-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .pagination-container {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .category-stats {
    grid-template-columns: 1fr;
  }

  .status-legend {
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
  }

  .maintenance-alerts {
    gap: var(--spacing-sm);
  }

  .alert-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .record-item {
    padding: var(--spacing-sm);
  }

  .record-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .record-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .process-item {
    padding: var(--spacing-sm);
  }

  .process-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .process-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .category-header {
    padding: var(--spacing-sm);
  }

  .category-actions {
    opacity: 1;
  }

  .qr-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .qr-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .equipment-list,
  .maintenance-records,
  .scrap-process,
  .category-management,
  .qr-management {
    max-height: 400px;
  }

  .equipment-actions .btn,
  .record-actions .btn,
  .process-actions .btn,
  .qr-actions .btn {
    padding: 6px 12px;
    font-size: 12px;
  }

  .equipment-name {
    font-size: 14px;
  }

  .category-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .qr-stats {
    flex-direction: column;
    gap: var(--spacing-sm);
  }
}

/* 信息发布系统专用样式 */
.material-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.filter-tabs {
  display: flex;
  gap: var(--spacing-sm);
}

.filter-tab {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  background: white;
  color: var(--gray-700);
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
}

.filter-tab:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
}

.filter-tab.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.search-box {
  display: flex;
  gap: var(--spacing-xs);
}

.search-box input {
  min-width: 200px;
}

/* 素材网格样式 */
.material-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.material-item {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  overflow: hidden;
  transition: all 0.3s ease;
  background: white;
}

.material-item:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.material-preview {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.material-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: var(--gray-100);
  border-radius: var(--radius-sm);
}

.material-preview img:not([src]),
.material-preview img[src=""] {
  background: var(--gray-200);
  display: flex;
  align-items: center;
  justify-content: center;
}

.material-preview img:not([src])::before,
.material-preview img[src=""]::before {
  content: "图片加载中...";
  color: var(--gray-500);
  font-size: 12px;
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 100%;
}

.video-thumbnail img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: var(--gray-100);
  border-radius: var(--radius-sm);
}

.content-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: var(--gray-100);
  border-radius: var(--radius-sm);
}

.preview-screen img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  background: var(--gray-900);
}

.video-duration {
  position: absolute;
  bottom: 8px;
  right: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 12px;
}

.video-play-icon {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 40px;
  height: 40px;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.text-preview {
  width: 100%;
  height: 100%;
  padding: var(--spacing-md);
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  display: flex;
  align-items: center;
  justify-content: center;
}

.text-content h4 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--gray-900);
  font-size: 16px;
}

.text-content p {
  margin: 0;
  color: var(--gray-600);
  font-size: 14px;
}

.material-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-xs);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.material-item:hover .material-overlay {
  opacity: 1;
}

.material-info {
  padding: var(--spacing-md);
}

.material-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
  font-size: 14px;
}

.material-meta {
  display: flex;
  gap: var(--spacing-sm);
  font-size: 12px;
  color: var(--gray-500);
}

.material-type {
  background: var(--primary-color);
  color: white;
  padding: 1px 6px;
  border-radius: var(--radius-sm);
}

/* 节目编辑器样式 */
.program-editor {
  max-height: 500px;
  overflow-y: auto;
}

.current-program {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  background: var(--gray-50);
}

.program-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.program-info h5 {
  margin: 0 0 var(--spacing-xs) 0;
  color: var(--gray-900);
}

.program-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 14px;
  color: var(--gray-600);
}

.program-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 时间轴编辑器样式 */
.timeline-editor {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  margin-bottom: var(--spacing-md);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-200);
  background: var(--gray-50);
}

.timeline-header h6 {
  margin: 0;
  color: var(--gray-900);
}

.timeline-controls {
  display: flex;
  gap: var(--spacing-xs);
}

.timeline-container {
  padding: var(--spacing-md);
}

.timeline-ruler {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
  padding: 0 var(--spacing-sm);
}

.time-marker {
  font-size: 12px;
  color: var(--gray-500);
}

.timeline-tracks {
  position: relative;
}

.timeline-track {
  display: flex;
  margin-bottom: var(--spacing-md);
  min-height: 40px;
}

.track-label {
  width: 100px;
  padding: var(--spacing-sm);
  background: var(--gray-100);
  border-radius: var(--radius-sm);
  font-size: 12px;
  font-weight: 500;
  color: var(--gray-700);
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.track-content {
  flex: 1;
  position: relative;
  margin-left: var(--spacing-md);
  border: 1px dashed var(--gray-300);
  border-radius: var(--radius-sm);
  min-height: 40px;
}

.timeline-item {
  position: absolute;
  top: 2px;
  bottom: 2px;
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  padding: var(--spacing-xs);
  color: white;
  font-size: 12px;
}

.timeline-item.video {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.timeline-item.image {
  background: linear-gradient(135deg, #10b981, #047857);
}

.timeline-item.text {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.timeline-item:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.timeline-item.selected {
  box-shadow: 0 0 0 2px var(--primary-color);
}

.item-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  flex: 1;
}

.item-duration {
  font-size: 10px;
  opacity: 0.8;
}

.timeline-playhead {
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2px;
  background: var(--danger-color);
  z-index: 10;
  pointer-events: none;
}

/* 属性面板样式 */
.property-panel {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
}

.property-panel h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
}

.property-group {
  margin-bottom: var(--spacing-md);
}

.property-group:last-child {
  margin-bottom: 0;
}

.property-group label {
  display: block;
  font-size: 12px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--spacing-xs);
}

.position-controls {
  display: flex;
  gap: var(--spacing-xs);
}

/* 播放计划管理样式 */
.schedule-filters {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: var(--spacing-md);
}

.filter-buttons {
  display: flex;
  gap: var(--spacing-xs);
}

.date-filter {
  display: flex;
  gap: var(--spacing-xs);
}

.schedule-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 500px;
  overflow-y: auto;
}

.schedule-item {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all 0.3s ease;
}

.schedule-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.schedule-item.active {
  border-left: 4px solid var(--success-color);
  background: rgba(16, 185, 129, 0.02);
}

.schedule-item.scheduled {
  border-left: 4px solid var(--warning-color);
  background: rgba(245, 158, 11, 0.02);
}

.schedule-item.completed {
  border-left: 4px solid var(--gray-400);
  background: rgba(107, 114, 128, 0.02);
  opacity: 0.8;
}

.schedule-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.schedule-info {
  flex: 1;
}

.schedule-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.schedule-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 14px;
  color: var(--gray-600);
}

.schedule-meta span {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
}

.schedule-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
}

.schedule-progress {
  width: 100px;
  height: 4px;
  background: var(--gray-200);
  border-radius: 2px;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--success-color);
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 12px;
  color: var(--gray-500);
}

.schedule-countdown {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--warning-color);
}

.schedule-result {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--gray-500);
}

.schedule-details {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.schedule-content {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.content-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--gray-600);
  background: var(--gray-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.schedule-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 终端设备管理样式 */
.terminal-overview {
  display: flex;
  gap: var(--spacing-md);
  justify-content: space-around;
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.terminal-stat {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: white;
  border-radius: var(--radius-md);
  flex: 1;
  transition: all 0.3s ease;
}

.terminal-stat:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.terminal-stat.online {
  border-left: 4px solid var(--success-color);
}

.terminal-stat.offline {
  border-left: 4px solid var(--danger-color);
}

.terminal-stat.playing {
  border-left: 4px solid var(--info-color);
}

.terminal-stat.idle {
  border-left: 4px solid var(--gray-400);
}

.stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.terminal-stat.online .stat-icon {
  background: var(--success-color);
}

.terminal-stat.offline .stat-icon {
  background: var(--danger-color);
}

.terminal-stat.playing .stat-icon {
  background: var(--info-color);
}

.terminal-stat.idle .stat-icon {
  background: var(--gray-400);
}

.stat-info {
  flex: 1;
}

.stat-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.stat-label {
  font-size: 12px;
  color: var(--gray-600);
}

.terminal-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.terminal-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.terminal-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.terminal-item.online {
  border-left: 4px solid var(--success-color);
}

.terminal-item.offline {
  border-left: 4px solid var(--danger-color);
}

.terminal-item.playing {
  background: rgba(59, 130, 246, 0.02);
}

.terminal-info {
  flex: 1;
}

.terminal-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.terminal-name {
  font-weight: 600;
  color: var(--gray-900);
}

.terminal-id {
  font-family: monospace;
  font-size: 12px;
  color: var(--gray-500);
  background: var(--gray-100);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.terminal-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-row {
  display: flex;
  font-size: 14px;
}

.detail-label {
  color: var(--gray-500);
  min-width: 60px;
}

.detail-value {
  color: var(--gray-700);
}

.terminal-preview {
  width: 160px;
  flex-shrink: 0;
}

.preview-screen {
  width: 100%;
  height: 90px;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-sm);
  overflow: hidden;
  position: relative;
  background: var(--gray-900);
}

.preview-screen img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.play-indicator {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 24px;
  height: 24px;
  background: rgba(16, 185, 129, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 10px;
}

.preview-screen.idle {
  background: var(--gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
}

.idle-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--gray-300);
  font-size: 12px;
}

.preview-screen.offline {
  background: var(--gray-800);
  display: flex;
  align-items: center;
  justify-content: center;
}

.offline-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--danger-color);
  font-size: 12px;
}

.terminal-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

/* 内容审核样式 */
.review-stats {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: space-around;
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.review-stat {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: white;
  border-radius: var(--radius-md);
  flex: 1;
  transition: all 0.3s ease;
}

.review-stat:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.review-stat.pending {
  border-left: 4px solid var(--warning-color);
}

.review-stat.approved {
  border-left: 4px solid var(--success-color);
}

.review-stat.rejected {
  border-left: 4px solid var(--danger-color);
}

.review-stat i {
  font-size: 20px;
}

.review-stat.pending i {
  color: var(--warning-color);
}

.review-stat.approved i {
  color: var(--success-color);
}

.review-stat.rejected i {
  color: var(--danger-color);
}

.review-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.review-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
}

.review-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.review-item.pending {
  border-left: 4px solid var(--warning-color);
  background: rgba(245, 158, 11, 0.02);
}

.review-item.approved {
  border-left: 4px solid var(--success-color);
  background: rgba(16, 185, 129, 0.02);
}

.review-content {
  display: flex;
  gap: var(--spacing-md);
  flex: 1;
}

.content-preview {
  width: 80px;
  height: 60px;
  flex-shrink: 0;
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.content-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.text-preview-small {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc, #e2e8f0);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  color: var(--gray-600);
}

.content-info {
  flex: 1;
}

.content-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.content-meta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--spacing-sm);
  font-size: 12px;
  color: var(--gray-500);
}

.content-type {
  background: var(--primary-color);
  color: white;
  padding: 1px 6px;
  border-radius: var(--radius-sm);
}

.review-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

.review-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
}

.review-note {
  font-size: 12px;
  color: var(--gray-500);
  font-style: italic;
}

/* 播放监控样式 */
.monitoring-stats {
  display: flex;
  gap: var(--spacing-lg);
}

.monitoring-chart {
  flex: 2;
  display: flex;
  justify-content: center;
}

.monitoring-chart canvas {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
}

.monitoring-summary {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.summary-item {
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  text-align: center;
  transition: all 0.3s ease;
}

.summary-item:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.summary-label {
  font-size: 12px;
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.summary-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
}

.realtime-status {
  margin-top: var(--spacing-lg);
}

.realtime-status h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: var(--spacing-sm);
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  max-height: 200px;
  overflow-y: auto;
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.status-item:hover {
  background: var(--gray-50);
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.status-item.playing .status-indicator {
  background: var(--success-color);
  animation: pulse 2s infinite;
}

.status-item.idle .status-indicator {
  background: var(--gray-400);
}

.status-item.error .status-indicator {
  background: var(--danger-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.status-info {
  flex: 1;
}

.status-terminal {
  font-weight: 600;
  color: var(--gray-900);
  font-size: 14px;
  margin-bottom: var(--spacing-xs);
}

.status-content {
  color: var(--gray-600);
  font-size: 12px;
  margin-bottom: var(--spacing-xs);
}

.status-time {
  color: var(--gray-500);
  font-size: 11px;
}

/* 响应式优化 - 信息发布系统 */
@media (max-width: 1200px) {
  .material-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }

  .monitoring-stats {
    flex-direction: column;
  }

  .monitoring-summary {
    flex-direction: row;
  }
}

@media (max-width: 768px) {
  .material-filters {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .filter-tabs {
    justify-content: center;
    flex-wrap: wrap;
  }

  .search-box input {
    min-width: auto;
    flex: 1;
  }

  .material-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .program-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .program-actions {
    justify-content: center;
  }

  .timeline-track {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .track-label {
    width: auto;
  }

  .track-content {
    margin-left: 0;
  }

  .schedule-filters {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .filter-buttons {
    justify-content: center;
    flex-wrap: wrap;
  }

  .schedule-header {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .schedule-details {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .schedule-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .terminal-overview {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-sm);
  }

  .terminal-item {
    flex-direction: column;
    text-align: center;
  }

  .terminal-header {
    justify-content: center;
  }

  .terminal-actions {
    flex-direction: row;
    justify-content: center;
  }

  .review-stats {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .review-item {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .review-content {
    flex-direction: column;
    text-align: center;
  }

  .review-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .monitoring-summary {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .filter-tab {
    font-size: 12px;
    padding: var(--spacing-xs) var(--spacing-sm);
  }

  .material-grid {
    grid-template-columns: 1fr;
  }

  .timeline-ruler {
    font-size: 10px;
  }

  .timeline-item {
    font-size: 10px;
    padding: 2px;
  }

  .terminal-overview {
    grid-template-columns: 1fr;
  }

  .terminal-stat {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-sm);
  }

  .status-list {
    max-height: 150px;
  }
}

/* 停车场管理系统专用样式 */
.parking-area-tabs {
  display: flex;
  gap: var(--spacing-sm);
  margin-bottom: var(--spacing-md);
}

.area-tab {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  background: white;
  color: var(--gray-700);
  cursor: pointer;
  transition: all 0.3s ease;
  flex: 1;
  text-align: center;
}

.area-tab:hover {
  border-color: var(--primary-color);
  color: var(--primary-color);
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.area-tab.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.area-status {
  font-size: 12px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  background: rgba(255, 255, 255, 0.2);
}

.area-tab.active .area-status {
  background: rgba(255, 255, 255, 0.3);
}

/* 停车场地图样式 */
.parking-map {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  background: var(--gray-50);
}

.parking-grid {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.parking-row {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.row-label {
  width: 60px;
  font-size: 12px;
  font-weight: 600;
  color: var(--gray-600);
  text-align: center;
}

.parking-spaces {
  display: flex;
  gap: var(--spacing-sm);
  flex: 1;
}

.parking-space {
  width: 80px;
  height: 60px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  background: white;
}

.parking-space:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.parking-space.occupied {
  background: #fee2e2;
  border-color: #ef4444;
  color: #dc2626;
}

.parking-space.empty {
  background: #dcfce7;
  border-color: #22c55e;
  color: #16a34a;
}

.parking-space.reserved {
  background: #dbeafe;
  border-color: #3b82f6;
  color: #2563eb;
}

.parking-space.violation {
  background: #fef3c7;
  border-color: #f59e0b;
  color: #d97706;
  animation: pulse 2s infinite;
}

.space-number {
  font-size: 10px;
  font-weight: 600;
  margin-bottom: 2px;
}

.space-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: 8px;
}

.space-status i {
  font-size: 12px;
  margin-bottom: 2px;
}

/* 停车场图例 */
.parking-legend {
  display: flex;
  justify-content: center;
  gap: var(--spacing-lg);
  padding: var(--spacing-md);
  background: white;
  border-radius: var(--radius-sm);
}

.legend-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--gray-600);
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-300);
}

.legend-color.occupied {
  background: #fee2e2;
  border-color: #ef4444;
}

.legend-color.empty {
  background: #dcfce7;
  border-color: #22c55e;
}

.legend-color.reserved {
  background: #dbeafe;
  border-color: #3b82f6;
}

.legend-color.violation {
  background: #fef3c7;
  border-color: #f59e0b;
}

/* 车辆搜索样式 */
.vehicle-search {
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.search-filters {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.search-input {
  display: flex;
  gap: var(--spacing-xs);
  flex: 1;
}

.search-input input {
  flex: 1;
}

/* 车辆列表样式 */
.vehicle-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.vehicle-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  background: white;
}

.vehicle-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.vehicle-item.in-parking {
  border-left: 4px solid var(--success-color);
}

.vehicle-item.out-parking {
  border-left: 4px solid var(--gray-400);
}

.vehicle-item.violation {
  border-left: 4px solid var(--warning-color);
  background: rgba(245, 158, 11, 0.02);
}

.vehicle-info {
  flex: 1;
}

.vehicle-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.license-plate {
  font-family: monospace;
  font-size: 16px;
  font-weight: 700;
  color: var(--gray-900);
  background: var(--gray-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-300);
}

.vehicle-type {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.vehicle-type.employee {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.vehicle-type.visitor {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.vehicle-type.service {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.vehicle-details {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-xs);
}

.detail-row {
  display: flex;
  font-size: 14px;
}

.detail-label {
  color: var(--gray-500);
  min-width: 80px;
}

.detail-value {
  color: var(--gray-700);
  font-weight: 500;
}

.vehicle-actions {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

/* 车牌识别记录样式 */
.recognition-filters {
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.filter-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.recognition-log {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  background: white;
}

.log-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.log-item.enter {
  border-left: 4px solid var(--success-color);
}

.log-item.exit {
  border-left: 4px solid var(--info-color);
}

.log-item.patrol {
  border-left: 4px solid var(--warning-color);
}

.log-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  text-align: center;
}

.time-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-900);
}

.date-value {
  font-size: 12px;
  color: var(--gray-500);
}

.log-image {
  position: relative;
  width: 120px;
  height: 80px;
  flex-shrink: 0;
}

.log-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-sm);
}

.recognition-confidence {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-size: 10px;
  font-weight: 600;
}

.log-info {
  flex: 1;
}

.license-plate-detected {
  font-family: monospace;
  font-size: 18px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
  background: var(--gray-100);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
  display: inline-block;
}

.log-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 14px;
  color: var(--gray-600);
}

.detail-item i {
  width: 16px;
  color: var(--gray-500);
}

.log-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-sm);
}

.log-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 违停告警管理样式 */
.violation-stats {
  display: flex;
  gap: var(--spacing-lg);
  justify-content: space-around;
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.violation-stats .stat-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: white;
  border-radius: var(--radius-md);
  flex: 1;
  transition: all 0.3s ease;
}

.violation-stats .stat-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.violation-stats .stat-item.urgent {
  border-left: 4px solid var(--danger-color);
}

.violation-stats .stat-item.warning {
  border-left: 4px solid var(--warning-color);
}

.violation-stats .stat-item.resolved {
  border-left: 4px solid var(--success-color);
}

.violation-stats .stat-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: white;
}

.violation-stats .stat-item.urgent .stat-icon {
  background: var(--danger-color);
}

.violation-stats .stat-item.warning .stat-icon {
  background: var(--warning-color);
}

.violation-stats .stat-item.resolved .stat-icon {
  background: var(--success-color);
}

.violation-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.violation-item {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-md);
  transition: all 0.3s ease;
  background: white;
}

.violation-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.violation-item.urgent {
  border-left: 4px solid var(--danger-color);
  background: rgba(239, 68, 68, 0.02);
}

.violation-item.warning {
  border-left: 4px solid var(--warning-color);
  background: rgba(245, 158, 11, 0.02);
}

.violation-item.resolved {
  border-left: 4px solid var(--success-color);
  background: rgba(16, 185, 129, 0.02);
  opacity: 0.8;
}

.violation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.violation-type {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-weight: 600;
  color: var(--gray-900);
}

.violation-time {
  font-size: 12px;
  color: var(--gray-500);
}

.priority-badge {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.priority-badge.urgent {
  background: rgba(239, 68, 68, 0.1);
  color: var(--danger-color);
}

.priority-badge.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.priority-badge.resolved {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.violation-content {
  display: flex;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.violation-info {
  flex: 1;
}

.violation-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.violation-image {
  width: 100px;
  height: 75px;
  flex-shrink: 0;
}

.violation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-sm);
}

.violation-status {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--success-color);
  font-weight: 500;
}

.violation-actions {
  display: flex;
  gap: var(--spacing-xs);
  justify-content: flex-end;
}

/* 停车收费管理样式 */
.fee-stats {
  margin-bottom: var(--spacing-md);
}

.fee-summary {
  display: flex;
  justify-content: space-around;
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.fee-summary .summary-item {
  text-align: center;
  padding: var(--spacing-md);
  background: white;
  border-radius: var(--radius-md);
  flex: 1;
  margin: 0 var(--spacing-xs);
  transition: all 0.3s ease;
}

.fee-summary .summary-item:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-sm);
}

.fee-summary .summary-label {
  font-size: 12px;
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.fee-summary .summary-value {
  font-size: 20px;
  font-weight: 700;
  color: var(--success-color);
}

.fee-records {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 300px;
  overflow-y: auto;
}

.record-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  background: white;
}

.record-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.record-item.paid {
  border-left: 4px solid var(--success-color);
}

.record-item.pending {
  border-left: 4px solid var(--warning-color);
}

.record-info {
  flex: 1;
}

.record-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-top: var(--spacing-sm);
}

.record-details .detail-item {
  font-size: 14px;
  color: var(--gray-600);
}

.record-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-xs);
}

.payment-time {
  font-size: 12px;
  color: var(--gray-500);
}

.record-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

/* 统计分析样式 */
.parking-chart {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-md);
}

.parking-chart canvas {
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
}

.statistics-summary {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.stat-row {
  display: flex;
  gap: var(--spacing-md);
}

.statistics-summary .stat-item {
  flex: 1;
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  text-align: center;
  transition: all 0.3s ease;
}

.statistics-summary .stat-item:hover {
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.statistics-summary .stat-label {
  font-size: 12px;
  color: var(--gray-600);
  margin-bottom: var(--spacing-xs);
}

.statistics-summary .stat-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--primary-color);
}

.time-analysis {
  margin-top: var(--spacing-lg);
}

.time-analysis h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: var(--spacing-sm);
}

.time-slots {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.time-slot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  transition: all 0.3s ease;
}

.time-slot:hover {
  transform: translateX(4px);
}

.time-slot.peak {
  background: rgba(239, 68, 68, 0.1);
  border-left: 4px solid var(--danger-color);
}

.time-slot.high {
  background: rgba(245, 158, 11, 0.1);
  border-left: 4px solid var(--warning-color);
}

.time-slot.normal {
  background: rgba(59, 130, 246, 0.1);
  border-left: 4px solid var(--info-color);
}

.time-slot.low {
  background: rgba(107, 114, 128, 0.1);
  border-left: 4px solid var(--gray-400);
}

.time-range {
  font-weight: 600;
  color: var(--gray-900);
}

.usage-rate {
  font-weight: 700;
  color: var(--primary-color);
}

/* 响应式优化 - 停车场管理系统 */
@media (max-width: 1200px) {
  .parking-spaces {
    gap: var(--spacing-xs);
  }

  .parking-space {
    width: 70px;
    height: 50px;
  }

  .vehicle-details {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .parking-area-tabs {
    flex-direction: column;
  }

  .parking-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .row-label {
    width: auto;
  }

  .parking-spaces {
    justify-content: center;
    flex-wrap: wrap;
  }

  .parking-space {
    width: 60px;
    height: 45px;
  }

  .space-status {
    font-size: 7px;
  }

  .parking-legend {
    flex-wrap: wrap;
    gap: var(--spacing-sm);
  }

  .search-filters {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .search-input {
    flex-direction: column;
  }

  .vehicle-item {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .vehicle-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .vehicle-actions {
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
  }

  .filter-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .log-item {
    flex-direction: column;
    text-align: center;
  }

  .log-status {
    align-items: center;
  }

  .violation-stats {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .violation-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .violation-content {
    flex-direction: column;
    text-align: center;
  }

  .violation-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .fee-summary {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .record-item {
    flex-direction: column;
    text-align: center;
  }

  .record-status {
    align-items: center;
  }

  .record-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .stat-row {
    flex-direction: column;
  }
}

@media (max-width: 480px) {
  .parking-space {
    width: 50px;
    height: 40px;
  }

  .space-number {
    font-size: 8px;
  }

  .space-status {
    font-size: 6px;
  }

  .space-status i {
    font-size: 10px;
  }

  .license-plate {
    font-size: 14px;
  }

  .license-plate-detected {
    font-size: 16px;
  }

  .log-image {
    width: 100px;
    height: 60px;
  }

  .violation-image {
    width: 80px;
    height: 60px;
  }
}

/* 安全策略配置系统专用样式 */
.policy-section {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.policy-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-md);
}

.section-title i {
  color: var(--primary-color);
}

.policy-settings {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.setting-group {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  background: var(--gray-50);
}

.setting-label {
  min-width: 150px;
  font-weight: 500;
  color: var(--gray-700);
  margin: 0;
}

.setting-control {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.form-range {
  width: 100%;
  height: 6px;
  background: var(--gray-200);
  border-radius: 3px;
  outline: none;
  -webkit-appearance: none;
}

.form-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
}

.form-range::-moz-range-thumb {
  width: 20px;
  height: 20px;
  background: var(--primary-color);
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.range-value {
  font-weight: 600;
  color: var(--primary-color);
  font-size: 14px;
}

.checkbox-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.checkbox-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: 14px;
  color: var(--gray-700);
}

.checkbox-item input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-300);
  border-radius: var(--radius-sm);
  position: relative;
  transition: all 0.3s ease;
}

.checkbox-item input[type="checkbox"]:checked + .checkmark {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-item input[type="checkbox"]:checked + .checkmark::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.radio-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  font-size: 14px;
  color: var(--gray-700);
}

.radio-item input[type="radio"] {
  display: none;
}

.radio-mark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--gray-300);
  border-radius: 50%;
  position: relative;
  transition: all 0.3s ease;
}

.radio-item input[type="radio"]:checked + .radio-mark {
  border-color: var(--primary-color);
}

.radio-item input[type="radio"]:checked + .radio-mark::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
}

.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

.form-text {
  font-size: 12px;
  color: var(--gray-500);
  margin-top: var(--spacing-xs);
}

/* 密码策略预览样式 */
.policy-preview {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.policy-preview h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.preview-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 14px;
}

.preview-item i {
  width: 16px;
  text-align: center;
}

/* IP访问控制样式 */
.ip-section {
  margin-bottom: var(--spacing-lg);
}

.ip-section-title {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-size: 14px;
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  color: var(--gray-900);
}

.ip-list {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.ip-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  background: white;
}

.ip-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.ip-item.allow {
  border-left: 4px solid var(--success-color);
}

.ip-item.deny {
  border-left: 4px solid var(--danger-color);
}

.ip-item.temporary {
  background: rgba(245, 158, 11, 0.02);
}

.ip-info {
  flex: 1;
}

.ip-address {
  font-family: monospace;
  font-size: 16px;
  font-weight: 700;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.ip-description {
  color: var(--gray-600);
  margin-bottom: var(--spacing-sm);
}

.ip-meta {
  display: flex;
  gap: var(--spacing-md);
  font-size: 12px;
}

.ip-type {
  background: var(--gray-100);
  color: var(--gray-600);
  padding: 2px 6px;
  border-radius: var(--radius-sm);
}

.ip-status {
  padding: 2px 6px;
  border-radius: var(--radius-sm);
  font-weight: 500;
}

.ip-status.active {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.ip-status.temporary {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.ip-date {
  color: var(--gray-500);
}

.ip-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

/* 验证码配置样式 */
.captcha-preview {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.captcha-preview h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
}

.preview-container {
  display: flex;
  gap: var(--spacing-lg);
  align-items: center;
}

.captcha-demo {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.captcha-image {
  position: relative;
  display: inline-block;
}

.captcha-image img {
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
}

.refresh-captcha {
  position: absolute;
  top: 2px;
  right: 2px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  border: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.captcha-input {
  width: 120px;
  padding: var(--spacing-xs);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-sm);
  text-align: center;
}

.captcha-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.captcha-info .info-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--gray-600);
}

.captcha-info .info-item i {
  width: 16px;
  color: var(--primary-color);
}

/* 会话管理样式 */
.session-list {
  margin-top: var(--spacing-lg);
}

.session-list h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
  border-bottom: 1px solid var(--gray-200);
  padding-bottom: var(--spacing-sm);
}

.session-items {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 400px;
  overflow-y: auto;
}

.session-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  background: white;
}

.session-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.session-item.suspicious {
  border-left: 4px solid var(--warning-color);
  background: rgba(245, 158, 11, 0.02);
}

.session-info {
  flex: 1;
}

.session-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.user-avatar {
  width: 40px;
  height: 40px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-xs);
}

.user-role {
  font-size: 12px;
  color: var(--gray-500);
}

.session-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.session-details .detail-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 12px;
  color: var(--gray-600);
}

.session-details .detail-item i {
  width: 16px;
  color: var(--gray-500);
}

.session-status {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-sm);
}

.session-actions {
  display: flex;
  gap: var(--spacing-xs);
}

/* 安全审计日志样式 */
.audit-filters {
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
}

.filter-row {
  display: flex;
  gap: var(--spacing-md);
  align-items: center;
}

.filter-row input,
.filter-row select {
  flex: 1;
}

.audit-log {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
  max-height: 500px;
  overflow-y: auto;
}

.log-item {
  display: flex;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all 0.3s ease;
  background: white;
}

.log-item:hover {
  box-shadow: var(--shadow-sm);
  border-color: var(--primary-color);
}

.log-item.info {
  border-left: 4px solid var(--info-color);
}

.log-item.warning {
  border-left: 4px solid var(--warning-color);
}

.log-item.error {
  border-left: 4px solid var(--danger-color);
}

.log-item.critical {
  border-left: 4px solid var(--danger-color);
  background: rgba(239, 68, 68, 0.02);
}

.log-time {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 80px;
  text-align: center;
}

.log-time .time-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-900);
}

.log-time .date-value {
  font-size: 12px;
  color: var(--gray-500);
}

.log-level {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 60px;
  text-align: center;
}

.log-level i {
  font-size: 18px;
  margin-bottom: var(--spacing-xs);
}

.log-level span {
  font-size: 12px;
  font-weight: 500;
}

.log-level.info i {
  color: var(--info-color);
}

.log-level.warning i {
  color: var(--warning-color);
}

.log-level.error i {
  color: var(--danger-color);
}

.log-level.critical i {
  color: var(--danger-color);
}

.log-content {
  flex: 1;
}

.log-title {
  font-weight: 600;
  color: var(--gray-900);
  margin-bottom: var(--spacing-sm);
}

.log-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.log-details .detail-item {
  font-size: 12px;
  color: var(--gray-600);
}

.log-actions {
  display: flex;
  gap: var(--spacing-xs);
  flex-shrink: 0;
}

/* 响应式优化 - 安全策略配置系统 */
@media (max-width: 1200px) {
  .setting-group {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .setting-label {
    min-width: auto;
  }

  .preview-container {
    flex-direction: column;
    gap: var(--spacing-md);
  }
}

@media (max-width: 768px) {
  .policy-settings {
    gap: var(--spacing-sm);
  }

  .setting-group {
    padding: var(--spacing-sm);
  }

  .checkbox-group,
  .radio-group {
    gap: var(--spacing-xs);
  }

  .ip-item {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .ip-meta {
    flex-direction: column;
    gap: var(--spacing-xs);
  }

  .ip-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .session-item {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .session-user {
    justify-content: center;
    text-align: center;
  }

  .session-status {
    align-items: center;
  }

  .session-actions {
    justify-content: center;
    flex-wrap: wrap;
  }

  .filter-row {
    flex-direction: column;
    gap: var(--spacing-sm);
  }

  .log-item {
    flex-direction: column;
    text-align: center;
  }

  .log-actions {
    justify-content: center;
    flex-wrap: wrap;
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 14px;
  }

  .setting-label {
    font-size: 14px;
  }

  .checkbox-item,
  .radio-item {
    font-size: 12px;
  }

  .ip-address {
    font-size: 14px;
  }

  .user-name {
    font-size: 14px;
  }

  .log-title {
    font-size: 14px;
  }
}

/* 系统配置专用样式 */
.config-section {
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.config-section:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.config-settings {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

/* 系统状态监控样式 */
.system-status {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.system-status h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-md);
}

.status-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: white;
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.status-icon.success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-color);
}

.status-icon.warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-color);
}

.status-icon.info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--info-color);
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 12px;
  color: var(--gray-500);
  margin-bottom: var(--spacing-xs);
}

.status-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-900);
}

/* 地图配置样式 */
.coordinate-input {
  display: flex;
  gap: var(--spacing-sm);
}

.coordinate-input .input-group {
  flex: 1;
}

.map-status {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.map-status h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
}

.service-status {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  font-weight: 500;
  color: var(--gray-900);
}

.indicator-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.indicator-dot.success {
  background: var(--success-color);
}

.indicator-dot.warning {
  background: var(--warning-color);
}

.indicator-dot.error {
  background: var(--danger-color);
}

.status-details {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  margin-left: var(--spacing-lg);
}

.detail-item {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
}

.detail-label {
  color: var(--gray-500);
}

.detail-value {
  color: var(--gray-900);
  font-weight: 500;
}

/* 时间配置样式 */
.ntp-servers {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.weekday-selector {
  display: flex;
  gap: var(--spacing-xs);
  flex-wrap: wrap;
}

.weekday-item {
  display: flex;
  align-items: center;
  cursor: pointer;
}

.weekday-item input[type="checkbox"] {
  display: none;
}

.weekday-mark {
  width: 32px;
  height: 32px;
  border: 2px solid var(--gray-300);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: var(--gray-600);
  transition: all 0.3s ease;
}

.weekday-item input[type="checkbox"]:checked + .weekday-mark {
  background: var(--primary-color);
  border-color: var(--primary-color);
  color: white;
}

.time-sync-status {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.time-sync-status h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
}

.sync-info {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.sync-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: white;
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

.sync-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.sync-details {
  flex: 1;
}

.sync-title {
  font-size: 12px;
  color: var(--gray-500);
  margin-bottom: var(--spacing-xs);
}

.sync-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-900);
}

/* 数据库配置样式 */
.database-status {
  margin-top: var(--spacing-lg);
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.database-status h6 {
  margin: 0 0 var(--spacing-md) 0;
  color: var(--gray-900);
  font-weight: 600;
}

.db-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: var(--spacing-md);
}

.metric-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm);
  background: white;
  border-radius: var(--radius-sm);
  border: 1px solid var(--gray-200);
}

.metric-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.metric-info {
  flex: 1;
}

.metric-title {
  font-size: 12px;
  color: var(--gray-500);
  margin-bottom: var(--spacing-xs);
}

.metric-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-900);
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-md);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  z-index: 10000;
  transform: translateX(400px);
  opacity: 0;
  transition: all 0.3s ease;
  border-left: 4px solid var(--info-color);
  min-width: 300px;
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification.notification-success {
  border-left-color: var(--success-color);
}

.notification.notification-success i {
  color: var(--success-color);
}

.notification.notification-warning {
  border-left-color: var(--warning-color);
}

.notification.notification-warning i {
  color: var(--warning-color);
}

.notification.notification-error,
.notification.notification-danger {
  border-left-color: var(--danger-color);
}

.notification.notification-error i,
.notification.notification-danger i {
  color: var(--danger-color);
}

.notification.notification-info {
  border-left-color: var(--info-color);
}

.notification.notification-info i {
  color: var(--info-color);
}

.notification i {
  font-size: 18px;
}

.notification span {
  flex: 1;
  color: var(--gray-900);
  font-weight: 500;
}

/* 响应式优化 - 系统配置 */
@media (max-width: 1200px) {
  .status-grid {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .db-metrics {
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  }

  .coordinate-input {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .config-settings {
    gap: var(--spacing-sm);
  }

  .setting-group {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-sm);
  }

  .status-grid {
    grid-template-columns: 1fr;
  }

  .db-metrics {
    grid-template-columns: 1fr;
  }

  .weekday-selector {
    justify-content: center;
  }

  .sync-info {
    gap: var(--spacing-sm);
  }

  .status-details {
    margin-left: 0;
    margin-top: var(--spacing-sm);
  }

  .notification {
    right: 10px;
    left: 10px;
    min-width: auto;
    transform: translateY(-100px);
  }

  .notification.show {
    transform: translateY(0);
  }
}

@media (max-width: 480px) {
  .section-title {
    font-size: 14px;
  }

  .status-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-xs);
  }

  .metric-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-xs);
  }

  .sync-item {
    flex-direction: column;
    text-align: center;
    gap: var(--spacing-xs);
  }

  .weekday-mark {
    width: 28px;
    height: 28px;
    font-size: 11px;
  }
}

/* 地图监控页面样式 */
.map-container {
  position: relative;
  height: 600px;
  background: #f0f0f0;
  border-radius: var(--radius-lg);
  overflow: hidden;
  border: 1px solid var(--gray-200);
}

/* 日志管理页面样式 */
.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
  min-width: 200px;
}

.filter-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--gray-700);
}

.search-input-group {
  display: flex;
  gap: var(--spacing-xs);
}

.search-input-group .form-input {
  flex: 1;
}

.log-details {
  max-width: 200px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-config-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
}

.config-section {
  background: var(--gray-50);
  padding: var(--spacing-lg);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
}

.config-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-800);
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--gray-200);
}

.config-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  border-bottom: 1px solid var(--gray-100);
}

.config-item:last-child {
  border-bottom: none;
}

.config-label {
  flex: 1;
}

.config-label span {
  display: block;
  font-weight: 500;
  color: var(--gray-800);
  margin-bottom: var(--spacing-xs);
}

.config-label small {
  color: var(--gray-600);
  font-size: 12px;
}

.config-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: flex-end;
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

/* 开关样式 */
.switch {
  position: relative;
  display: inline-block;
  width: 50px;
  height: 24px;
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--gray-300);
  transition: 0.3s;
  border-radius: 24px;
}

.slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

input:checked + .slider {
  background-color: var(--primary-color);
}

input:checked + .slider:before {
  transform: translateX(26px);
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10000;
}

.modal-content {
  background: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
  overflow-y: auto;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--gray-200);
}

.modal-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--gray-800);
}

.modal-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: var(--gray-500);
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-sm);
  transition: all 0.2s ease;
}

.modal-close:hover {
  background: var(--gray-100);
  color: var(--gray-700);
}

.modal-body {
  padding: var(--spacing-lg);
}

.log-detail-grid {
  display: grid;
  gap: var(--spacing-md);
}

.detail-item {
  display: grid;
  grid-template-columns: 120px 1fr;
  gap: var(--spacing-sm);
  align-items: start;
}

.detail-item.full-width {
  grid-template-columns: 1fr;
}

.detail-item label {
  font-weight: 600;
  color: var(--gray-700);
  font-size: 14px;
}

.log-detail-content {
  background: var(--gray-50);
  padding: var(--spacing-md);
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-200);
  font-family: 'Courier New', monospace;
  font-size: 13px;
  line-height: 1.5;
  color: var(--gray-800);
}

/* 通知样式 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  background: white;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-md) var(--spacing-lg);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  z-index: 10001;
  transform: translateX(100%);
  opacity: 0;
  transition: all 0.3s ease;
  border-left: 4px solid var(--primary-color);
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification-success {
  border-left-color: var(--success-color);
}

.notification-error {
  border-left-color: var(--danger-color);
}

.notification-warning {
  border-left-color: var(--warning-color);
}

.notification i {
  font-size: 16px;
}

.notification-success i {
  color: var(--success-color);
}

.notification-error i {
  color: var(--danger-color);
}

.notification-warning i {
  color: var(--warning-color);
}

.notification span {
  font-size: 14px;
  color: var(--gray-800);
}

/* 日志卡片布局样式 */
.logs-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.log-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.log-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.log-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--gray-100);
}

.log-time {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--gray-600);
  font-size: 14px;
  font-weight: 500;
}

.log-time i {
  color: var(--gray-400);
}

.log-badges {
  display: flex;
  gap: var(--spacing-xs);
}

.log-card-body {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-lg);
}

.log-main-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.log-user {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--gray-700);
  font-size: 14px;
}

.log-user i {
  color: var(--gray-400);
  width: 14px;
}

.log-operation {
  font-size: 16px;
  color: var(--gray-800);
  margin: var(--spacing-xs) 0;
}

.log-details {
  color: var(--gray-600);
  font-size: 14px;
  line-height: 1.5;
  background: var(--gray-50);
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--gray-300);
}

.log-meta {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: var(--spacing-md);
  min-width: 140px;
}

.log-ip {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  color: var(--gray-600);
  font-size: 13px;
  font-family: 'Courier New', monospace;
}

.log-ip i {
  color: var(--gray-400);
  width: 14px;
}

.log-view-btn {
  white-space: nowrap;
}

.logs-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: var(--spacing-lg);
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
}

.empty-state {
  text-align: center;
  padding: var(--spacing-xl) 0;
  color: var(--gray-500);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .log-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .log-badges {
    align-self: flex-end;
  }

  .log-card-body {
    flex-direction: column;
    gap: var(--spacing-md);
  }

  .log-meta {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-width: auto;
  }

  .logs-pagination {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .pagination-controls {
    justify-content: center;
  }
}

/* 资源管理页面样式 */
.resources-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.resource-card {
  background: white;
  border: 1px solid var(--gray-200);
  border-radius: var(--radius-lg);
  overflow: hidden;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.resource-card:hover {
  border-color: var(--primary-color);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.resource-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.resource-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.resource-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.resource-icon.video {
  background: var(--primary-color);
}

.resource-icon.access {
  background: var(--success-color);
}

.resource-icon.sensor {
  background: var(--info-color);
}

.resource-icon.alarm {
  background: var(--warning-color);
}

.resource-details h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--gray-800);
}

.resource-details p {
  margin: 4px 0 0 0;
  font-size: 14px;
  color: var(--gray-600);
}

.resource-status .status-badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  font-size: 12px;
  font-weight: 500;
}

.status-badge.success {
  background: var(--success-light);
  color: var(--success-color);
}

.status-badge.danger {
  background: var(--danger-light);
  color: var(--danger-color);
}

.status-badge.warning {
  background: var(--warning-light);
  color: var(--warning-color);
}

.resource-card-body {
  padding: var(--spacing-lg);
}

.resource-specs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-lg);
}

.spec-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  font-size: 13px;
}

.spec-item i {
  width: 14px;
  color: var(--gray-400);
}

.spec-label {
  color: var(--gray-600);
  font-weight: 500;
}

.spec-value {
  color: var(--gray-800);
  font-family: 'Courier New', monospace;
}

.resource-description {
  padding: var(--spacing-md);
  background: var(--gray-50);
  border-radius: var(--radius-sm);
  border-left: 3px solid var(--primary-color);
}

.resource-description p {
  margin: 0;
  font-size: 14px;
  color: var(--gray-700);
  line-height: 1.5;
}

.resource-card-footer {
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-top: 1px solid var(--gray-200);
}

.resource-actions {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.resources-pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-lg);
  border-top: 1px solid var(--gray-200);
  background: var(--gray-50);
}

/* 筛选区域样式 */
.card-filters {
  padding: var(--spacing-lg);
  background: var(--gray-50);
  border-bottom: 1px solid var(--gray-200);
}

.filter-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-lg);
  align-items: end;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-xs);
}

.filter-group label {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
}

.search-group {
  min-width: 300px;
}

.search-input-wrapper {
  position: relative;
}

.search-input-wrapper .search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--gray-400);
  pointer-events: none;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .resources-container {
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  }
}

@media (max-width: 768px) {
  .resources-container {
    grid-template-columns: 1fr;
    padding: var(--spacing-md);
  }

  .resource-card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-sm);
  }

  .resource-status {
    align-self: flex-end;
  }

  .resource-specs {
    grid-template-columns: 1fr;
  }

  .resource-actions {
    justify-content: center;
  }

  .resources-pagination {
    flex-direction: column;
    gap: var(--spacing-md);
    text-align: center;
  }

  .filter-row {
    grid-template-columns: 1fr;
  }

  .search-group {
    min-width: auto;
  }
}



/* 地图加载状态样式 */
#mapLoading {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
  text-align: center;
  background: rgba(255, 255, 255, 0.9);
  padding: 20px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.map-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 18px;
  font-weight: 500;
}

.map-controls {
  position: absolute;
  top: var(--spacing-md);
  right: var(--spacing-md);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
  z-index: 10;
}

.map-control-btn {
  width: 40px;
  height: 40px;
  background: white;
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: var(--shadow-sm);
}

.map-control-btn:hover {
  background: var(--gray-50);
  box-shadow: var(--shadow-md);
}

.map-layers {
  position: absolute;
  top: var(--spacing-md);
  left: var(--spacing-md);
  background: white;
  border-radius: var(--radius-lg);
  padding: var(--spacing-md);
  box-shadow: var(--shadow-md);
  border: 1px solid var(--gray-200);
  min-width: 200px;
}

.layer-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-sm) 0;
  border-bottom: 1px solid var(--gray-100);
}

.layer-item:last-child {
  border-bottom: none;
}

.layer-checkbox {
  width: 16px;
  height: 16px;
}

.layer-label {
  font-size: 14px;
  color: var(--gray-700);
  flex: 1;
}

.layer-count {
  font-size: 12px;
  color: var(--gray-500);
  background: var(--gray-100);
  padding: 2px 6px;
  border-radius: 10px;
}

/* 设备监控样式 */
.device-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: var(--spacing-md);
}

.device-card {
  background: white;
  border-radius: var(--radius-lg);
  border: 1px solid var(--gray-200);
  overflow: hidden;
  transition: all 0.3s ease;
}

.device-card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.device-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.device-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.device-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: white;
}

.device-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--gray-900);
}

.device-location {
  font-size: 12px;
  color: var(--gray-500);
}

.device-body {
  padding: var(--spacing-md);
}

.device-params {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-sm);
}

.param-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xs) 0;
}

.param-label {
  font-size: 12px;
  color: var(--gray-600);
}

.param-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-900);
}

/* 表单样式 */
.form-group {
  margin-bottom: var(--spacing-md);
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--gray-700);
  margin-bottom: var(--spacing-xs);
}

.form-control {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--gray-300);
  border-radius: var(--radius-md);
  font-size: 14px;
  transition: all 0.3s ease;
}

.form-control:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select {
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  padding-right: 40px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-md);
}

@media (max-width: 768px) {
  .form-row {
    grid-template-columns: 1fr;
  }

  .device-grid {
    grid-template-columns: 1fr;
  }

  .map-layers {
    position: relative;
    top: auto;
    left: auto;
    margin-bottom: var(--spacing-md);
  }
}
