// 智慧楼宇管理平台 - 主JavaScript文件

class SmartBuildingApp {
  constructor() {
    this.sidebar = null;
    this.sidebarToggle = null;
    this.currentPage = 'dashboard';
    this.init();
  }

  init() {
    this.initElements();
    this.bindEvents();
    this.initNavigation();
    this.updateActiveNav();
  }

  initElements() {
    this.sidebar = document.querySelector('.sidebar');
    this.sidebarToggle = document.querySelector('.sidebar-toggle');
    this.mainContent = document.querySelector('.main-content');
  }

  bindEvents() {
    // 侧边栏切换
    if (this.sidebarToggle) {
      this.sidebarToggle.addEventListener('click', () => {
        this.toggleSidebar();
      });
    }

    // 导航点击事件
    document.addEventListener('click', (e) => {
      if (e.target.matches('.nav-item') || e.target.closest('.nav-item')) {
        e.preventDefault();
        const navItem = e.target.matches('.nav-item') ? e.target : e.target.closest('.nav-item');
        const href = navItem.getAttribute('href');
        if (href && href !== '#') {
          this.navigateTo(href);
        }
      }
    });

    // 响应式处理
    window.addEventListener('resize', () => {
      this.handleResize();
    });

    // 初始化时检查屏幕尺寸
    this.handleResize();
  }

  toggleSidebar() {
    if (this.sidebar) {
      this.sidebar.classList.toggle('collapsed');
      
      // 保存状态到localStorage
      const isCollapsed = this.sidebar.classList.contains('collapsed');
      localStorage.setItem('sidebarCollapsed', isCollapsed);
    }
  }

  handleResize() {
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
      // 移动端处理
      if (this.sidebar) {
        this.sidebar.classList.remove('collapsed');
      }
    } else {
      // 桌面端恢复侧边栏状态
      const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
      if (this.sidebar && isCollapsed) {
        this.sidebar.classList.add('collapsed');
      }
    }
  }

  initNavigation() {
    // 从localStorage恢复侧边栏状态
    const isCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
    if (this.sidebar && isCollapsed && window.innerWidth > 768) {
      this.sidebar.classList.add('collapsed');
    }

    // 从URL获取当前页面
    const path = window.location.pathname;
    const page = path.split('/').pop().replace('.html', '') || 'index';
    this.currentPage = page === 'index' ? 'dashboard' : page;
  }

  navigateTo(href) {
    // 更新当前页面
    const page = href.replace('.html', '').replace('./', '');
    this.currentPage = page === 'index' ? 'dashboard' : page;
    
    // 导航到新页面
    window.location.href = href;
  }

  updateActiveNav() {
    // 移除所有活动状态
    document.querySelectorAll('.nav-item').forEach(item => {
      item.classList.remove('active');
    });

    // 添加当前页面的活动状态
    const currentNavItem = document.querySelector(`[href="${this.currentPage}.html"], [href="./${this.currentPage}.html"], [href="index.html"]`);
    if (currentNavItem) {
      currentNavItem.classList.add('active');
    } else if (this.currentPage === 'dashboard') {
      const dashboardItem = document.querySelector('[href="index.html"], [href="./index.html"]');
      if (dashboardItem) {
        dashboardItem.classList.add('active');
      }
    }
  }

  // 工具方法
  showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
      <div class="notification-content">
        <i class="fas fa-${this.getNotificationIcon(type)}"></i>
        <span>${message}</span>
      </div>
      <button class="notification-close">
        <i class="fas fa-times"></i>
      </button>
    `;

    // 添加样式
    notification.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border-radius: 8px;
      box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
      border-left: 4px solid ${this.getNotificationColor(type)};
      padding: 16px;
      z-index: 9999;
      min-width: 300px;
      transform: translateX(100%);
      transition: transform 0.3s ease;
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 显示动画
    setTimeout(() => {
      notification.style.transform = 'translateX(0)';
    }, 100);

    // 关闭按钮事件
    const closeBtn = notification.querySelector('.notification-close');
    closeBtn.addEventListener('click', () => {
      this.hideNotification(notification);
    });

    // 自动关闭
    setTimeout(() => {
      this.hideNotification(notification);
    }, 5000);
  }

  hideNotification(notification) {
    notification.style.transform = 'translateX(100%)';
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
    }, 300);
  }

  getNotificationIcon(type) {
    const icons = {
      success: 'check-circle',
      warning: 'exclamation-triangle',
      error: 'exclamation-circle',
      info: 'info-circle'
    };
    return icons[type] || 'info-circle';
  }

  getNotificationColor(type) {
    const colors = {
      success: '#10b981',
      warning: '#f59e0b',
      error: '#ef4444',
      info: '#06b6d4'
    };
    return colors[type] || '#06b6d4';
  }

  // 数据格式化工具
  formatNumber(num) {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  formatDate(date) {
    if (typeof date === 'string') {
      date = new Date(date);
    }
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  formatFileSize(bytes) {
    const sizes = ['B', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 B';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
  }

  // API请求封装
  async request(url, options = {}) {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
      },
    };

    const config = { ...defaultOptions, ...options };

    try {
      const response = await fetch(url, config);
      
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }
      
      return await response.json();
    } catch (error) {
      console.error('Request failed:', error);
      this.showNotification('请求失败，请稍后重试', 'error');
      throw error;
    }
  }

  // 模拟数据生成器
  generateMockData() {
    return {
      dashboard: {
        totalAlerts: Math.floor(Math.random() * 50) + 10,
        totalWorkOrders: Math.floor(Math.random() * 100) + 20,
        onlineDevices: Math.floor(Math.random() * 500) + 800,
        totalDevices: 1000,
        energyConsumption: Math.floor(Math.random() * 10000) + 50000,
        recentAlerts: this.generateAlerts(5),
        recentWorkOrders: this.generateWorkOrders(5)
      }
    };
  }

  generateAlerts(count) {
    const types = ['设备故障', '温度异常', '门禁异常', '视频丢失', '网络异常'];
    const levels = ['高', '中', '低'];
    const alerts = [];

    for (let i = 0; i < count; i++) {
      alerts.push({
        id: i + 1,
        type: types[Math.floor(Math.random() * types.length)],
        level: levels[Math.floor(Math.random() * levels.length)],
        location: `${Math.floor(Math.random() * 10) + 1}号楼${Math.floor(Math.random() * 20) + 1}层`,
        time: new Date(Date.now() - Math.random() * 86400000).toISOString(),
        status: Math.random() > 0.5 ? '已处理' : '待处理'
      });
    }

    return alerts;
  }

  generateWorkOrders(count) {
    const types = ['设备维修', '清洁保养', '安全检查', '系统升级'];
    const statuses = ['待处理', '处理中', '已完成'];
    const orders = [];

    for (let i = 0; i < count; i++) {
      orders.push({
        id: `WO${String(i + 1).padStart(4, '0')}`,
        type: types[Math.floor(Math.random() * types.length)],
        title: `${types[Math.floor(Math.random() * types.length)]}工单`,
        assignee: `维修员${i + 1}`,
        status: statuses[Math.floor(Math.random() * statuses.length)],
        createTime: new Date(Date.now() - Math.random() * 86400000).toISOString(),
        priority: Math.random() > 0.7 ? '高' : Math.random() > 0.4 ? '中' : '低'
      });
    }

    return orders;
  }
}

// 图表工具类
class ChartUtils {
  static createLineChart(canvas, data, options = {}) {
    const ctx = canvas.getContext('2d');
    
    // 简单的线图实现（如果没有Chart.js）
    if (typeof Chart === 'undefined') {
      return this.drawSimpleLineChart(ctx, data, options);
    }
    
    return new Chart(ctx, {
      type: 'line',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        ...options
      }
    });
  }

  static createBarChart(canvas, data, options = {}) {
    const ctx = canvas.getContext('2d');
    
    if (typeof Chart === 'undefined') {
      return this.drawSimpleBarChart(ctx, data, options);
    }
    
    return new Chart(ctx, {
      type: 'bar',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        ...options
      }
    });
  }

  static createDoughnutChart(canvas, data, options = {}) {
    const ctx = canvas.getContext('2d');
    
    if (typeof Chart === 'undefined') {
      return this.drawSimpleDoughnutChart(ctx, data, options);
    }
    
    return new Chart(ctx, {
      type: 'doughnut',
      data: data,
      options: {
        responsive: true,
        maintainAspectRatio: false,
        ...options
      }
    });
  }

  // 简单图表绘制方法（备用）
  static drawSimpleLineChart(ctx, data, options) {
    // 简单实现，实际项目中建议使用Chart.js
    ctx.clearRect(0, 0, ctx.canvas.width, ctx.canvas.height);
    ctx.strokeStyle = '#3b82f6';
    ctx.lineWidth = 2;
    ctx.beginPath();
    
    const width = ctx.canvas.width;
    const height = ctx.canvas.height;
    const points = data.datasets[0].data;
    const stepX = width / (points.length - 1);
    const maxY = Math.max(...points);
    
    points.forEach((point, index) => {
      const x = index * stepX;
      const y = height - (point / maxY) * height;
      
      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });
    
    ctx.stroke();
  }
}

// 初始化应用
document.addEventListener('DOMContentLoaded', () => {
  window.app = new SmartBuildingApp();
  
  // 添加一些全局样式
  const style = document.createElement('style');
  style.textContent = `
    .notification {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    
    .notification-content {
      display: flex;
      align-items: center;
      gap: 8px;
    }
    
    .notification-close {
      background: none;
      border: none;
      cursor: pointer;
      color: #6b7280;
      padding: 4px;
    }
    
    .notification-close:hover {
      color: #374151;
    }
  `;
  document.head.appendChild(style);
});
