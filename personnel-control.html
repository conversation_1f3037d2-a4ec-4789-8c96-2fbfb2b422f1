<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>人员布控系统 - 广州农行智慧楼宇</title>
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="assets/css/main.css">
</head>
<body>
  <div class="app-container">
    <div id="sidebarContainer"></div>
    <div class="main-content">
      <div id="headerContainer"></div>
      <div class="page-content">
        <div class="page-header">
          <h1 class="page-title">人员布控系统</h1>
          <p class="page-description">布控策略配置、实时抓拍与布控告警、人脸轨迹回溯、人脸检索与身份比对</p>
        </div>
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">布控人员</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-user-shield"></i>
              </div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">3</div>
                <div class="stat-label">今日告警</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-exclamation-triangle"></i>
              </div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">1,245</div>
                <div class="stat-label">今日抓拍</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-camera"></i>
              </div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">24</div>
                <div class="stat-label">监控点位</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-video"></i>
              </div>
            </div>
          </div>
        </div>
        <!-- 功能导航 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-user-plus text-primary"></i>
                布控人员管理
              </h3>
              <button class="btn btn-primary" onclick="showAddPersonModal()">
                <i class="fas fa-plus"></i>
                新增布控
              </button>
            </div>
            <div class="card-body">
              <!-- 搜索和筛选 -->
              <div class="d-flex gap-md mb-md">
                <div class="form-group flex-1">
                  <input type="text" class="form-control" placeholder="搜索姓名、身份证号..." id="searchInput">
                </div>
                <div class="form-group">
                  <select class="form-control" id="statusFilter">
                    <option value="">全部状态</option>
                    <option value="active">启用中</option>
                    <option value="disabled">已禁用</option>
                    <option value="expired">已过期</option>
                  </select>
                </div>
                <div class="form-group">
                  <select class="form-control" id="levelFilter">
                    <option value="">全部级别</option>
                    <option value="high">高危</option>
                    <option value="medium">中危</option>
                    <option value="low">低危</option>
                  </select>
                </div>
                <button class="btn btn-outline-primary" onclick="searchPersonnel()">
                  <i class="fas fa-search"></i>
                  搜索
                </button>
              </div>

              <!-- 布控人员列表 -->
              <div class="table-responsive">
                <table class="table">
                  <thead>
                    <tr>
                      <th>头像</th>
                      <th>姓名</th>
                      <th>身份证号</th>
                      <th>布控级别</th>
                      <th>布控原因</th>
                      <th>创建时间</th>
                      <th>状态</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody id="personnelTableBody">
                    <tr>
                      <td>
                        <div class="avatar">
                          <img src="https://via.placeholder.com/40x40/4F46E5/FFFFFF?text=张" alt="张某某">
                        </div>
                      </td>
                      <td>张某某</td>
                      <td>110101199001011234</td>
                      <td><span class="status-badge danger">高危</span></td>
                      <td>涉嫌盗窃</td>
                      <td>2024-01-15 09:30</td>
                      <td><span class="status-badge success">启用中</span></td>
                      <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewPersonDetail(1)">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="editPerson(1)">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deletePerson(1)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="avatar">
                          <img src="https://via.placeholder.com/40x40/059669/FFFFFF?text=李" alt="李某某">
                        </div>
                      </td>
                      <td>李某某</td>
                      <td>110101199002022345</td>
                      <td><span class="status-badge warning">中危</span></td>
                      <td>可疑人员</td>
                      <td>2024-01-14 14:20</td>
                      <td><span class="status-badge success">启用中</span></td>
                      <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewPersonDetail(2)">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="editPerson(2)">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deletePerson(2)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="avatar">
                          <img src="https://via.placeholder.com/40x40/DC2626/FFFFFF?text=王" alt="王某某">
                        </div>
                      </td>
                      <td>王某某</td>
                      <td>110101199003033456</td>
                      <td><span class="status-badge info">低危</span></td>
                      <td>访客逾期</td>
                      <td>2024-01-13 16:45</td>
                      <td><span class="status-badge warning">已禁用</span></td>
                      <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="viewPersonDetail(3)">
                          <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="editPerson(3)">
                          <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deletePerson(3)">
                          <i class="fas fa-trash"></i>
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>

              <!-- 分页 -->
              <div class="d-flex justify-content-between align-items-center mt-md">
                <div class="text-sm text-gray-600">
                  显示 1-10 条，共 156 条记录
                </div>
                <div class="pagination">
                  <button class="btn btn-sm btn-outline-primary" disabled>
                    <i class="fas fa-chevron-left"></i>
                  </button>
                  <button class="btn btn-sm btn-primary">1</button>
                  <button class="btn btn-sm btn-outline-primary">2</button>
                  <button class="btn btn-sm btn-outline-primary">3</button>
                  <button class="btn btn-sm btn-outline-primary">
                    <i class="fas fa-chevron-right"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-bell text-warning"></i>
                实时告警
              </h3>
              <button class="btn btn-outline-primary btn-sm" onclick="refreshAlerts()">
                <i class="fas fa-sync-alt"></i>
                刷新
              </button>
            </div>
            <div class="card-body">
              <div class="alert-list" id="alertList">
                <div class="alert-item">
                  <div class="alert-icon danger">
                    <i class="fas fa-exclamation-triangle"></i>
                  </div>
                  <div class="alert-content">
                    <div class="alert-title">高危人员告警</div>
                    <div class="alert-desc">张某某 在 1号楼大厅 被识别</div>
                    <div class="alert-time">2分钟前</div>
                  </div>
                  <div class="alert-actions">
                    <button class="btn btn-sm btn-danger" onclick="handleAlert(1)">
                      <i class="fas fa-eye"></i>
                      查看
                    </button>
                  </div>
                </div>
                <div class="alert-item">
                  <div class="alert-icon warning">
                    <i class="fas fa-user-clock"></i>
                  </div>
                  <div class="alert-content">
                    <div class="alert-title">可疑人员告警</div>
                    <div class="alert-desc">李某某 在 停车场入口 被识别</div>
                    <div class="alert-time">5分钟前</div>
                  </div>
                  <div class="alert-actions">
                    <button class="btn btn-sm btn-warning" onclick="handleAlert(2)">
                      <i class="fas fa-eye"></i>
                      查看
                    </button>
                  </div>
                </div>
                <div class="alert-item">
                  <div class="alert-icon info">
                    <i class="fas fa-info-circle"></i>
                  </div>
                  <div class="alert-content">
                    <div class="alert-title">布控人员离开</div>
                    <div class="alert-desc">王某某 离开 2号楼电梯口</div>
                    <div class="alert-time">8分钟前</div>
                  </div>
                  <div class="alert-actions">
                    <button class="btn btn-sm btn-info" onclick="handleAlert(3)">
                      <i class="fas fa-eye"></i>
                      查看
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 抓拍记录和轨迹分析 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-camera text-success"></i>
                今日抓拍记录
              </h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-outline-primary btn-sm" onclick="exportCaptures()">
                  <i class="fas fa-download"></i>
                  导出
                </button>
                <button class="btn btn-outline-primary btn-sm" onclick="refreshCaptures()">
                  <i class="fas fa-sync-alt"></i>
                  刷新
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="capture-grid">
                <div class="capture-item">
                  <div class="capture-image">
                    <img src="https://via.placeholder.com/120x90/4F46E5/FFFFFF?text=抓拍" alt="抓拍图片">
                    <div class="capture-overlay">
                      <button class="btn btn-sm btn-primary" onclick="viewCaptureDetail(1)">
                        <i class="fas fa-search-plus"></i>
                      </button>
                    </div>
                  </div>
                  <div class="capture-info">
                    <div class="capture-person">张某某</div>
                    <div class="capture-location">1号楼大厅</div>
                    <div class="capture-time">14:32:15</div>
                    <div class="capture-confidence">相似度: 98%</div>
                  </div>
                </div>
                <div class="capture-item">
                  <div class="capture-image">
                    <img src="https://via.placeholder.com/120x90/059669/FFFFFF?text=抓拍" alt="抓拍图片">
                    <div class="capture-overlay">
                      <button class="btn btn-sm btn-primary" onclick="viewCaptureDetail(2)">
                        <i class="fas fa-search-plus"></i>
                      </button>
                    </div>
                  </div>
                  <div class="capture-info">
                    <div class="capture-person">李某某</div>
                    <div class="capture-location">停车场入口</div>
                    <div class="capture-time">14:28:42</div>
                    <div class="capture-confidence">相似度: 95%</div>
                  </div>
                </div>
                <div class="capture-item">
                  <div class="capture-image">
                    <img src="https://via.placeholder.com/120x90/DC2626/FFFFFF?text=抓拍" alt="抓拍图片">
                    <div class="capture-overlay">
                      <button class="btn btn-sm btn-primary" onclick="viewCaptureDetail(3)">
                        <i class="fas fa-search-plus"></i>
                      </button>
                    </div>
                  </div>
                  <div class="capture-info">
                    <div class="capture-person">未知人员</div>
                    <div class="capture-location">2号楼电梯口</div>
                    <div class="capture-time">14:25:18</div>
                    <div class="capture-confidence">待识别</div>
                  </div>
                </div>
                <div class="capture-item">
                  <div class="capture-image">
                    <img src="https://via.placeholder.com/120x90/7C3AED/FFFFFF?text=抓拍" alt="抓拍图片">
                    <div class="capture-overlay">
                      <button class="btn btn-sm btn-primary" onclick="viewCaptureDetail(4)">
                        <i class="fas fa-search-plus"></i>
                      </button>
                    </div>
                  </div>
                  <div class="capture-info">
                    <div class="capture-person">王某某</div>
                    <div class="capture-location">3号楼会议室</div>
                    <div class="capture-time">14:20:33</div>
                    <div class="capture-confidence">相似度: 92%</div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-route text-info"></i>
                人员轨迹分析
              </h3>
              <div class="d-flex gap-sm">
                <select class="form-control form-control-sm" style="width: auto;" id="trackPersonSelect">
                  <option value="">选择人员</option>
                  <option value="1">张某某</option>
                  <option value="2">李某某</option>
                  <option value="3">王某某</option>
                </select>
                <button class="btn btn-primary btn-sm" onclick="analyzeTrack()">
                  <i class="fas fa-search"></i>
                  分析
                </button>
              </div>
            </div>
            <div class="card-body">
              <div class="track-timeline" id="trackTimeline">
                <div class="timeline-item">
                  <div class="timeline-marker success"></div>
                  <div class="timeline-content">
                    <div class="timeline-title">进入大楼</div>
                    <div class="timeline-location">1号楼大厅</div>
                    <div class="timeline-time">14:32:15</div>
                  </div>
                </div>
                <div class="timeline-item">
                  <div class="timeline-marker warning"></div>
                  <div class="timeline-content">
                    <div class="timeline-title">电梯移动</div>
                    <div class="timeline-location">1号楼电梯</div>
                    <div class="timeline-time">14:33:02</div>
                  </div>
                </div>
                <div class="timeline-item">
                  <div class="timeline-marker info"></div>
                  <div class="timeline-content">
                    <div class="timeline-title">楼层活动</div>
                    <div class="timeline-location">1号楼5层走廊</div>
                    <div class="timeline-time">14:33:45</div>
                  </div>
                </div>
                <div class="timeline-item">
                  <div class="timeline-marker danger"></div>
                  <div class="timeline-content">
                    <div class="timeline-title">敏感区域</div>
                    <div class="timeline-location">1号楼5层机房</div>
                    <div class="timeline-time">14:34:12</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 人脸检索和布控策略 -->
        <div class="dashboard-grid mb-lg">
          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-search text-primary"></i>
                人脸检索
              </h3>
            </div>
            <div class="card-body">
              <div class="face-search-container">
                <div class="upload-area" onclick="uploadFaceImage()">
                  <div class="upload-content">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <div class="upload-text">点击上传人脸图片</div>
                    <div class="upload-hint">支持 JPG、PNG 格式，建议图片清晰度高</div>
                  </div>
                </div>
                <div class="search-options mt-md">
                  <div class="form-row">
                    <div class="form-group">
                      <label>相似度阈值</label>
                      <select class="form-control">
                        <option value="0.9">90% (精确)</option>
                        <option value="0.8" selected>80% (推荐)</option>
                        <option value="0.7">70% (宽松)</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label>搜索范围</label>
                      <select class="form-control">
                        <option value="all">全部区域</option>
                        <option value="building1">1号楼</option>
                        <option value="building2">2号楼</option>
                        <option value="building3">3号楼</option>
                      </select>
                    </div>
                    <div class="form-group">
                      <label>时间范围</label>
                      <select class="form-control">
                        <option value="today">今天</option>
                        <option value="week">最近一周</option>
                        <option value="month">最近一月</option>
                        <option value="custom">自定义</option>
                      </select>
                    </div>
                  </div>
                  <button class="btn btn-primary" onclick="startFaceSearch()">
                    <i class="fas fa-search"></i>
                    开始检索
                  </button>
                </div>
              </div>
            </div>
          </div>

          <div class="card">
            <div class="card-header">
              <h3 class="card-title">
                <i class="fas fa-cogs text-warning"></i>
                布控策略配置
              </h3>
              <button class="btn btn-primary btn-sm" onclick="saveStrategy()">
                <i class="fas fa-save"></i>
                保存配置
              </button>
            </div>
            <div class="card-body">
              <div class="strategy-config">
                <div class="config-section">
                  <h5>告警设置</h5>
                  <div class="form-group">
                    <label class="checkbox-label">
                      <input type="checkbox" checked>
                      <span class="checkmark"></span>
                      启用实时告警
                    </label>
                  </div>
                  <div class="form-group">
                    <label class="checkbox-label">
                      <input type="checkbox" checked>
                      <span class="checkmark"></span>
                      声音提醒
                    </label>
                  </div>
                  <div class="form-group">
                    <label class="checkbox-label">
                      <input type="checkbox">
                      <span class="checkmark"></span>
                      短信通知
                    </label>
                  </div>
                </div>
                <div class="config-section">
                  <h5>识别参数</h5>
                  <div class="form-group">
                    <label>识别阈值</label>
                    <input type="range" class="form-range" min="70" max="99" value="85" id="recognitionThreshold">
                    <div class="range-value">85%</div>
                  </div>
                  <div class="form-group">
                    <label>检测间隔</label>
                    <select class="form-control">
                      <option value="1">1秒</option>
                      <option value="3" selected>3秒</option>
                      <option value="5">5秒</option>
                      <option value="10">10秒</option>
                    </select>
                  </div>
                </div>
                <div class="config-section">
                  <h5>存储设置</h5>
                  <div class="form-group">
                    <label>抓拍保存天数</label>
                    <select class="form-control">
                      <option value="7">7天</option>
                      <option value="30" selected>30天</option>
                      <option value="90">90天</option>
                      <option value="365">1年</option>
                    </select>
                  </div>
                  <div class="form-group">
                    <label class="checkbox-label">
                      <input type="checkbox" checked>
                      <span class="checkmark"></span>
                      自动清理过期数据
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 新增布控人员模态框 -->
  <div class="modal" id="addPersonModal">
    <div class="modal-content">
      <div class="modal-header">
        <h3>新增布控人员</h3>
        <button class="modal-close" onclick="closeModal('addPersonModal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <form id="addPersonForm">
          <div class="form-row">
            <div class="form-group">
              <label>姓名 *</label>
              <input type="text" class="form-control" name="name" required>
            </div>
            <div class="form-group">
              <label>身份证号 *</label>
              <input type="text" class="form-control" name="idCard" required>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>布控级别 *</label>
              <select class="form-control" name="level" required>
                <option value="">请选择</option>
                <option value="high">高危</option>
                <option value="medium">中危</option>
                <option value="low">低危</option>
              </select>
            </div>
            <div class="form-group">
              <label>联系电话</label>
              <input type="tel" class="form-control" name="phone">
            </div>
          </div>
          <div class="form-group">
            <label>布控原因 *</label>
            <textarea class="form-control" name="reason" rows="3" required></textarea>
          </div>
          <div class="form-group">
            <label>人脸照片 *</label>
            <div class="upload-area-small" onclick="uploadPersonPhoto()">
              <i class="fas fa-camera"></i>
              <span>点击上传照片</span>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label>有效期开始</label>
              <input type="date" class="form-control" name="startDate">
            </div>
            <div class="form-group">
              <label>有效期结束</label>
              <input type="date" class="form-control" name="endDate">
            </div>
          </div>
          <div class="form-group">
            <label class="checkbox-label">
              <input type="checkbox" name="autoAlert" checked>
              <span class="checkmark"></span>
              启用自动告警
            </label>
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline-secondary" onclick="closeModal('addPersonModal')">取消</button>
        <button class="btn btn-primary" onclick="submitAddPerson()">确认添加</button>
      </div>
    </div>
  </div>

  <!-- 人员详情模态框 -->
  <div class="modal" id="personDetailModal">
    <div class="modal-content modal-lg">
      <div class="modal-header">
        <h3>人员详情</h3>
        <button class="modal-close" onclick="closeModal('personDetailModal')">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="modal-body">
        <div class="person-detail-container">
          <div class="person-basic-info">
            <div class="person-photo">
              <img src="https://via.placeholder.com/120x150/4F46E5/FFFFFF?text=照片" alt="人员照片">
            </div>
            <div class="person-info">
              <h4>张某某</h4>
              <div class="info-item">
                <label>身份证号:</label>
                <span>110101199001011234</span>
              </div>
              <div class="info-item">
                <label>布控级别:</label>
                <span class="status-badge danger">高危</span>
              </div>
              <div class="info-item">
                <label>布控原因:</label>
                <span>涉嫌盗窃</span>
              </div>
              <div class="info-item">
                <label>创建时间:</label>
                <span>2024-01-15 09:30</span>
              </div>
              <div class="info-item">
                <label>状态:</label>
                <span class="status-badge success">启用中</span>
              </div>
            </div>
          </div>
          <div class="person-records">
            <h5>识别记录</h5>
            <div class="record-list">
              <div class="record-item">
                <div class="record-time">14:32:15</div>
                <div class="record-location">1号楼大厅</div>
                <div class="record-confidence">98%</div>
                <div class="record-action">
                  <button class="btn btn-sm btn-outline-primary">查看</button>
                </div>
              </div>
              <div class="record-item">
                <div class="record-time">14:33:45</div>
                <div class="record-location">1号楼5层走廊</div>
                <div class="record-confidence">95%</div>
                <div class="record-action">
                  <button class="btn btn-sm btn-outline-primary">查看</button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button class="btn btn-outline-secondary" onclick="closeModal('personDetailModal')">关闭</button>
        <button class="btn btn-warning" onclick="editPersonFromDetail()">编辑</button>
        <button class="btn btn-danger" onclick="deletePersonFromDetail()">删除</button>
      </div>
    </div>
  </div>

  <script src="assets/js/main.js"></script>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('人员布控系统');
        }
      }, 100);

      // 初始化页面功能
      initPersonnelControl();
    });

    function loadComponent(containerId, componentPath) {
      fetch(componentPath).then(response => response.text()).then(html => {
        document.getElementById(containerId).innerHTML = html;
      }).catch(error => console.error('Error loading component:', error));
    }

    // 初始化人员布控功能
    function initPersonnelControl() {
      // 初始化识别阈值滑块
      const thresholdSlider = document.getElementById('recognitionThreshold');
      if (thresholdSlider) {
        thresholdSlider.addEventListener('input', function() {
          const value = this.value;
          const rangeValue = this.parentNode.querySelector('.range-value');
          if (rangeValue) {
            rangeValue.textContent = value + '%';
          }
        });
      }

      // 自动刷新告警列表
      setInterval(refreshAlerts, 30000); // 30秒刷新一次
    }

    // 显示新增布控人员模态框
    function showAddPersonModal() {
      document.getElementById('addPersonModal').style.display = 'flex';
    }

    // 关闭模态框
    function closeModal(modalId) {
      document.getElementById(modalId).style.display = 'none';
    }

    // 搜索人员
    function searchPersonnel() {
      const searchInput = document.getElementById('searchInput').value;
      const statusFilter = document.getElementById('statusFilter').value;
      const levelFilter = document.getElementById('levelFilter').value;

      console.log('搜索条件:', { searchInput, statusFilter, levelFilter });
      showNotification('搜索功能已触发', 'info');
    }

    // 查看人员详情
    function viewPersonDetail(id) {
      console.log('查看人员详情:', id);
      document.getElementById('personDetailModal').style.display = 'flex';
    }

    // 编辑人员
    function editPerson(id) {
      console.log('编辑人员:', id);
      showNotification('编辑功能开发中', 'info');
    }

    // 删除人员
    function deletePerson(id) {
      if (confirm('确定要删除这个布控人员吗？')) {
        console.log('删除人员:', id);
        showNotification('删除成功', 'success');
      }
    }

    // 提交新增人员
    function submitAddPerson() {
      const form = document.getElementById('addPersonForm');
      const formData = new FormData(form);

      // 验证必填字段
      const name = formData.get('name');
      const idCard = formData.get('idCard');
      const level = formData.get('level');
      const reason = formData.get('reason');

      if (!name || !idCard || !level || !reason) {
        showNotification('请填写所有必填字段', 'error');
        return;
      }

      console.log('新增人员数据:', Object.fromEntries(formData));
      showNotification('布控人员添加成功', 'success');
      closeModal('addPersonModal');
      form.reset();
    }

    // 处理告警
    function handleAlert(id) {
      console.log('处理告警:', id);
      showNotification('告警处理功能已触发', 'info');
    }

    // 刷新告警列表
    function refreshAlerts() {
      console.log('刷新告警列表');
      showNotification('告警列表已刷新', 'info');
    }

    // 查看抓拍详情
    function viewCaptureDetail(id) {
      console.log('查看抓拍详情:', id);
      showNotification('抓拍详情功能开发中', 'info');
    }

    // 导出抓拍记录
    function exportCaptures() {
      console.log('导出抓拍记录');
      showNotification('导出功能已触发', 'info');
    }

    // 刷新抓拍记录
    function refreshCaptures() {
      console.log('刷新抓拍记录');
      showNotification('抓拍记录已刷新', 'info');
    }

    // 分析人员轨迹
    function analyzeTrack() {
      const selectedPerson = document.getElementById('trackPersonSelect').value;
      if (!selectedPerson) {
        showNotification('请选择要分析的人员', 'warning');
        return;
      }
      console.log('分析轨迹:', selectedPerson);
      showNotification('轨迹分析已开始', 'info');
    }

    // 上传人脸图片
    function uploadFaceImage() {
      console.log('上传人脸图片');
      showNotification('图片上传功能开发中', 'info');
    }

    // 开始人脸检索
    function startFaceSearch() {
      console.log('开始人脸检索');
      showNotification('人脸检索已开始', 'info');
    }

    // 上传人员照片
    function uploadPersonPhoto() {
      console.log('上传人员照片');
      showNotification('照片上传功能开发中', 'info');
    }

    // 保存策略配置
    function saveStrategy() {
      console.log('保存策略配置');
      showNotification('配置保存成功', 'success');
    }

    // 从详情页编辑
    function editPersonFromDetail() {
      closeModal('personDetailModal');
      showAddPersonModal();
    }

    // 从详情页删除
    function deletePersonFromDetail() {
      if (confirm('确定要删除这个布控人员吗？')) {
        closeModal('personDetailModal');
        showNotification('删除成功', 'success');
      }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
      // 创建通知元素
      const notification = document.createElement('div');
      notification.className = `notification notification-${type}`;
      notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : type === 'warning' ? 'exclamation-triangle' : 'info-circle'}"></i>
        <span>${message}</span>
      `;

      // 添加到页面
      document.body.appendChild(notification);

      // 显示动画
      setTimeout(() => notification.classList.add('show'), 100);

      // 自动移除
      setTimeout(() => {
        notification.classList.remove('show');
        setTimeout(() => document.body.removeChild(notification), 300);
      }, 3000);
    }
  </script>
</body>
</html>
