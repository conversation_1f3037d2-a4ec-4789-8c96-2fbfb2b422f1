# 百度地图配置说明

## 概述

本项目的地图监控页面使用百度地图API来展示广州区域的智慧楼宇监控信息。为了正常使用地图功能，需要申请百度地图API密钥。

## 申请百度地图API密钥

### 1. 注册百度开发者账号

1. 访问百度地图开放平台：https://lbsyun.baidu.com/
2. 点击"控制台"，使用百度账号登录
3. 如果没有百度账号，需要先注册一个

### 2. 创建应用

1. 登录后进入控制台
2. 点击"创建应用"
3. 填写应用信息：
   - **应用名称**：智慧楼宇监控系统
   - **应用类型**：浏览器端
   - **启用服务**：勾选"地图JavaScript API v3.0"
   - **Referer白名单**：
     - 开发环境：`http://localhost:*/*`
     - 生产环境：`https://yourdomain.com/*`（替换为实际域名）

### 3. 获取API密钥

1. 创建应用后，系统会生成一个AK（API Key）
2. 复制这个AK，后续配置时需要用到

## 配置API密钥

### 方法1：直接修改HTML文件

打开 `map-monitoring.html` 文件，找到以下行：

```html
<script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=YOUR_BAIDU_MAP_AK"></script>
```

将 `YOUR_BAIDU_MAP_AK` 替换为您申请到的实际API密钥：

```html
<script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=您的实际API密钥"></script>
```

### 方法2：使用环境变量（推荐）

为了安全起见，建议使用环境变量来管理API密钥：

1. 创建一个配置文件 `config.js`：

```javascript
// config.js
const CONFIG = {
  BAIDU_MAP_AK: process.env.BAIDU_MAP_AK || 'YOUR_BAIDU_MAP_AK'
};
```

2. 在HTML中引入配置文件：

```html
<script src="config.js"></script>
<script type="text/javascript" src="https://api.map.baidu.com/api?v=3.0&ak=" + CONFIG.BAIDU_MAP_AK></script>
```

## 功能特性

### 地图显示
- **地图中心**：广州市天河CBD（经度：113.324520，纬度：23.150533）
- **默认缩放级别**：13级
- **地图样式**：简洁风格，突出监控信息

### 监控点类型
1. **视频监控**（📹）：显示视频监控设备位置和状态
2. **门禁设备**（🚪）：显示门禁控制设备位置和状态
3. **告警事件**（⚠️）：显示告警事件位置和级别
4. **人员位置**（👤）：显示人员实时位置
5. **车辆轨迹**（🚗）：显示车辆位置和轨迹

### 状态标识
- **绿色**：设备在线正常
- **橙色**：设备告警状态
- **红色**：设备离线或报警

### 交互功能
- **点击标记**：显示设备详细信息
- **图层控制**：可以开启/关闭不同类型的监控点显示
- **地图控制**：放大、缩小、定位、测距等功能
- **数据刷新**：实时刷新监控数据

## 示例数据

系统预置了广州地区的示例监控数据：

### 视频监控点
- 天河CBD监控点1（113.324520, 23.150533）
- 珠江新城监控点2（113.334520, 23.120533）
- 体育西路监控点3（113.314520, 23.140533）
- 天河北路监控点4（113.344520, 23.160533）
- 林和西路监控点5（113.354520, 23.135533）
- 花城大道监控点6（113.364520, 23.125533）

### 门禁设备
- 主楼入口门禁（113.314520, 23.130533）
- 停车场门禁（113.304520, 23.125533）
- 后门门禁（113.324520, 23.135533）
- 地下室门禁（113.334520, 23.145533）

### 告警事件
- 火灾报警（113.344520, 23.135533）- 高级别
- 入侵检测（113.354520, 23.145533）- 中级别
- 设备故障（113.364520, 23.155533）- 低级别

## 自定义配置

### 修改地图中心点

如果需要修改地图的默认中心点，可以在 `initBaiduMap()` 函数中修改：

```javascript
// 设置新的中心点坐标
const centerPoint = new BMap.Point(经度, 纬度);
baiduMap.centerAndZoom(centerPoint, 缩放级别);
```

### 添加新的监控点

在 `loadMapData()` 函数中添加新的监控点数据：

```javascript
const newDevice = {
  id: '设备ID',
  name: '设备名称',
  lng: 经度,
  lat: 纬度,
  status: '状态', // online, offline, warning, alarm
  type: '类型'    // video, access, alarm, personnel, vehicle
};
```

### 自定义图标

可以在 `createMarker()` 函数中自定义不同类型设备的图标样式。

## 注意事项

1. **API配额**：百度地图API有使用配额限制，请根据实际需求选择合适的套餐
2. **域名限制**：确保在百度地图控制台中正确配置了Referer白名单
3. **HTTPS要求**：生产环境建议使用HTTPS协议
4. **性能优化**：大量标记点时建议使用聚合显示功能
5. **数据更新**：实际使用时需要连接真实的数据源

## 故障排除

### 地图无法加载
1. 检查API密钥是否正确配置
2. 检查网络连接是否正常
3. 检查浏览器控制台是否有错误信息
4. 确认域名是否在白名单中

### 标记点不显示
1. 检查坐标数据是否正确
2. 确认图层是否已开启
3. 检查数据格式是否符合要求

### 信息窗口显示异常
1. 检查HTML内容是否正确
2. 确认CSS样式是否冲突
3. 检查JavaScript是否有语法错误

## 技术支持

如果在使用过程中遇到问题，可以：

1. 查看百度地图API官方文档：https://lbsyun.baidu.com/cms/jsapi/reference/jsapi_reference_3_0.html
2. 访问百度地图开发者社区：https://lbsyun.baidu.com/forum
3. 联系技术支持团队

## 更新日志

- **v1.0.0**：初始版本，支持基础地图显示和监控点标记
- **v1.1.0**：添加图层控制和信息窗口功能
- **v1.2.0**：优化地图样式和交互体验
