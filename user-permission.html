<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>用户与权限管理 - 广州农行智慧楼宇</title>
  
  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- 引入主样式 -->
  <link rel="stylesheet" href="assets/css/main.css">
  
  <style>
    .user-management-layout {
      display: grid;
      grid-template-columns: 300px 1fr;
      gap: var(--spacing-lg);
    }
    
    .user-sidebar {
      background: white;
      border-radius: var(--radius-lg);
      border: 1px solid var(--gray-200);
      padding: var(--spacing-lg);
    }
    
    .user-main {
      background: white;
      border-radius: var(--radius-lg);
      border: 1px solid var(--gray-200);
    }
    
    .org-tree {
      margin-bottom: var(--spacing-lg);
    }
    
    .org-node {
      margin-bottom: var(--spacing-sm);
    }
    
    .org-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-sm);
      border-radius: var(--radius-sm);
      cursor: pointer;
      transition: background 0.3s ease;
      font-size: 14px;
    }
    
    .org-item:hover {
      background: var(--gray-50);
    }
    
    .org-item.active {
      background: var(--primary-color);
      color: white;
    }
    
    .org-icon {
      width: 16px;
      margin-right: var(--spacing-sm);
      color: var(--gray-500);
    }
    
    .org-item.active .org-icon {
      color: white;
    }
    
    .org-children {
      margin-left: var(--spacing-lg);
      margin-top: var(--spacing-xs);
    }
    
    .user-table-container {
      overflow-x: auto;
    }
    
    .user-avatar {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--primary-color);
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 500;
      font-size: 12px;
      margin-right: var(--spacing-sm);
    }
    
    .user-info {
      display: flex;
      align-items: center;
    }
    
    .user-details {
      display: flex;
      flex-direction: column;
    }
    
    .user-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--gray-900);
    }
    
    .user-email {
      font-size: 12px;
      color: var(--gray-500);
    }
    
    .role-tags {
      display: flex;
      gap: var(--spacing-xs);
      flex-wrap: wrap;
    }
    
    .role-tag {
      padding: 2px 8px;
      background: var(--primary-color);
      color: white;
      border-radius: 12px;
      font-size: 11px;
      font-weight: 500;
    }
    
    .role-tag.admin {
      background: var(--danger-color);
    }
    
    .role-tag.manager {
      background: var(--warning-color);
    }
    
    .role-tag.user {
      background: var(--success-color);
    }
    
    .permission-matrix {
      display: grid;
      grid-template-columns: 200px repeat(4, 1fr);
      gap: 1px;
      background: var(--gray-200);
      border-radius: var(--radius-md);
      overflow: hidden;
    }
    
    .permission-header {
      background: var(--gray-100);
      padding: var(--spacing-sm);
      font-size: 12px;
      font-weight: 600;
      color: var(--gray-700);
      text-align: center;
    }
    
    .permission-row {
      background: white;
      padding: var(--spacing-sm);
      font-size: 13px;
      color: var(--gray-700);
      display: flex;
      align-items: center;
    }
    
    .permission-cell {
      background: white;
      padding: var(--spacing-sm);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .permission-checkbox {
      width: 16px;
      height: 16px;
    }
    
    @media (max-width: 1024px) {
      .user-management-layout {
        grid-template-columns: 1fr;
        grid-template-rows: auto 1fr;
      }
      
      .user-sidebar {
        max-height: 300px;
        overflow-y: auto;
      }
    }
  </style>
</head>
<body>
  <div class="app-container">
    <!-- 侧边栏 -->
    <div id="sidebarContainer"></div>

    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 头部导航 -->
      <div id="headerContainer"></div>

      <!-- 页面内容 -->
      <div class="page-content">
        <!-- 页面标题 -->
        <div class="page-header">
          <h1 class="page-title">用户与权限管理</h1>
          <p class="page-description">用户账号管理、权限分配、组织架构管理、角色配置</p>
        </div>

        <!-- 统计信息 -->
        <div class="stats-grid mb-lg">
          <div class="stat-card primary">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">156</div>
                <div class="stat-label">总用户数</div>
              </div>
              <div class="stat-icon primary">
                <i class="fas fa-users"></i>
              </div>
            </div>
          </div>

          <div class="stat-card success">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">142</div>
                <div class="stat-label">活跃用户</div>
              </div>
              <div class="stat-icon success">
                <i class="fas fa-user-check"></i>
              </div>
            </div>
          </div>

          <div class="stat-card info">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">8</div>
                <div class="stat-label">角色数量</div>
              </div>
              <div class="stat-icon info">
                <i class="fas fa-user-tag"></i>
              </div>
            </div>
          </div>

          <div class="stat-card warning">
            <div class="stat-header">
              <div class="stat-content">
                <div class="stat-value">14</div>
                <div class="stat-label">待审核</div>
              </div>
              <div class="stat-icon warning">
                <i class="fas fa-clock"></i>
              </div>
            </div>
          </div>
        </div>

        <!-- 用户管理主界面 -->
        <div class="user-management-layout">
          <!-- 组织架构 -->
          <div class="user-sidebar">
            <div class="d-flex justify-between items-center mb-md">
              <h3 style="margin: 0; font-size: 16px; font-weight: 600;">组织架构</h3>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i>
              </button>
            </div>
            
            <div class="org-tree">
              <div class="org-node">
                <div class="org-item active">
                  <i class="fas fa-building org-icon"></i>
                  <span>智慧楼宇管理公司</span>
                </div>
                <div class="org-children">
                  <div class="org-item">
                    <i class="fas fa-users org-icon"></i>
                    <span>管理部门</span>
                  </div>
                  <div class="org-item">
                    <i class="fas fa-tools org-icon"></i>
                    <span>运维部门</span>
                  </div>
                  <div class="org-item">
                    <i class="fas fa-shield-alt org-icon"></i>
                    <span>安保部门</span>
                  </div>
                  <div class="org-item">
                    <i class="fas fa-cogs org-icon"></i>
                    <span>技术部门</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="d-flex justify-between items-center mb-md">
              <h3 style="margin: 0; font-size: 16px; font-weight: 600;">角色管理</h3>
              <button class="btn btn-sm btn-primary">
                <i class="fas fa-plus"></i>
              </button>
            </div>
            
            <div class="org-tree">
              <div class="org-item">
                <i class="fas fa-crown org-icon"></i>
                <span>超级管理员</span>
              </div>
              <div class="org-item">
                <i class="fas fa-user-tie org-icon"></i>
                <span>系统管理员</span>
              </div>
              <div class="org-item">
                <i class="fas fa-user-cog org-icon"></i>
                <span>运维管理员</span>
              </div>
              <div class="org-item">
                <i class="fas fa-user-shield org-icon"></i>
                <span>安保管理员</span>
              </div>
              <div class="org-item">
                <i class="fas fa-user org-icon"></i>
                <span>普通用户</span>
              </div>
            </div>
          </div>

          <!-- 用户列表 -->
          <div class="user-main">
            <div class="card-header">
              <h3 class="card-title">用户列表</h3>
              <div class="d-flex gap-sm">
                <button class="btn btn-sm btn-secondary">
                  <i class="fas fa-download"></i>
                  导出
                </button>
                <button class="btn btn-sm btn-primary">
                  <i class="fas fa-user-plus"></i>
                  新增用户
                </button>
              </div>
            </div>
            
            <div class="card-body">
              <!-- 搜索筛选 -->
              <div class="form-row mb-lg">
                <div class="form-group">
                  <input type="text" class="form-control" placeholder="搜索用户名、邮箱...">
                </div>
                <div class="form-group">
                  <select class="form-control form-select">
                    <option>全部部门</option>
                    <option>管理部门</option>
                    <option>运维部门</option>
                    <option>安保部门</option>
                    <option>技术部门</option>
                  </select>
                </div>
                <div class="form-group">
                  <select class="form-control form-select">
                    <option>全部角色</option>
                    <option>超级管理员</option>
                    <option>系统管理员</option>
                    <option>运维管理员</option>
                    <option>普通用户</option>
                  </select>
                </div>
                <div class="form-group">
                  <button class="btn btn-primary">
                    <i class="fas fa-search"></i>
                    搜索
                  </button>
                </div>
              </div>

              <!-- 用户表格 -->
              <div class="user-table-container">
                <table class="table">
                  <thead>
                    <tr>
                      <th>用户信息</th>
                      <th>部门</th>
                      <th>角色</th>
                      <th>状态</th>
                      <th>最后登录</th>
                      <th>操作</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>
                        <div class="user-info">
                          <div class="user-avatar">张</div>
                          <div class="user-details">
                            <div class="user-name">张三</div>
                            <div class="user-email"><EMAIL></div>
                          </div>
                        </div>
                      </td>
                      <td>管理部门</td>
                      <td>
                        <div class="role-tags">
                          <span class="role-tag admin">超级管理员</span>
                        </div>
                      </td>
                      <td><span class="status-badge success">正常</span></td>
                      <td>2024-01-15 14:30</td>
                      <td>
                        <div class="d-flex gap-sm">
                          <button class="btn btn-sm btn-secondary">编辑</button>
                          <button class="btn btn-sm btn-warning">重置密码</button>
                        </div>
                      </td>
                    </tr>
                    
                    <tr>
                      <td>
                        <div class="user-info">
                          <div class="user-avatar">李</div>
                          <div class="user-details">
                            <div class="user-name">李四</div>
                            <div class="user-email"><EMAIL></div>
                          </div>
                        </div>
                      </td>
                      <td>运维部门</td>
                      <td>
                        <div class="role-tags">
                          <span class="role-tag manager">运维管理员</span>
                        </div>
                      </td>
                      <td><span class="status-badge success">正常</span></td>
                      <td>2024-01-15 13:45</td>
                      <td>
                        <div class="d-flex gap-sm">
                          <button class="btn btn-sm btn-secondary">编辑</button>
                          <button class="btn btn-sm btn-warning">重置密码</button>
                        </div>
                      </td>
                    </tr>
                    
                    <tr>
                      <td>
                        <div class="user-info">
                          <div class="user-avatar">王</div>
                          <div class="user-details">
                            <div class="user-name">王五</div>
                            <div class="user-email"><EMAIL></div>
                          </div>
                        </div>
                      </td>
                      <td>安保部门</td>
                      <td>
                        <div class="role-tags">
                          <span class="role-tag user">普通用户</span>
                        </div>
                      </td>
                      <td><span class="status-badge warning">禁用</span></td>
                      <td>2024-01-14 16:20</td>
                      <td>
                        <div class="d-flex gap-sm">
                          <button class="btn btn-sm btn-secondary">编辑</button>
                          <button class="btn btn-sm btn-success">启用</button>
                        </div>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        <!-- 权限配置 -->
        <div class="card mt-lg">
          <div class="card-header">
            <h3 class="card-title">权限配置矩阵</h3>
            <div class="d-flex gap-sm">
              <select class="form-control form-select" style="width: 200px;">
                <option>超级管理员</option>
                <option>系统管理员</option>
                <option>运维管理员</option>
                <option>安保管理员</option>
                <option>普通用户</option>
              </select>
              <button class="btn btn-primary">保存权限</button>
            </div>
          </div>
          <div class="card-body">
            <div class="permission-matrix">
              <div class="permission-header">功能模块</div>
              <div class="permission-header">查看</div>
              <div class="permission-header">新增</div>
              <div class="permission-header">编辑</div>
              <div class="permission-header">删除</div>
              
              <div class="permission-row">地图监控中心</div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox"></div>
              
              <div class="permission-row">事件与联动</div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox"></div>
              
              <div class="permission-row">视频监控系统</div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox"></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox"></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox"></div>
              
              <div class="permission-row">门禁与通行控制</div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox"></div>
              
              <div class="permission-row">用户与权限管理</div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
              <div class="permission-cell"><input type="checkbox" class="permission-checkbox" checked></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 引入主JavaScript -->
  <script src="assets/js/main.js"></script>
  
  <script>
    // 页面初始化
    document.addEventListener('DOMContentLoaded', function() {
      // 加载组件
      loadComponent('sidebarContainer', 'components/sidebar.html');
      loadComponent('headerContainer', 'components/header.html');
      
      // 更新面包屑
      setTimeout(() => {
        if (typeof updateBreadcrumb === 'function') {
          updateBreadcrumb('用户与权限管理');
        }
      }, 100);
    });

    // 加载组件
    function loadComponent(containerId, componentPath) {
      fetch(componentPath)
        .then(response => response.text())
        .then(html => {
          document.getElementById(containerId).innerHTML = html;
        })
        .catch(error => {
          console.error('Error loading component:', error);
        });
    }
  </script>
</body>
</html>
