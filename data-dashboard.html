<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>广州农行智慧楼宇大屏 - 数据可视化中心</title>
  
  <!-- 引入字体 -->
  <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  
  <!-- 引入Font Awesome图标 -->
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  
  <!-- 引入Chart.js -->
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  
  <!-- 引入Three.js用于3D效果 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
  
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: 'Inter', sans-serif;
      background: linear-gradient(135deg, #0a1628 0%, #1a2332 50%, #0f1419 100%);
      color: #ffffff;
      overflow: hidden;
      height: 100vh;
    }

    /* 背景动画效果 */
    .bg-animation {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: -1;
      opacity: 0.3;
    }

    .bg-particles {
      position: absolute;
      width: 2px;
      height: 2px;
      background: #00d4ff;
      border-radius: 50%;
      animation: float 6s ease-in-out infinite;
    }

    @keyframes float {
      0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 1; }
      50% { transform: translateY(-20px) rotate(180deg); opacity: 0.5; }
    }

    /* 主容器 */
    .dashboard-container {
      display: grid;
      grid-template-columns: 1fr 2fr 1fr;
      grid-template-rows: auto 1fr auto;
      height: 100vh;
      gap: 20px;
      padding: 20px;
    }

    /* 顶部标题区域 */
    .header-section {
      grid-column: 1 / -1;
      text-align: center;
      position: relative;
    }

    .main-title {
      font-family: 'Orbitron', monospace;
      font-size: 48px;
      font-weight: 900;
      background: linear-gradient(45deg, #00d4ff, #0099cc, #ffffff);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      text-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
      margin-bottom: 10px;
      letter-spacing: 3px;
    }

    .subtitle {
      font-size: 18px;
      color: #7dd3fc;
      font-weight: 300;
      letter-spacing: 2px;
    }

    /* 左侧面板 */
    .left-panel {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* 右侧面板 */
    .right-panel {
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    /* 中央3D建筑区域 */
    .center-building {
      position: relative;
      background: radial-gradient(circle at center, rgba(0, 212, 255, 0.1) 0%, transparent 70%);
      border: 1px solid rgba(0, 212, 255, 0.3);
      border-radius: 20px;
      overflow: hidden;
    }

    .building-3d {
      width: 100%;
      height: 100%;
      position: relative;
      background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 600"><defs><linearGradient id="buildingGrad" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%2300d4ff;stop-opacity:0.3"/><stop offset="100%" style="stop-color:%230099cc;stop-opacity:0.1"/></linearGradient></defs><rect x="300" y="150" width="200" height="300" fill="url(%23buildingGrad)" stroke="%2300d4ff" stroke-width="2"/><rect x="320" y="170" width="20" height="20" fill="%2300d4ff" opacity="0.8"/><rect x="350" y="170" width="20" height="20" fill="%2300d4ff" opacity="0.6"/><rect x="380" y="170" width="20" height="20" fill="%2300d4ff" opacity="0.8"/><rect x="410" y="170" width="20" height="20" fill="%2300d4ff" opacity="0.4"/></svg>') center/contain no-repeat;
    }

    .building-info {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
      background: rgba(0, 20, 40, 0.8);
      padding: 20px;
      border-radius: 15px;
      border: 1px solid rgba(0, 212, 255, 0.5);
      backdrop-filter: blur(10px);
    }

    .building-stats {
      display: flex;
      justify-content: space-around;
      margin-top: 15px;
    }

    .building-stat {
      text-align: center;
    }

    .building-stat-value {
      font-family: 'Orbitron', monospace;
      font-size: 24px;
      font-weight: 700;
      color: #00d4ff;
    }

    .building-stat-label {
      font-size: 12px;
      color: #7dd3fc;
      margin-top: 5px;
    }

    /* 数据卡片样式 */
    .data-card {
      background: linear-gradient(135deg, rgba(0, 20, 40, 0.8) 0%, rgba(10, 30, 50, 0.6) 100%);
      border: 1px solid rgba(0, 212, 255, 0.3);
      border-radius: 15px;
      padding: 20px;
      backdrop-filter: blur(10px);
      position: relative;
      overflow: hidden;
    }

    .data-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00d4ff, transparent);
      animation: scan 3s linear infinite;
    }

    @keyframes scan {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    .card-title {
      font-family: 'Orbitron', monospace;
      font-size: 16px;
      font-weight: 700;
      color: #00d4ff;
      margin-bottom: 15px;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .card-title i {
      font-size: 20px;
    }

    /* 统计数字样式 */
    .stat-number {
      font-family: 'Orbitron', monospace;
      font-size: 36px;
      font-weight: 900;
      color: #00d4ff;
      text-shadow: 0 0 20px rgba(0, 212, 255, 0.5);
      margin-bottom: 5px;
    }

    .stat-label {
      font-size: 14px;
      color: #7dd3fc;
      margin-bottom: 10px;
    }

    .stat-change {
      font-size: 12px;
      display: flex;
      align-items: center;
      gap: 5px;
    }

    .stat-change.positive {
      color: #10b981;
    }

    .stat-change.negative {
      color: #ef4444;
    }

    /* 图表容器 */
    .chart-container {
      height: 200px;
      position: relative;
    }

    .chart-container canvas {
      border-radius: 10px;
    }

    /* 底部控制区域 */
    .bottom-controls {
      grid-column: 1 / -1;
      display: flex;
      justify-content: center;
      gap: 20px;
    }

    .control-btn {
      background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 153, 204, 0.3));
      border: 1px solid rgba(0, 212, 255, 0.5);
      color: #ffffff;
      padding: 12px 24px;
      border-radius: 25px;
      font-family: 'Orbitron', monospace;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;
      backdrop-filter: blur(10px);
    }

    .control-btn:hover {
      background: linear-gradient(135deg, rgba(0, 212, 255, 0.4), rgba(0, 153, 204, 0.5));
      box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
      transform: translateY(-2px);
    }

    .control-btn.active {
      background: linear-gradient(135deg, #00d4ff, #0099cc);
      box-shadow: 0 0 30px rgba(0, 212, 255, 0.5);
    }

    /* 实时数据流动效果 */
    .data-flow {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 2px;
      background: linear-gradient(90deg, transparent, #00d4ff, transparent);
      animation: dataFlow 2s linear infinite;
    }

    @keyframes dataFlow {
      0% { transform: translateX(-100%); }
      100% { transform: translateX(100%); }
    }

    /* 响应式设计 */
    @media (max-width: 1920px) {
      .main-title { font-size: 42px; }
    }

    @media (max-width: 1600px) {
      .main-title { font-size: 36px; }
      .dashboard-container { gap: 15px; padding: 15px; }
    }

    @media (max-width: 1200px) {
      .dashboard-container {
        grid-template-columns: 1fr;
        grid-template-rows: auto auto 1fr auto;
      }
      .left-panel, .right-panel {
        flex-direction: row;
        overflow-x: auto;
      }
    }
  </style>
</head>
<body>
  <!-- 背景动画 -->
  <div class="bg-animation" id="bgAnimation"></div>

  <div class="dashboard-container">
    <!-- 顶部标题 -->
    <div class="header-section">
      <h1 class="main-title">广州农行智慧楼宇大屏</h1>
      <p class="subtitle">GUANGZHOU ABC SMART BUILDING DASHBOARD</p>
    </div>

    <!-- 左侧面板 -->
    <div class="left-panel">
      <!-- 数据统计 -->
      <div class="data-card">
        <div class="card-title">
          <i class="fas fa-chart-line"></i>
          数据统计
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
          <div>
            <div class="stat-number" id="totalDevices">998</div>
            <div class="stat-label">设备总数</div>
            <div class="stat-change positive">
              <i class="fas fa-arrow-up"></i>
              <span>较昨日 +2.3%</span>
            </div>
          </div>
          <div>
            <div class="stat-number" id="onlineDevices">3,124</div>
            <div class="stat-label">在线设备数量</div>
            <div class="stat-change positive">
              <i class="fas fa-arrow-up"></i>
              <span>较昨日 +1.8%</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统监控 -->
      <div class="data-card">
        <div class="card-title">
          <i class="fas fa-desktop"></i>
          系统监控
        </div>
        <div class="chart-container">
          <canvas id="systemChart"></canvas>
        </div>
      </div>

      <!-- 出入口管理 -->
      <div class="data-card">
        <div class="card-title">
          <i class="fas fa-door-open"></i>
          出入口管理
        </div>
        <div class="chart-container">
          <canvas id="accessChart"></canvas>
        </div>
      </div>
    </div>

    <!-- 中央3D建筑区域 -->
    <div class="center-building">
      <div class="building-3d">
        <div class="building-info">
          <h3 style="color: #00d4ff; font-family: 'Orbitron', monospace; margin-bottom: 15px;">广州农行大厦</h3>
          <div class="building-stats">
            <div class="building-stat">
              <div class="building-stat-value">18</div>
              <div class="building-stat-label">楼层</div>
            </div>
            <div class="building-stat">
              <div class="building-stat-value">500</div>
              <div class="building-stat-label">设备总数</div>
            </div>
            <div class="building-stat">
              <div class="building-stat-value">460</div>
              <div class="building-stat-label">在线设备</div>
            </div>
          </div>
          <div style="margin-top: 20px; padding: 10px; background: rgba(0, 212, 255, 0.1); border-radius: 8px;">
            <div style="font-size: 14px; color: #7dd3fc;">实时状态</div>
            <div style="font-size: 18px; color: #10b981; font-weight: 600;">系统运行正常</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右侧面板 -->
    <div class="right-panel">
      <!-- 设备监控 -->
      <div class="data-card">
        <div class="card-title">
          <i class="fas fa-cog"></i>
          设备监控
        </div>
        <div style="text-align: center; margin-bottom: 20px;">
          <div class="stat-number" style="font-size: 48px;">500</div>
          <div class="stat-label">设备总数</div>
        </div>
        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; font-size: 14px;">
          <div style="padding: 8px; background: rgba(16, 185, 129, 0.2); border-radius: 8px; text-align: center;">
            <div style="color: #10b981; font-weight: 600;">346</div>
            <div style="color: #7dd3fc;">正常运行</div>
          </div>
          <div style="padding: 8px; background: rgba(239, 68, 68, 0.2); border-radius: 8px; text-align: center;">
            <div style="color: #ef4444; font-weight: 600;">137</div>
            <div style="color: #7dd3fc;">离线设备</div>
          </div>
        </div>
      </div>

      <!-- 告警统计 -->
      <div class="data-card">
        <div class="card-title">
          <i class="fas fa-exclamation-triangle"></i>
          告警统计
        </div>
        <div class="chart-container">
          <canvas id="alertChart"></canvas>
        </div>
      </div>

      <!-- 事件统计 -->
      <div class="data-card">
        <div class="card-title">
          <i class="fas fa-calendar-alt"></i>
          事件统计
        </div>
        <div class="chart-container">
          <canvas id="eventChart"></canvas>
        </div>
      </div>
    </div>

    <!-- 底部控制区域 -->
    <div class="bottom-controls">
      <button class="control-btn active" onclick="switchView('overview')">
        <i class="fas fa-home"></i> 总览视图
      </button>
      <button class="control-btn" onclick="switchView('building')">
        <i class="fas fa-building"></i> 楼宇管理
      </button>
      <button class="control-btn" onclick="switchView('security')">
        <i class="fas fa-shield-alt"></i> 安防监控
      </button>
      <button class="control-btn" onclick="switchView('energy')">
        <i class="fas fa-bolt"></i> 能源管理
      </button>
      <button class="control-btn" onclick="switchView('maintenance')">
        <i class="fas fa-tools"></i> 维保管理
      </button>
    </div>
  </div>

  <script>
    // 背景粒子动画
    function createParticles() {
      const container = document.getElementById('bgAnimation');
      for (let i = 0; i < 50; i++) {
        const particle = document.createElement('div');
        particle.className = 'bg-particles';
        particle.style.left = Math.random() * 100 + '%';
        particle.style.top = Math.random() * 100 + '%';
        particle.style.animationDelay = Math.random() * 6 + 's';
        particle.style.animationDuration = (Math.random() * 4 + 4) + 's';
        container.appendChild(particle);
      }
    }

    // 数据更新动画
    function animateNumber(element, start, end, duration) {
      const startTime = performance.now();
      const animate = (currentTime) => {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);
        const current = Math.floor(start + (end - start) * progress);
        element.textContent = current.toLocaleString();
        if (progress < 1) {
          requestAnimationFrame(animate);
        }
      };
      requestAnimationFrame(animate);
    }

    // 初始化图表
    function initCharts() {
      // 系统监控图表
      const systemCtx = document.getElementById('systemChart').getContext('2d');
      new Chart(systemCtx, {
        type: 'line',
        data: {
          labels: ['7-18', '7-19', '7-20', '7-21', '7-22', '7-23', '7-24'],
          datasets: [{
            label: '系统负载',
            data: [65, 59, 80, 81, 56, 55, 40],
            borderColor: '#00d4ff',
            backgroundColor: 'rgba(0, 212, 255, 0.1)',
            tension: 0.4,
            fill: true
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            x: {
              grid: { color: 'rgba(0, 212, 255, 0.2)' },
              ticks: { color: '#7dd3fc' }
            },
            y: {
              grid: { color: 'rgba(0, 212, 255, 0.2)' },
              ticks: { color: '#7dd3fc' }
            }
          }
        }
      });

      // 出入口管理图表
      const accessCtx = document.getElementById('accessChart').getContext('2d');
      new Chart(accessCtx, {
        type: 'bar',
        data: {
          labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
          datasets: [{
            label: '人员流量',
            data: [12, 19, 45, 67, 52, 23],
            backgroundColor: 'rgba(0, 212, 255, 0.6)',
            borderColor: '#00d4ff',
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            x: {
              grid: { color: 'rgba(0, 212, 255, 0.2)' },
              ticks: { color: '#7dd3fc' }
            },
            y: {
              grid: { color: 'rgba(0, 212, 255, 0.2)' },
              ticks: { color: '#7dd3fc' }
            }
          }
        }
      });

      // 告警统计图表
      const alertCtx = document.getElementById('alertChart').getContext('2d');
      new Chart(alertCtx, {
        type: 'doughnut',
        data: {
          labels: ['正常', '警告', '严重'],
          datasets: [{
            data: [85, 10, 5],
            backgroundColor: ['#10b981', '#f59e0b', '#ef4444'],
            borderWidth: 0
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: 'bottom',
              labels: { color: '#7dd3fc', font: { size: 12 } }
            }
          }
        }
      });

      // 事件统计图表
      const eventCtx = document.getElementById('eventChart').getContext('2d');
      new Chart(eventCtx, {
        type: 'radar',
        data: {
          labels: ['门禁', '监控', '消防', '环境', '设备'],
          datasets: [{
            label: '事件数量',
            data: [65, 59, 90, 81, 56],
            borderColor: '#00d4ff',
            backgroundColor: 'rgba(0, 212, 255, 0.2)',
            pointBackgroundColor: '#00d4ff'
          }]
        },
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: { display: false }
          },
          scales: {
            r: {
              grid: { color: 'rgba(0, 212, 255, 0.2)' },
              pointLabels: { color: '#7dd3fc', font: { size: 10 } },
              ticks: { color: '#7dd3fc', backdropColor: 'transparent' }
            }
          }
        }
      });
    }

    // 视图切换功能
    function switchView(view) {
      // 移除所有按钮的active类
      document.querySelectorAll('.control-btn').forEach(btn => {
        btn.classList.remove('active');
      });

      // 为当前按钮添加active类
      event.target.classList.add('active');

      // 这里可以添加视图切换逻辑
      console.log('切换到视图:', view);
    }

    // 实时数据更新
    function updateRealTimeData() {
      // 模拟实时数据更新
      const totalDevices = document.getElementById('totalDevices');
      const onlineDevices = document.getElementById('onlineDevices');

      // 随机更新数据
      const newTotal = 998 + Math.floor(Math.random() * 10 - 5);
      const newOnline = 3124 + Math.floor(Math.random() * 50 - 25);

      animateNumber(totalDevices, parseInt(totalDevices.textContent), newTotal, 1000);
      animateNumber(onlineDevices, parseInt(onlineDevices.textContent.replace(',', '')), newOnline, 1000);
    }

    // 页面加载完成后初始化
    document.addEventListener('DOMContentLoaded', function() {
      createParticles();
      initCharts();

      // 每30秒更新一次数据
      setInterval(updateRealTimeData, 30000);
    });
  </script>
</body>
</html>
